<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Promo Code API</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .result {
            background: #f5f5f5;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 3px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        input {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <h1>Promo Code API Test</h1>
    
    <div class="test-section">
        <h2>Test FREESHIP3 Promo Code</h2>
        <p>This will test the promo code validation API directly.</p>
        
        <div>
            <label>Promo Code: </label>
            <input type="text" id="promoCode" value="FREESHIP3" />
        </div>
        <div>
            <label>Subtotal: </label>
            <input type="number" id="subtotal" value="110" />
        </div>
        <div>
            <label>Token: </label>
            <input type="text" id="token" placeholder="JWT token (get from browser dev tools)" style="width: 400px;" />
        </div>
        
        <button onclick="testPromoCode()">Test Promo Code</button>
        
        <div id="result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>Expected Behavior for FREESHIP3</h2>
        <ul>
            <li><strong>discountAmount:</strong> Should be 0</li>
            <li><strong>isFreeDelivery:</strong> Should be true</li>
            <li><strong>type:</strong> Should be "free_delivery"</li>
            <li><strong>remainingUses:</strong> Should show remaining uses (1 or 2)</li>
        </ul>
    </div>

    <script>
        async function testPromoCode() {
            const promoCode = document.getElementById('promoCode').value;
            const subtotal = parseFloat(document.getElementById('subtotal').value);
            const token = document.getElementById('token').value;
            const resultDiv = document.getElementById('result');
            
            if (!token) {
                resultDiv.className = 'result error';
                resultDiv.textContent = 'Please provide a JWT token. You can get this from browser dev tools > Application > Cookies > token';
                return;
            }
            
            try {
                resultDiv.className = 'result';
                resultDiv.textContent = 'Testing...';
                
                const response = await fetch('http://localhost:3111/api/promo-codes/validate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({
                        code: promoCode,
                        subtotal: subtotal
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = 'API Response:\n' + JSON.stringify(data, null, 2);
                    
                    // Check if the response is correct for FREESHIP3
                    if (promoCode.toUpperCase() === 'FREESHIP3' && data.success) {
                        const checks = [];
                        checks.push(`✓ discountAmount: ${data.data.discountAmount} (expected: 0)`);
                        checks.push(`✓ isFreeDelivery: ${data.data.isFreeDelivery} (expected: true)`);
                        checks.push(`✓ type: ${data.data.type} (expected: free_delivery)`);
                        checks.push(`✓ remainingUses: ${data.data.remainingUses}`);
                        
                        resultDiv.textContent += '\n\nValidation Checks:\n' + checks.join('\n');
                        
                        if (data.data.discountAmount === 0 && data.data.isFreeDelivery === true && data.data.type === 'free_delivery') {
                            resultDiv.textContent += '\n\n✅ All checks passed! The API is working correctly.';
                        } else {
                            resultDiv.textContent += '\n\n❌ Some checks failed. There might be an issue.';
                        }
                    }
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = 'API Error:\n' + JSON.stringify(data, null, 2);
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = 'Network Error:\n' + error.message;
            }
        }
        
        // Auto-fill token from localStorage if available
        window.onload = function() {
            const savedToken = localStorage.getItem('authToken') || getCookie('token');
            if (savedToken) {
                document.getElementById('token').value = savedToken;
            }
        };
        
        function getCookie(name) {
            const value = `; ${document.cookie}`;
            const parts = value.split(`; ${name}=`);
            if (parts.length === 2) return parts.pop().split(';').shift();
        }
    </script>
</body>
</html>
