import mongoose from 'mongoose';
import { User } from './models/usermodel.js';
import { config } from 'dotenv';

// Load environment variables
config({ path: './config.env' });

async function testRoleBasedAuth() {
  try {
    // Connect to database
    await mongoose.connect(process.env.MONGO_URI);
    console.log('✅ Connected to MongoDB');

    // Test 1: Create users with different roles
    console.log('\n🧪 Test 1: Creating users with different roles...');
    
    // Clean up existing test users
    await User.deleteMany({ 
      email: { $in: ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'] }
    });

    const testUsers = [
      {
        name: 'Admin User',
        email: '<EMAIL>',
        phone: '+************',
        password: 'password123',
        role: 'admin',
        accountVerified: true
      },
      {
        name: 'Vendor User',
        email: '<EMAIL>',
        phone: '+************',
        password: 'password123',
        role: 'vendor',
        accountVerified: true
      },
      {
        name: 'Delivery Partner',
        email: '<EMAIL>',
        phone: '+************',
        password: 'password123',
        role: 'deliveryPartner',
        accountVerified: true
      },
      {
        name: 'Regular User',
        email: '<EMAIL>',
        phone: '+************',
        password: 'password123',
        role: 'user',
        accountVerified: true
      }
    ];

    const createdUsers = [];
    for (const userData of testUsers) {
      const user = await User.create(userData);
      createdUsers.push(user);
      console.log(`✅ Created ${user.role}: ${user.name} (${user.email})`);
    }

    // Test 2: Generate JWT tokens and verify role inclusion
    console.log('\n🧪 Test 2: Testing JWT token generation with roles...');
    
    for (const user of createdUsers) {
      const token = user.generateToken();
      console.log(`✅ Generated token for ${user.role}: ${user.name}`);
      console.log(`   Token preview: ${token.substring(0, 50)}...`);
      
      // Decode token to verify role is included
      const jwt = await import('jsonwebtoken');
      const decoded = jwt.default.verify(token, process.env.JWT_SECRET);
      console.log(`   Decoded payload: { id: ${decoded.id}, role: ${decoded.role} }`);
      
      if (decoded.role === user.role) {
        console.log(`   ✅ Role correctly included in token`);
      } else {
        console.log(`   ❌ Role mismatch in token`);
      }
    }

    // Test 3: Verify default role assignment
    console.log('\n🧪 Test 3: Testing default role assignment...');
    
    const defaultUser = await User.create({
      name: 'Default User',
      email: '<EMAIL>',
      phone: '+************',
      password: 'password123',
      accountVerified: true
      // No role specified - should default to 'user'
    });

    console.log(`✅ Created user without role: ${defaultUser.name}`);
    console.log(`   Default role assigned: ${defaultUser.role}`);
    
    if (defaultUser.role === 'user') {
      console.log(`   ✅ Default role assignment working correctly`);
    } else {
      console.log(`   ❌ Default role assignment failed`);
    }

    console.log('\n🎉 All tests completed successfully!');
    console.log('\n📋 Summary:');
    console.log(`   - Created ${createdUsers.length + 1} test users`);
    console.log(`   - Verified JWT token generation with roles`);
    console.log(`   - Confirmed default role assignment`);
    console.log('\n🔐 Test user credentials:');
    console.log('   Admin: <EMAIL> / password123');
    console.log('   Vendor: <EMAIL> / password123');
    console.log('   Delivery: <EMAIL> / password123');
    console.log('   User: <EMAIL> / password123');

  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n✅ Disconnected from MongoDB');
  }
}

// Run the test
testRoleBasedAuth();
