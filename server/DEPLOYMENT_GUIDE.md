# Vercel Deployment Guide

## Prerequisites

1. **MongoDB Atlas Account**: Since Vercel doesn't support local MongoDB, you need to set up MongoDB Atlas (cloud database)
2. **Vercel Account**: Sign up at [vercel.com](https://vercel.com)
3. **GitHub Repository**: Your code should be in a GitHub repository

## Step 1: Set up MongoDB Atlas

1. Go to [MongoDB Atlas](https://www.mongodb.com/cloud/atlas)
2. Create a free account and cluster
3. Create a database user with read/write permissions
4. Whitelist all IP addresses (0.0.0.0/0) for Vercel deployment
5. Get your connection string (it will look like: `mongodb+srv://username:<EMAIL>/dbname`)

## Step 2: Update Environment Variables

You'll need to set these environment variables in Vercel:

```
PORT=4000
FRONTEND_URL=https://your-frontend-app.vercel.app
MONGO_URL=mongodb+srv://username:<EMAIL>/dbname

TWILIO_ACCOUNT_SID=your_twilio_sid
TWILIO_AUTH_TOKEN=your_twilio_token
TWILIO_VERIFY_SERVICE_SID=your_verify_service_sid
TWILIO_PHONE_NUMBER=your_twilio_phone

SMTP_HOST=smtp.gmail.com
SMTP_SERVICE=gmail
SMTP_PORT=465
SMTP_MAIL=<EMAIL>
SMTP_PASS=your_app_password

COOKIE_EXPIRE=7
JWT_SECRET=your_jwt_secret
JWT_EXPIRE=7d
```

## Step 3: Deploy to Vercel

### Option A: Using Vercel CLI
1. Install Vercel CLI: `npm i -g vercel`
2. Navigate to your server directory: `cd server`
3. Run: `vercel`
4. Follow the prompts

### Option B: Using Vercel Dashboard
1. Go to [vercel.com/dashboard](https://vercel.com/dashboard)
2. Click "New Project"
3. Import your GitHub repository
4. Set the root directory to `server`
5. Add all environment variables in the Environment Variables section
6. Deploy

## Step 4: Update Frontend Configuration

After deployment, update your frontend to use the new server URL:
- Replace `http://localhost:4000` with your Vercel deployment URL
- Update CORS settings if needed

## Step 5: Test Your Deployment

1. Visit your Vercel deployment URL
2. Test the `/api/test` endpoint
3. Verify database connectivity
4. Test all API endpoints

## Important Notes

- Vercel functions have a 30-second timeout limit
- MongoDB Atlas is required (local MongoDB won't work)
- Environment variables must be set in Vercel dashboard
- Update CORS origins to include your frontend URL

## Troubleshooting

- Check Vercel function logs for errors
- Verify environment variables are set correctly
- Ensure MongoDB Atlas allows connections from all IPs
- Check that all dependencies are in package.json
