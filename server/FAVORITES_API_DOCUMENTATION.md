# Favorites API Documentation

## Overview

The Favorites API provides comprehensive functionality for managing user favorites in the Bandiwala application. Users can favorite vendors and menu items, with full CRUD operations, pagination, search, and analytics.

## Base URL
```
https://your-backend-domain.com/api/favorites
```

## Authentication

All endpoints require JWT authentication. Include the token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

## Endpoints

### 1. Get All User Favorites

**GET** `/api/favorites/user`

Retrieves all favorites for the authenticated user with pagination and search support.

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 20, max: 100)
- `sortBy` (optional): Sort field (default: 'createdAt')
- `sortOrder` (optional): 'asc' or 'desc' (default: 'desc')
- `search` (optional): Search term for item names, vendor names, or descriptions

**Response:**
```json
{
  "success": true,
  "message": "Favorites retrieved successfully",
  "data": [
    {
      "_id": "favorite_id",
      "userId": "user_id",
      "itemId": "vendor_123",
      "itemType": "vendor",
      "itemName": "<PERSON>havani Chat Bhandar",
      "description": "Authentic street food",
      "imageUrl": "https://example.com/image.jpg",
      "metadata": {
        "rating": 4.5,
        "category": "Street Food",
        "isAvailable": true
      },
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 50,
    "pages": 3,
    "hasNext": true,
    "hasPrev": false
  }
}
```

### 2. Get Favorite Vendors

**GET** `/api/favorites/vendors`

Retrieves only vendor favorites for the authenticated user.

**Query Parameters:** Same as above

**Response:** Same structure as above, but only vendor favorites

### 3. Get Favorite Menu Items

**GET** `/api/favorites/menu-items`

Retrieves only menu item favorites for the authenticated user.

**Query Parameters:** Same as above

**Response:** Same structure as above, but only menu item favorites with additional fields:
```json
{
  "vendorId": "vendor_123",
  "vendorName": "Jai Bhavani Chat Bhandar",
  "price": 99.99
}
```

### 4. Add to Favorites

**POST** `/api/favorites`

Adds an item to the user's favorites.

**Request Body:**
```json
{
  "itemId": "vendor_123",
  "itemType": "vendor", // "vendor" or "menuItem"
  "itemName": "Jai Bhavani Chat Bhandar",
  "description": "Authentic street food",
  "imageUrl": "https://example.com/image.jpg",
  "metadata": {
    "rating": 4.5,
    "category": "Street Food",
    "tags": ["street-food", "chat"],
    "isAvailable": true
  }
}
```

**For Menu Items, additional required fields:**
```json
{
  "vendorId": "vendor_123",
  "vendorName": "Jai Bhavani Chat Bhandar",
  "price": 99.99
}
```

**Response:**
```json
{
  "success": true,
  "message": "Item added to favorites successfully",
  "data": {
    "_id": "favorite_id",
    "userId": "user_id",
    "itemId": "vendor_123",
    "itemType": "vendor",
    "itemName": "Jai Bhavani Chat Bhandar",
    "createdAt": "2024-01-01T00:00:00.000Z"
  }
}
```

### 5. Remove from Favorites

**DELETE** `/api/favorites/:itemType/:itemId`

Removes an item from the user's favorites.

**URL Parameters:**
- `itemType`: "vendor" or "menuItem"
- `itemId`: The ID of the item to remove

**Response:**
```json
{
  "success": true,
  "message": "Item removed from favorites successfully",
  "data": {
    "itemId": "vendor_123",
    "itemType": "vendor"
  }
}
```

### 6. Toggle Favorite Status

**POST** `/api/favorites/toggle`

Toggles the favorite status of an item (adds if not favorited, removes if favorited).

**Request Body:** Same as Add to Favorites

**Response:**
```json
{
  "success": true,
  "message": "Item added to favorites", // or "Item removed from favorites"
  "data": {
    "isFavorite": true, // or false
    "favorite": { /* favorite object if added */ }
  }
}
```

### 7. Check Favorite Status

**GET** `/api/favorites/check/:itemType/:itemId`

Checks if a specific item is favorited by the user.

**URL Parameters:**
- `itemType`: "vendor" or "menuItem"
- `itemId`: The ID of the item to check

**Response:**
```json
{
  "success": true,
  "message": "Favorite status retrieved successfully",
  "data": {
    "isFavorite": true,
    "itemId": "vendor_123",
    "itemType": "vendor"
  }
}
```

### 8. Get Favorites Count

**GET** `/api/favorites/user/count`

Gets the count of favorites by type for the authenticated user.

**Response:**
```json
{
  "success": true,
  "message": "Favorites count retrieved successfully",
  "data": {
    "vendorCount": 15,
    "menuItemCount": 32,
    "totalCount": 47
  }
}
```

### 9. Clear All Favorites

**DELETE** `/api/favorites/user`

Removes all favorites for the authenticated user.

**Response:**
```json
{
  "success": true,
  "message": "Cleared 47 favorites successfully",
  "data": {
    "deletedCount": 47
  }
}
```

### 10. Get Favorite by ID

**GET** `/api/favorites/:id`

Gets a specific favorite by its ID.

**URL Parameters:**
- `id`: The MongoDB ObjectId of the favorite

**Response:**
```json
{
  "success": true,
  "message": "Favorite retrieved successfully",
  "data": {
    "_id": "favorite_id",
    "userId": "user_id",
    "itemId": "vendor_123",
    "itemType": "vendor",
    "itemName": "Jai Bhavani Chat Bhandar",
    "createdAt": "2024-01-01T00:00:00.000Z"
  }
}
```

### 11. Bulk Add Favorites

**POST** `/api/favorites/bulk`

Adds multiple favorites at once (useful for import functionality).

**Request Body:**
```json
{
  "favorites": [
    {
      "itemId": "vendor_123",
      "itemType": "vendor",
      "itemName": "Vendor 1"
    },
    {
      "itemId": "item_456",
      "itemType": "menuItem",
      "itemName": "Menu Item 1",
      "vendorId": "vendor_123",
      "vendorName": "Vendor 1",
      "price": 99.99
    }
  ]
}
```

**Response:**
```json
{
  "success": true,
  "message": "Successfully imported 2 favorites",
  "data": {
    "insertedCount": 2,
    "totalProvided": 2
  }
}
```

## Error Responses

All endpoints return consistent error responses:

```json
{
  "success": false,
  "message": "Error description"
}
```

**Common HTTP Status Codes:**
- `200`: Success
- `201`: Created
- `400`: Bad Request (validation errors)
- `401`: Unauthorized (invalid/missing token)
- `404`: Not Found
- `409`: Conflict (duplicate favorite)
- `500`: Internal Server Error

## Data Models

### Favorite Schema
```javascript
{
  userId: ObjectId,           // Reference to User
  itemId: String,             // ID of the favorited item
  itemType: String,           // "vendor" or "menuItem"
  itemName: String,           // Display name
  vendorId: String,           // Required for menuItem
  vendorName: String,         // Required for menuItem
  price: Number,              // Required for menuItem
  imageUrl: String,           // Optional image URL
  description: String,        // Optional description
  metadata: {
    rating: Number,           // Item rating
    category: String,         // Item category
    tags: [String],           // Item tags
    isAvailable: Boolean      // Availability status
  },
  createdAt: Date,            // Auto-generated
  updatedAt: Date             // Auto-generated
}
```

## Database Indexes

The following indexes are created for optimal performance:

1. `{ userId: 1, itemId: 1, itemType: 1 }` - Unique compound index
2. `{ userId: 1 }` - User favorites lookup
3. `{ userId: 1, itemType: 1 }` - Type-specific queries
4. `{ createdAt: -1 }` - Chronological sorting

## Rate Limiting

- **Standard endpoints**: 100 requests per minute per user
- **Bulk operations**: 10 requests per minute per user

## Best Practices

1. **Pagination**: Always use pagination for large datasets
2. **Search**: Use the search parameter for better user experience
3. **Caching**: Cache favorite status checks on the client side
4. **Bulk Operations**: Use bulk endpoints for importing/exporting
5. **Error Handling**: Always handle error responses gracefully

## Example Usage

### JavaScript/Fetch Example
```javascript
// Add to favorites
const response = await fetch('/api/favorites', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  },
  body: JSON.stringify({
    itemId: 'vendor_123',
    itemType: 'vendor',
    itemName: 'Jai Bhavani Chat Bhandar'
  })
});

const result = await response.json();
```

### cURL Example
```bash
# Check favorite status
curl -X GET \
  "https://your-api.com/api/favorites/check/vendor/vendor_123" \
  -H "Authorization: Bearer your-jwt-token"
```
