{"name": "server", "version": "1.0.0", "main": "index.js", "type": "module", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "import:data": "node sampledata/importData.js", "vercel-build": "echo 'Building for Vercel...'"}, "author": "", "license": "ISC", "description": "", "dependencies": {"axios": "^1.9.0", "bcrypt": "^6.0.0", "bcryptjs": "^3.0.2", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.15.0", "multer": "^2.0.0", "node-cron": "^4.1.0", "node-fetch": "^3.3.2", "nodemailer": "^7.0.3", "razorpay": "^2.9.6", "socket.io": "^4.8.1", "twilio": "^5.6.1"}}