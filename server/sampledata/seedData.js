import mongoose from 'mongoose';
import Vendor from '../models/Vendor.js';
import MenuItem from '../models/MenuItem.js';
import PromoCode from '../models/PromoCode.js';
import { User } from '../models/usermodel.js';
import { generateMenuItemSlug } from '../utils/slugUtils.js';

export const seedData = async () => {
  try {
    console.log('Starting data seeding...');

    // Create vendor users first - using User.create() to ensure password hashing
    const vendorUsersData = [
      {
        name: "<PERSON><PERSON><PERSON> Chat Owner",
        email: "j<PERSON><PERSON><PERSON>@bandiwala.com",
        phone: "+************",
        password: "vendor123",
        role: "vendor",
        accountVerified: true,
        isApproved: true, 
      },
      {
        name: "<PERSON><PERSON><PERSON><PERSON> Owner",
        email: "<EMAIL>",
        phone: "+************",
        password: "vendor123",
        role: "vendor",
        accountVerified: true,
        isApproved: true, 
      },
      {
        name: "<PERSON><PERSON><PERSON> Owner",
        email: "laxmi<PERSON><PERSON>@bandiwala.com",
        phone: "+************",
        password: "vendor123",
        role: "vendor",
        accountVerified: true,
        isApproved: true, 
      },
      {
        name: "Sangamesh Bhavani Owner",
        email: "<EMAIL>",
        phone: "+************",
        password: "vendor123",
        role: "vendor",
        accountVerified: true,
        isApproved: true,
      },
      {
        name: "Amma Hot Soups Owner",
        email: "<EMAIL>",
        phone: "+************",
        password: "vendor123",
        role: "vendor",
        accountVerified: true,
        isApproved: true,
      },
      {
        name: "Apsara Bandam Milk Owner",
        email: "<EMAIL>",
        phone: "+************",
        password: "vendor123",
        role: "vendor",
        accountVerified: true,
        isApproved: true,
      },
    ];

    // Create users one by one to ensure password hashing middleware runs
    const vendorUsers = [];
    for (const userData of vendorUsersData) {
      const user = await User.create(userData);
      vendorUsers.push(user);
    }

    console.log(`${vendorUsers.length} vendor users created`);

    // Create vendors with linked user accounts
    const vendors = await Vendor.insertMany([
      {
        name: "Jai Bhavani Chat Bhandar",
        description: "Your go-to for authentic Indian street food chats & pani puri.",
        slug: "bhavani-street-food",
        rating: 4.5,
        location: "https://maps.app.goo.gl/K4ukR2zXSH7QL1fb9?g_st=aw",
        phone: "+91 98765 43210",
        image: "/bandiwala-items-pics/vendors/jaibhavani.jpeg",
        deliveryTime: "20-30 min",
        deliveryFee: 20,
        minOrderValue: 0,
        isActive: true,
        userId: vendorUsers[0]._id, // Link to vendor user
      },
      {
        name: "BFC Chicken Pakodi Center",
        description: "Specializing in delicious chicken pakodis and a selection of other chicken and fish preparations.",
        slug: "BFC-Chicken-Pakodi-Center",
        rating: 4.3,
        location: "https://maps.app.goo.gl/nAyssp8JmJEgmha56?g_st=aw",
        phone: "+91 87654 32109",
        image: "/bandiwala-items-pics/vendors/bfc.jpeg",
        deliveryTime: "20-30 min",
        deliveryFee: 20,
        minOrderValue: 0,
        isActive: true,
        userId: vendorUsers[1]._id, // Link to vendor user
      },
      {
        name: "Rajahmundry vari Special Muntha Masala",
        description: "Crave local flavors? We offer a wide array of specialty bajjis and unique muntha masala dishes.",
        slug: "Rajahmundry-vari-Special-Muntha-Masala",
        rating: 4.7,
        location: "https://maps.app.goo.gl/SR28akQWeZr1hggk6?g_st=aw",
        phone: "+91 76543 21098",
        image: "/bandiwala-items-pics/vendors/rajamandri.jpeg",
        deliveryTime: "20-30 min",
        deliveryFee: 20,
        minOrderValue: 0,
        isActive: true,
        userId: vendorUsers[2]._id, // Link to vendor user
      },
      {
        name: "Sangamesh Bhavani Fast Food",
        description: "Serving up popular Chinese noodles, fried rice, and more for a quick and satisfying meal.",
        slug: "Sangamesh-Bhavani-Fast-Food",
        rating: 4.4,
        location: "https://maps.app.goo.gl/YyTZPdPyuhqs49kj6?g_st=aw",
        phone: "+91 65432 10987",
        image: "/bandiwala-items-pics/vendors/sangamesh.jpeg",
        deliveryTime: "20-30 min",
        deliveryFee: 20,
        minOrderValue: 0,
        isActive: true,
        userId: vendorUsers[3]._id, // Link to vendor user
      },
      {
        name: "Amma Hot Soups",
        description: "Do you eat or drink soups",
        slug: "amma-hot-soups",
        rating: 4.2,
        location: "shop no 4 kphb phase1",
        phone: "+91 98765 43214",
        image: "/bandiwala-items-pics/vendors/amma.jpeg",
        deliveryTime: "15-25 min",
        deliveryFee: 20,
        minOrderValue: 0,
        isActive: true,
        userId: vendorUsers[4]._id, // Link to vendor user
      },
      {
        name: "Apsara Bandam Milk",
        description: "Get ready to indulge in the rich, creamy goodness of Apsara Badam Milk",
        slug: "apsara-bandam-milk",
        rating: 4.6,
        location: "HIG-76, eSeva Ln, K P H B Phase 3",
        phone: "+91 98765 43215",
        image: "/bandiwala-items-pics/vendors/apsara.jpeg",
        deliveryTime: "10-20 min",
        deliveryFee: 20,
        minOrderValue: 0,
        isActive: true,
        userId: vendorUsers[5]._id, // Link to vendor user
      },
    ]);

    console.log(`${vendors.length} vendors created`);

    // Create menu items with proper vendor references
    const menuItemsData = [
      {
        "vendorId": vendors[0]._id,
        "itemCategory": "Papidi",
        "itemName": "Dahi Papdi",
        "slug": generateMenuItemSlug("Dahi Papdi"),
        "description": "Crispy papdi topped with yogurt, chutneys and spices",
        "image": "/bandiwala-items-pics/items/Jai Bhavani Chat/dahi papdi.jpg",
        "isAvailable": true,
        "subcategories": [
          {
            "title": "unit",
            "quantity": "unit",
            "price": 45
          }
        ]
      },
      {
        "vendorId": vendors[0]._id,
        "itemCategory": "Pani Puri",
        "itemName": "Pani Puri (6 pcs)",
        "slug": generateMenuItemSlug("Pani Puri (6 pcs)"),
        "description": "Crispy puris filled with spicy water and chutneys",
        "image": "/bandiwala-items-pics/items/Jai Bhavani Chat/pani puri.jpeg",
        "isAvailable": true,
        "subcategories": [
          {
            "title": "6 pcs",
            "quantity": "6 Pcs",
            "price": 25
          }
        ]
      },
      {
        "vendorId": vendors[0]._id,
        "itemCategory": "Pani Puri",
        "itemName": "Sevi Puri",
        "slug": generateMenuItemSlug("Sevi Puri"),
        "description": "Crispy puris topped with sev, chutneys and vegetables",
        "image": "/bandiwala-items-pics/items/Jai Bhavani Chat/sev puri.jpg",
        "isAvailable": true,
        "subcategories": [
          {
            "title": "7 Pcs",
            "quantity": "7 Pcs",
            "price": 45
          }
        ]
      },
      {
        "vendorId": vendors[0]._id,
        "itemCategory": "Pani Puri",
        "itemName": "Bhel Puri",
        "slug": generateMenuItemSlug("Bhel Puri"),
        "description": "Popular Mumbai street food with puffed rice and chutneys",
        "image": "/bandiwala-items-pics/items/Jai Bhavani Chat/bhel puri.jpeg",
        "isAvailable": true,
        "subcategories": [
          {
            "title": "7 Pcs",
            "quantity": "7 Pcs",
            "price": 40
          }
        ]
      },
      {
        "vendorId": vendors[0]._id,
        "itemCategory": "Pani Puri",
        "itemName": "Dahi Puri",
        "slug": generateMenuItemSlug("Dahi Puri"),
        "description": "Puris filled with yogurt, chutneys and spices",
        "image": "/bandiwala-items-pics/items/Jai Bhavani Chat/dahi puri.jpg",
        "isAvailable": true,
        "subcategories": [
          {
            "title": "7 Pcs",
            "quantity": "7 Pcs",
            "price": 40
          }
        ]
      },
      {
        "vendorId": vendors[0]._id,
        "itemCategory": "Pani Puri",
        "itemName": "Sweet Puri",
        "description": "Sweet and tangy puris with special chutneys",
        "isAvailable": true,
        "slug": generateMenuItemSlug("Sweet Puri"),
        "image": "/bandiwala-items-pics/items/Jai Bhavani Chat/sweet puri.jpg",
        "subcategories": [
          {
            "title": "7 Pcs",
            "quantity": "7 Pcs",
            "price": 40
          }
        ]
      },
      {
        "vendorId": vendors[0]._id,
        "itemCategory": "Pani Puri",
        "itemName": "Masala Puri",
        "description": "Spicy masala puris with aromatic spices",
        "isAvailable": true,
        "slug": generateMenuItemSlug("Masala Puri"),
        "image": "/bandiwala-items-pics/items/Jai Bhavani Chat/Masalapuri.jpg",
        "subcategories": [
          {
            "title": "7 Pcs",
            "quantity": "7 Pcs",
            "price": 40
          }
        ]
      },
      {
        "vendorId": vendors[0]._id,
        "itemCategory": "Pani Puri",
        "itemName": "Lemon Puri",
        "description": "Tangy lemon flavored puris with fresh herbs",
        "isAvailable": true,
        "slug": generateMenuItemSlug("Lemon Puri"),
        "image": "/bandiwala-items-pics/items/Jai Bhavani Chat/lemon puri.jpg",
        "subcategories": [
          {
            "title": "7 Pcs",
            "quantity": "7 Pcs",
            "price": 40
          }
        ]
      },
      {
        "vendorId": vendors[0]._id,
        "itemCategory": "Chat",
        "itemName": "Papdi Chat",
        "description": "Crispy papdi chat with yogurt and chutneys",
        "isAvailable": true,
        "slug": generateMenuItemSlug("Papdi Chat"),
        "image": "/bandiwala-items-pics/items/Jai Bhavani Chat/papdi chat.jpg",
        "subcategories": [
          {
            "title": "1 unit",
            "quantity": "1 unit",
            "price": 40
          }
        ]
      },
      {
        "vendorId": vendors[0]._id,
        "itemCategory": "Chat",
        "itemName": "Samosa Chat",
        "description": "Crispy samosa topped with chutneys and spices",
        "isAvailable": true,
        "slug": generateMenuItemSlug("Samosa Chat"),
        "image": "/bandiwala-items-pics/items/Jai Bhavani Chat/samosa chat.jpg",
        "subcategories": [
          {
            "title": "1 unit",
            "quantity": "1 unit",
            "price": 40
          }
        ]
      },
      {
        "vendorId": vendors[0]._id,
        "itemCategory": "Chat",
        "itemName": "Aloo Chat",
        "description": "Spicy potato chat with tangy chutneys",
        "isAvailable": true,
        "slug": generateMenuItemSlug("Aloo Chat"),
        "image": "/bandiwala-items-pics/items/Jai Bhavani Chat/aloo chat.jpg",
        "subcategories": [
          {
            "title": "1 unit",
            "quantity": "1 unit",
            "price": 40
          }
        ]
      },

      {
        "vendorId": vendors[1]._id,
        "itemCategory": "Chicken Pakodi",
        "itemName": "CHICKEN PAKODI (Bone)",
        "description": "Crispy chicken pakodi with bone, perfectly spiced",
        "isAvailable": true,
        "slug": generateMenuItemSlug("CHICKEN PAKODI (Bone)"),
        "image": "/bandiwala-items-pics/items/BFC Chicken pakodi/Chicken pakodi_chicken bone.jpg",
        "subcategories": [
          {
            "title": "250 gr",
            "quantity": "Chicken Pakodi 250 gr",
            "price": 155
          },
          {
            "title": "500 gr",
            "quantity": "Chicken Pakodi 500 gr",
            "price": 305
          },
          {
            "title": "750 gr",
            "quantity": "Chicken Pakodi 750 gr",
            "price": 455
          },
          {
            "title": "1 Kg",
            "quantity": "Chicken Pakodi 1 Kg",
            "price": 605
          }
        ]
      },
      {
        "vendorId": vendors[1]._id,
        "itemCategory": "Chicken Pakodi",
        "itemName": "CHICKEN PAKODI (Boneless)",
        "description": "Tender boneless chicken pakodi with aromatic spices",
        "isAvailable": true,
        "slug": generateMenuItemSlug("CHICKEN PAKODI (Boneless)"),
        "image": "/bandiwala-items-pics/items/BFC Chicken pakodi/Chicken pakodi _ boneless.jpg",
        "subcategories": [
          {
            "title": "250 gr",
            "quantity": "Chicken Pakodi 250 gr",
            "price": 205
          },
          {
            "title": "500 gr",
            "quantity": "Chicken Pakodi 500 gr",
            "price": 405
          },
          {
            "title": "750 gr",
            "quantity": "Chicken Pakodi 750 gr",
            "price": 605
          },
          {
            "title": "1 Kg",
            "quantity": "Chicken Pakodi 1 Kg",
            "price": 805
          }
        ]
      },
      {
        "vendorId": vendors[1]._id,
        "itemCategory": "Chicken Fried",
        "itemName": "CHICKEN WINGS",
        "description": "Crispy fried chicken wings with special masala",
        "isAvailable": true,
        "slug": generateMenuItemSlug("CHICKEN WINGS"),
        "image": "/bandiwala-items-pics/items/BFC Chicken pakodi/Chicken wings.jpg",
        "subcategories": [
          {
            "title": "250 gr",
            "quantity": "Chicken Wings 250 gr",
            "price": 155
          },
          {
            "title": "500 gr",
            "quantity": "Chicken Wings 500 gr",
            "price": 305
          }
        ]
      },
      {
        "vendorId": vendors[1]._id,
        "itemCategory": "Chicken Fried",
        "itemName": "Chicken Leg Piece",
        "description": "Juicy chicken leg piece, perfectly fried",
        "isAvailable": true,
        "slug": generateMenuItemSlug("Chicken Leg Piece"),
        "image": "/bandiwala-items-pics/items/BFC Chicken pakodi/Chicken Leg piece.jpeg",
        "subcategories": [
          {
            "title": "One Piece",
            "quantity": "One Piece",
            "price": 75
          }
        ]
      },
      {
        "vendorId": vendors[1]._id,
        "itemCategory": "Chicken Fried",
        "itemName": "Chicken Joint",
        "description": "Tender chicken joint with crispy coating",
        "isAvailable": true,
        "slug": generateMenuItemSlug("Chicken Joint"),
        "image": "/bandiwala-items-pics/items/BFC Chicken pakodi/Chicken Joint.jpeg",
        "subcategories": [
          {
            "title": "One Piece",
            "quantity": "One Piece",
            "price": 105
          }
        ]
      },
      {
        "vendorId": vendors[1]._id,
        "itemCategory": "Chicken Fried",
        "itemName": "Kandanakaya (Chicken Gizzards)",
        "description": "Spicy chicken gizzards, a local delicacy",
        "isAvailable": true,
        "slug": generateMenuItemSlug("Kandanakaya (Chicken Gizzards)"),
        "image": "/bandiwala-items-pics/items/BFC Chicken pakodi/Kandankaya fry.png",
        "subcategories": [
          {
            "title": "300 gms",
            "quantity": "300 gms",
            "price": 155
          }
        ]
      },
      {
        "vendorId": vendors[1]._id,
        "itemCategory": "Fish items / Sea foods",
        "itemName": "Fish Cut Pieces (Rava)",
        "description": "Fresh fish pieces coated in rava and fried",
        "isAvailable": true,
        "slug": generateMenuItemSlug("Fish Cut Pieces (Rava)"),
        "image": "/bandiwala-items-pics/items/BFC Chicken pakodi/Fish cut pieces.jpg",
        "subcategories": [
          {
            "title": "1 Pc",
            "quantity": "1 Pc",
            "price": 85
          }
        ]
      },
      {
        "vendorId": vendors[1]._id,
        "itemCategory": "Fish items / Sea foods",
        "itemName": "Thilapi Goraka",
        "description": "Traditional fish preparation with special spices",
        "isAvailable": true,
        "slug": generateMenuItemSlug("Thilapi Goraka"),
        "image": "/bandiwala-items-pics/items/BFC Chicken pakodi/Tilapi Fish.jpg",
        "subcategories": [
          {
            "title": "1 Pc",
            "quantity": "1 Pc",
            "price": 155
          }
        ]
      },
      {
        "vendorId": vendors[1]._id,
        "itemCategory": "Fish items / Sea foods",
        "itemName": "Prawns Fry",
        "description": "Fresh prawns fried to perfection with spices",
        "isAvailable": true,
        "slug": generateMenuItemSlug("Prawns Fry"),
        "image": "/bandiwala-items-pics/items/BFC Chicken pakodi/Prawns fry.png",
        "subcategories": [
          {
            "title": "250 gr",
            "quantity": "250 gr",
            "price": 255
          },
          {
            "title": "500G",
            "quantity": "500G",
            "price": 505
          }
        ]
      },
      {
        "vendorId": vendors[1]._id,
        "itemCategory": "Fish items / Sea foods",
        "itemName": "Methallu",
        "description": "Traditional fish preparation, crispy and flavorful",
        "isAvailable": true,
        "slug": generateMenuItemSlug("Methallu"),
        "image": "/bandiwala-items-pics/items/BFC Chicken pakodi/Mettalu fry.jpg",
        "subcategories": [
          {
            "title": "250Gms",
            "quantity": "250Gms",
            "price": 205
          }
        ]
      },

      {
        "vendorId": vendors[2]._id,
        "itemCategory": "Bajjis",
        "itemName": "Mirchi Bajji (With onion stuffing)",
        "description": "Spicy green chili bajji stuffed with onions",
        "isAvailable": true,
        "slug": generateMenuItemSlug("Mirchi Bajji (With onion stuffing)"),
        "image": "/bandiwala-items-pics/items/Rajabundry Vaari special muntha masala/BAJJI/Mirchi bajji.png",
        "subcategories": [
          {
            "title": "4pcs",
            "quantity": "4pcs",
            "price": 45
          }
        ]
      },
      {
        "vendorId": vendors[2]._id,
        "itemCategory": "Bajjis",
        "itemName": "Aratikaya Bajji",
        "description": "Crispy banana bajji, a traditional favorite",
        "isAvailable": true,
        "slug": generateMenuItemSlug("Aratikaya Bajji"),
        "image": "/bandiwala-items-pics/items/Rajabundry Vaari special muntha masala/BAJJI/Aratikaya Bajji.png",
        "subcategories": [
          {
            "title": "4pcs",
            "quantity": "4pcs",
            "price": 45
          }
        ]
      },
      {
        "vendorId": vendors[2]._id,
        "itemCategory": "Bajjis",
        "itemName": "Aloo Bajji",
        "description": "Potato bajji with perfect crispy coating",
        "isAvailable": true,
        "slug": generateMenuItemSlug("Aloo Bajji"),
        "image": "/bandiwala-items-pics/items/Rajabundry Vaari special muntha masala/BAJJI/Aloo bajji.jpg",
        "subcategories": [
          {
            "title": "4pcs",
            "quantity": "4pcs",
            "price": 45
          }
        ]
      },
      {
        "vendorId": vendors[2]._id,
        "itemCategory": "Bajjis",
        "itemName": "Vankaya Bajji",
        "description": "Eggplant bajji served with special mixture",
        "isAvailable": true,
        "slug": generateMenuItemSlug("Vankaya Bajji"),
        "image": "/bandiwala-items-pics/items/Rajabundry Vaari special muntha masala/BAJJI/Vankaya bajji.jpg",
        "subcategories": [
          {
            "title": "1 Piece with Mixture",
            "quantity": "1 Piece with Mixture",
            "price": 45
          }
        ]
      },
      {
        "vendorId": vendors[2]._id,
        "itemCategory": "Bajjis",
        "itemName": "Tomato Bajji",
        "description": "Tangy tomato bajji with aromatic spices",
        "isAvailable": true,
        "slug": generateMenuItemSlug("Tomato Bajji"),
        "image": "/bandiwala-items-pics/items/Rajabundry Vaari special muntha masala/BAJJI/tomato bajji.jpg",
        "subcategories": [
          {
            "title": "2 cups",
            "quantity": "2 cups",
            "price": 45
          }
        ]
      },
      {
        "vendorId": vendors[2]._id,
        "itemCategory": "Bajjis",
        "itemName": "Egg Bajji",
        "description": "Boiled egg bajji with crispy batter coating",
        "isAvailable": true,
        "slug": generateMenuItemSlug("Egg Bajji"),
        "image": "/bandiwala-items-pics/items/Rajabundry Vaari special muntha masala/BAJJI/egg bajji.jpg",
        "subcategories": [
          {
            "title": "1 Piece With mixture",
            "quantity": "1 Piece With mixture",
            "price": 45
          }
        ]
      },
      {
        "vendorId": vendors[2]._id,
        "itemCategory": "Vada",
        "itemName": "Masala Vada",
        "description": "Crispy lentil vada with aromatic spices",
        "isAvailable": true,
        "slug": generateMenuItemSlug("Masala Vada"),
        "image": "/bandiwala-items-pics/items/Rajabundry Vaari special muntha masala/Masala wada.jpeg",
        "subcategories": [
          {
            "title": "4pcs",
            "quantity": "4pcs",
            "price": 45
          }
        ]
      },
      {
        "vendorId": vendors[2]._id,
        "itemCategory": "Punugulu",
        "itemName": "Punugulu",
        "description": "Traditional Andhra snack, crispy and delicious",
        "isAvailable": true,
        "slug": generateMenuItemSlug("Punugulu"),
        "image": "/bandiwala-items-pics/items/Rajabundry Vaari special muntha masala/punugulu.jpg",
        "subcategories": [
          {
            "title": "15pcs",
            "quantity": "15pcs",
            "price": 45
          }
        ]
      },
      {
        "vendorId": vendors[2]._id,
        "itemCategory": "Muntha masala",
        "itemName": "Mirchi Bajji Muntha Masala",
        "description": "Spicy mirchi bajji with special muntha masala",
        "isAvailable": true,
        "slug": "mirchi-bajji-muntha-masala",
        "image": "/bandiwala-items-pics/items/Rajabundry Vaari special muntha masala/Mirchi bajji mixture.jpg",
        "subcategories": [
          {
            "title": "1 Unit",
            "quantity": "1 Unit",
            "price": 55
          }
        ]
      },
      {
        "vendorId": vendors[2]._id,
        "itemCategory": "Muntha masala",
        "itemName": "Aritikaya Bajji Muntha Masala",
        "description": "Banana bajji with traditional muntha masala",
        "isAvailable": true,
        "slug": "aritikaya-bajji-muntha-masala",
        "image": "/bandiwala-items-pics/items/Rajabundry Vaari special muntha masala/Muntha Masala/Aratikaya bajji muntha masala.jpg",
        "subcategories": [
          {
            "title": "1 Unit",
            "quantity": "1 Unit",
            "price": 55
          }
        ]
      },
      {
        "vendorId": vendors[2]._id,
        "itemCategory": "Muntha masala",
        "itemName": "Aloo Bajji Muntha Masala",
        "description": "Potato bajji with aromatic muntha masala",
        "isAvailable": true,
        "slug": "aloo-bajji-muntha-masala",
        "image": "/bandiwala-items-pics/items/Rajabundry Vaari special muntha masala/Muntha Masala/AAlu bajji muntha masala.avif",
        "subcategories": [
          {
            "title": "1 Unit",
            "quantity": "1 Unit",
            "price": 55
          }
        ]
      },
      {
        "vendorId": vendors[2]._id,
        "itemCategory": "Muntha masala",
        "itemName": "Vankaya Bajji Muntha Masala",
        "description": "Eggplant bajji with special muntha masala",
        "isAvailable": true,
        "slug": "vankaya-bajji-muntha-masala",
        "image": "/bandiwala-items-pics/items/Rajabundry Vaari special muntha masala/Muntha Masala/vankaya bajji muntha masala.jpg",
        "subcategories": [
          {
            "title": "1 Unit",
            "quantity": "1 Unit",
            "price": 55
          }
        ]
      },
      {
        "vendorId": vendors[2]._id,
        "itemCategory": "Muntha masala",
        "itemName": "Tomato Bajji Muntha Masala",
        "description": "Tomato bajji with tangy muntha masala",
        "isAvailable": true,
        "slug": "tomato-bajji-muntha-masala",
        "image": "/bandiwala-items-pics/items/Rajabundry Vaari special muntha masala/Muntha Masala/tamato bajji muntha masala.jpg",
        "subcategories": [
          {
            "title": "1 Unit",
            "quantity": "1 Unit",
            "price": 55
          }
        ]
      },
      {
        "vendorId": vendors[2]._id,
        "itemCategory": "Muntha masala",
        "itemName": "Egg Bajji Muntha Masala",
        "description": "Egg bajji with flavorful muntha masala",
        "isAvailable": true,
        "slug": "egg-bajji-muntha-masala",
        "image": "/bandiwala-items-pics/items/Rajabundry Vaari special muntha masala/Muntha Masala/egg bajji muntha masala.jpg",
        "subcategories": [
          {
            "title": "1 Unit",
            "quantity": "1 Unit",
            "price": 55
          }
        ]
      },
      {
        "vendorId": vendors[2]._id,
        "itemCategory": "Muntha masala",
        "itemName": "Masala Vada Muntha Masala",
        "description": "Masala vada with traditional muntha masala",
        "isAvailable": true,
        "slug": "masala-vada-muntha-masala",
        "image": "/bandiwala-items-pics/items/Rajabundry Vaari special muntha masala/Masala wada.jpeg",
        "subcategories": [
          {
            "title": "1 Unit",
            "quantity": "1 Unit",
            "price": 55
          }
        ]
      },
      {
        "vendorId": vendors[2]._id,
        "itemCategory": "Mixtures",
        "itemName": "Mirchi Bajji Mixture",
        "description": "Spicy mirchi bajji served with crunchy mixture",
        "isAvailable": true,
        "slug": "mirchi-bajji-mixture",
        "image": "/bandiwala-items-pics/items/Rajabundry Vaari special muntha masala/Mixture/Bajji mixture.jpg",
        "subcategories": [
          {
            "title": "1 Unit",
            "quantity": "1 Unit",
            "price": 55
          }
        ]
      },
      {
        "vendorId": vendors[2]._id,
        "itemCategory": "Mixtures",
        "itemName": "Aratikaya Bajji Mixture",
        "description": "Banana bajji with traditional mixture",
        "isAvailable": true,
        "slug": "aratikaya-bajji-mixture",
        "image": "/bandiwala-items-pics/items/Rajabundry Vaari special muntha masala/Mixture/Bajji mixture.jpg",
        "subcategories": [
          {
            "title": "1 Unit",
            "quantity": "1 Unit",
            "price": 55
          }
        ]
      },
      {
        "vendorId": vendors[2]._id,
        "itemCategory": "Mixtures",
        "itemName": "Aloo Bajji Mixture",
        "description": "Potato bajji with aromatic mixture",
        "isAvailable": true,
        "slug": "aloo-bajji-mixture",
        "image": "/bandiwala-items-pics/items/Rajabundry Vaari special muntha masala/Mixture/Bajji mixture.jpg",
        "subcategories": [
          {
            "title": "1 Unit",
            "quantity": "1 Unit",
            "price": 55
          }
        ]
      },
      {
        "vendorId": vendors[2]._id,
        "itemCategory": "Mixtures",
        "itemName": "Vankaya Bajji Mixture",
        "description": "Eggplant bajji with special mixture",
        "isAvailable": true,
        "slug": "vankaya-bajji-mixture",
        "image": "/bandiwala-items-pics/items/Rajabundry Vaari special muntha masala/Mixture/Vnakay bajji mixture.png",
        "subcategories": [
          {
            "title": "1 Unit",
            "quantity": "1 Unit",
            "price": 55
          }
        ]
      },
      {
        "vendorId": vendors[2]._id,
        "itemCategory": "Mixtures",
        "itemName": "Tomato Bajji Mixture",
        "description": "Tomato bajji with tangy mixture",
        "isAvailable": true,
        "slug": "tomato-bajji-mixture",
        "image": "/bandiwala-items-pics/items/Rajabundry Vaari special muntha masala/Mixture/Tomato mixture & all other mixtures.avif",
        "subcategories": [
          {
            "title": "1 Unit",
            "quantity": "1 Unit",
            "price": 55
          }
        ]
      },
      {
        "vendorId": vendors[2]._id,
        "itemCategory": "Mixtures",
        "itemName": "Egg Bajji Mixture",
        "description": "Egg bajji with flavorful mixture",
        "isAvailable": true,
        "slug": "egg-bajji-mixture",
        "image": "/bandiwala-items-pics/items/Rajabundry Vaari special muntha masala/Mixture/egg mixture.jpg",
        "subcategories": [
          {
            "title": "1 Unit",
            "quantity": "1 Unit",
            "price": 55
          }
        ]
      },
      {
        "vendorId": vendors[2]._id,
        "itemCategory": "Mixtures",
        "itemName": "Masala Vada Bajji Mixture",
        "description": "Masala vada with crunchy mixture",
        "isAvailable": true,
        "slug": "masala-vada-bajji-mixture",
        "image": "/bandiwala-items-pics/items/Rajabundry Vaari special muntha masala/Mixture/Bajji mixture.jpg",
        "subcategories": [
          {
            "title": "1 Unit",
            "quantity": "1 Unit",
            "price": 55
          }
        ]
      },
      {
        "vendorId": vendors[2]._id,
        "itemCategory": "Mixtures",
        "itemName": "Atukula Mixture",
        "description": "Traditional rice flakes mixture",
        "isAvailable": true,
        "slug": "atukula-mixture",
        "image": "/bandiwala-items-pics/items/Rajabundry Vaari special muntha masala/Mixture/Tomato mixture & all other mixtures.avif",
        "subcategories": [
          {
            "title": "1 qty",
            "quantity": "1 qty",
            "price": 55
          }
        ]
      },
      {
        "vendorId": vendors[2]._id,
        "itemCategory": "Mixtures",
        "itemName": "Corn Mixture",
        "description": "Crispy corn mixture with spices",
        "isAvailable": true,
        "slug": "corn-mixture",
        "image": "/bandiwala-items-pics/items/Rajabundry Vaari special muntha masala/Mixture/Tomato mixture & all other mixtures.avif",
        "subcategories": [
          {
            "title": "1 Unit",
            "quantity": "1 Unit",
            "price": 55
          }
        ]
      },
      {
        "vendorId": vendors[2]._id,
        "itemCategory": "Mixtures",
        "itemName": "Kaju Mixture",
        "description": "Premium cashew mixture, rich and crunchy",
        "isAvailable": true,
        "slug": "kaju-mixture",
        "image": "/bandiwala-items-pics/items/Rajabundry Vaari special muntha masala/Mixture/Kaju Mixture.avif",
        "subcategories": [
          {
            "title": "1 Unit",
            "quantity": "1 Unit",
            "price": 85
          }
        ]
      },
      {
        "vendorId": vendors[2]._id,
        "itemCategory": "Mixtures",
        "itemName": "Ghee Mixture",
        "description": "Traditional mixture prepared in pure ghee",
        "isAvailable": true,
        "slug": "ghee-mixture",
        "image": "/bandiwala-items-pics/items/Rajabundry Vaari special muntha masala/Mixture/Tomato mixture & all other mixtures.avif",
        "subcategories": [
          {
            "title": "1 Unit",
            "quantity": "1 Unit",
            "price": 85
          }
        ]
      },
      {
        "vendorId": vendors[2]._id,
        "itemCategory": "Batani",
        "itemName": "Mirchi Batani",
        "description": "Spicy mirchi with traditional batani preparation",
        "isAvailable": true,
        "slug": "mirchi-batani",
        "image": "/bandiwala-items-pics/items/Rajabundry Vaari special muntha masala/Batni/Mirchi batani.jpeg",
        "subcategories": [
          {
            "title": "1 Unit",
            "quantity": "1 Unit",
            "price": 45
          }
        ]
      },
      {
        "vendorId": vendors[2]._id,
        "itemCategory": "Batani",
        "itemName": "Aratikaya Batani",
        "description": "Banana with special batani style",
        "isAvailable": true,
        "slug": "aratikaya-batani",
        "image": "/bandiwala-items-pics/items/Rajabundry Vaari special muntha masala/Batni/Aratikai Batani.jpeg",
        "subcategories": [
          {
            "title": "1 Unit",
            "quantity": "1 Unit",
            "price": 45
          }
        ]
      },
      {
        "vendorId": vendors[2]._id,
        "itemCategory": "Batani",
        "itemName": "Aloo Batani",
        "description": "Potato batani with aromatic spices",
        "isAvailable": true,
        "slug": "aloo-batani",
        "image": "/bandiwala-items-pics/items/Rajabundry Vaari special muntha masala/Batni/Aloo Batani.jpeg",
        "subcategories": [
          {
            "title": "1 Unit",
            "quantity": "1 Unit",
            "price": 45
          }
        ]
      },
      {
        "vendorId": vendors[2]._id,
        "itemCategory": "Batani",
        "itemName": "Vankaya Batani",
        "description": "Eggplant batani, traditional preparation",
        "isAvailable": true,
        "slug": "vankaya-batani",
        "image": "/bandiwala-items-pics/items/Rajabundry Vaari special muntha masala/Batni/Vankaya batani.jpeg",
        "subcategories": [
          {
            "title": "1 Unit",
            "quantity": "1 Unit",
            "price": 45
          }
        ]
      },
      {
        "vendorId": vendors[2]._id,
        "itemCategory": "Batani",
        "itemName": "Tomato Batani",
        "description": "Tangy tomato batani with spices",
        "isAvailable": true,
        "slug": "tomato-batani",
        "image": "/bandiwala-items-pics/items/Rajabundry Vaari special muntha masala/Batni/Tomato batani.jpg",
        "subcategories": [
          {
            "title": "1 Unit",
            "quantity": "1 Unit",
            "price": 45
          }
        ]
      },
      {
        "vendorId": vendors[2]._id,
        "itemCategory": "Batani",
        "itemName": "Egg Batani",
        "description": "Egg batani with flavorful preparation",
        "isAvailable": true,
        "slug": "egg-batani",
        "image": "/bandiwala-items-pics/items/Rajabundry Vaari special muntha masala/Batni/Egg batani.jpeg",
        "subcategories": [
          {
            "title": "1 Unit",
            "quantity": "1 Unit",
            "price": 45
          }
        ]
      },
      {
        "vendorId": vendors[2]._id,
        "itemCategory": "Batani",
        "itemName": "Corn Flakes Batani",
        "description": "Crispy corn flakes batani",
        "isAvailable": true,
        "slug": "corn-flakes-batani",
        "image": "/bandiwala-items-pics/items/Rajabundry Vaari special muntha masala/Batni/corn flaksbatani.jpg",
        "subcategories": [
          {
            "title": "1 Unit",
            "quantity": "1 Unit",
            "price": 45
          }
        ]
      },
      {
        "vendorId": vendors[2]._id,
        "itemCategory": "Slices",
        "itemName": "Tomato Slices",
        "description": "Fresh tomato slices with special seasoning",
        "isAvailable": true,
        "slug": "tomato-slices",
        "image": "/bandiwala-items-pics/items/Rajabundry Vaari special muntha masala/Tomato slices.jpg",
        "subcategories": [
          {
            "title": "1 Unit",
            "quantity": "1 Unit",
            "price": 55
          }
        ]
      },
      {
        "vendorId": vendors[2]._id,
        "itemCategory": "Slices",
        "itemName": "Egg Slices",
        "description": "Boiled egg slices with aromatic spices",
        "isAvailable": true,
        "slug": "egg-slices",
        "image": "/bandiwala-items-pics/items/Rajabundry Vaari special muntha masala/egg slices.jpg",
        "subcategories": [
          {
            "title": "1 Unit",
            "quantity": "1 Unit",
            "price": 55
          }
        ]
      },
      {
        "vendorId": vendors[3]._id,
        "itemCategory": "Noodles",
        "itemName": "VEG NOODLES",
        "description": "Delicious vegetable noodles with mixed vegetables",
        "isAvailable": true,
        "slug": "veg-noodles",
        "image": "/bandiwala-items-pics/items/Snagamesh Lkashmi bahavani fast food/veg noodles.jpg",
        "subcategories": [
          {
            "title": "Half plate",
            "quantity": "Half plate",
            "price": 65
          },
          {
            "title": "Full Plate",
            "quantity": "Full Plate",
            "price": 105
          }
        ]
      },
      {
        "vendorId": vendors[3]._id,
        "itemCategory": "Noodles",
        "itemName": "EGG NOODLES",
        "description": "Tasty egg noodles with scrambled eggs",
        "isAvailable": true,
        "slug": "egg-noodles",
        "image": "/bandiwala-items-pics/items/Snagamesh Lkashmi bahavani fast food/egg noodles.jpg",
        "subcategories": [
          {
            "title": "Half plate",
            "quantity": "Half plate",
            "price": 75
          },
          {
            "title": "Full Plate",
            "quantity": "Full Plate",
            "price": 125
          }
        ]
      },
      {
        "vendorId": vendors[3]._id,
        "itemCategory": "Noodles",
        "itemName": "CHICKEN NOODLES",
        "description": "Spicy chicken noodles with tender chicken pieces",
        "isAvailable": true,
        "slug": "chicken-noodles",
        "image": "/bandiwala-items-pics/items/Snagamesh Lkashmi bahavani fast food/chicken noodles.jpg",
        "subcategories": [
          {
            "title": "Half plate",
            "quantity": "Half plate",
            "price": 85
          },
          {
            "title": "Full Plate",
            "quantity": "Full Plate",
            "price": 145
          }
        ]
      },
      {
        "vendorId": vendors[3]._id,
        "itemCategory": "Noodles",
        "itemName": "DOUBLE EGG CHICKEN NOODLES",
        "description": "Chicken noodles with extra eggs",
        "isAvailable": true,
        "slug": "double-egg-chicken-noodles",
        "image": "/bandiwala-items-pics/items/Snagamesh Lkashmi bahavani fast food/chicken noodles.jpg",
        "subcategories": [
          {
            "title": "Half plate",
            "quantity": "Half plate",
            "price": 95
          },
          {
            "title": "Full Plate",
            "quantity": "Full Plate",
            "price": 155
          }
        ]
      },
      {
        "vendorId": vendors[3]._id,
        "itemCategory": "Noodles",
        "itemName": "DOUBLE EGG NOODLES",
        "description": "Egg noodles with double portion of eggs",
        "isAvailable": true,
        "slug": "double-egg-noodles",
        "image": "/bandiwala-items-pics/items/Snagamesh Lkashmi bahavani fast food/egg noodles.jpg",
        "subcategories": [
          {
            "title": "Half plate",
            "quantity": "Half plate",
            "price": 85
          },
          {
            "title": "Full Plate",
            "quantity": "Full Plate",
            "price": 145
          }
        ]
      },
      {
        "vendorId": vendors[3]._id,
        "itemCategory": "Fried rice",
        "itemName": "VEG FRIED RICE",
        "description": "Aromatic vegetable fried rice with mixed vegetables",
        "isAvailable": true,
        "slug": "veg-fried-rice",
        "image": "/bandiwala-items-pics/items/Snagamesh Lkashmi bahavani fast food/veg fried rice.jpg",
        "subcategories": [
          {
            "title": "Half plate",
            "quantity": "Half plate",
            "price": 65
          },
          {
            "title": "Full Plate",
            "quantity": "Full Plate",
            "price": 105
          }
        ]
      },
      {
        "vendorId": vendors[3]._id,
        "itemCategory": "Fried rice",
        "itemName": "EGG FRIED RICE",
        "description": "Delicious egg fried rice with scrambled eggs",
        "isAvailable": true,
        "slug": "egg-fried-rice",
        "image": "/bandiwala-items-pics/items/Snagamesh Lkashmi bahavani fast food/egg fried ice.jpg",
        "subcategories": [
          {
            "title": "Half plate",
            "quantity": "Half plate",
            "price": 75
          },
          {
            "title": "Full Plate",
            "quantity": "Full Plate",
            "price": 125
          }
        ]
      },
      {
        "vendorId": vendors[3]._id,
        "itemCategory": "Fried rice",
        "itemName": "CHICKEN FRIED RICE",
        "description": "Flavorful chicken fried rice with tender chicken",
        "isAvailable": true,
        "slug": "chicken-fried-rice",
        "image": "/bandiwala-items-pics/items/Snagamesh Lkashmi bahavani fast food/chicken fried rice.jpg",
        "subcategories": [
          {
            "title": "Half plate",
            "quantity": "Half plate",
            "price": 85
          },
          {
            "title": "Full Plate",
            "quantity": "Full Plate",
            "price": 145
          }
        ]
      },
      {
        "vendorId": vendors[3]._id,
        "itemCategory": "Fried rice",
        "itemName": "DOUBLE EGG CHICKEN FRIED RICE",
        "description": "Chicken fried rice with extra eggs",
        "isAvailable": true,
        "slug": "double-egg-chicken-fried-rice",
        "image": "/bandiwala-items-pics/items/Snagamesh Lkashmi bahavani fast food/chicken fried rice(1).jpg",
        "subcategories": [
          {
            "title": "Half plate",
            "quantity": "Half plate",
            "price": 95
          },
          {
            "title": "Full Plate",
            "quantity": "Full Plate",
            "price": 155
          }
        ]
      },
      {
        "vendorId": vendors[3]._id,
        "itemCategory": "Fried rice",
        "itemName": "DOUBLE EGG FRIED RICE",
        "description": "Egg fried rice with double portion of eggs",
        "isAvailable": true,
        "slug": "double-egg-fried-rice",
        "image": "/bandiwala-items-pics/items/Snagamesh Lkashmi bahavani fast food/egg fried ice.jpg",
        "subcategories": [
          {
            "title": "Half plate",
            "quantity": "Half plate",
            "price": 85
          },
          {
            "title": "Full Plate",
            "quantity": "Full Plate",
            "price": 145
          }
        ]
      },
      {
        "vendorId": vendors[3]._id,
        "itemCategory": "Fried rice",
        "itemName": "VEG MANCHURIAN FRIED RICE",
        "description": "Fried rice with vegetable manchurian",
        "isAvailable": true,
        "slug": "veg-manchurian-fried-rice",
        "image": "/bandiwala-items-pics/items/Snagamesh Lkashmi bahavani fast food/veg manchurian rice.jpg",
        "subcategories": [
          {
            "title": "Half plate",
            "quantity": "Half plate",
            "price": 75
          },
          {
            "title": "Full Plate",
            "quantity": "Full Plate",
            "price": 135
          }
        ]
      },
      {
        "vendorId": vendors[3]._id,
        "itemCategory": "Manchurian",
        "itemName": "CHICKEN MANCHURIAN",
        "description": "Spicy chicken manchurian with aromatic sauce",
        "isAvailable": true,
        "slug": "chicken-manchurian",
        "image": "/bandiwala-items-pics/items/Snagamesh Lkashmi bahavani fast food/chicken manchurian.jpg",
        "subcategories": [
          {
            "title": "Half plate",
            "quantity": "Half plate",
            "price": 105
          },
          {
            "title": "Full Plate",
            "quantity": "Full Plate",
            "price": 195
          }
        ]
      },
      {
        "vendorId": vendors[3]._id,
        "itemCategory": "Manchurian",
        "itemName": "VEG MANCHURIAN",
        "description": "Crispy vegetable manchurian with tangy sauce",
        "isAvailable": true,
        "slug": "veg-manchurian",
        "image": "/bandiwala-items-pics/items/Snagamesh Lkashmi bahavani fast food/veg manchurian.jpg",
        "subcategories": [
          {
            "title": "Half plate",
            "quantity": "Half plate",
            "price": 65
          },
          {
            "title": "Full Plate",
            "quantity": "Full Plate",
            "price": 115
          }
        ]
      },
      {
        "vendorId": vendors[3]._id,
        "itemCategory": "Manchurian",
        "itemName": "CHICKEN 65",
        "description": "Famous South Indian chicken 65, spicy and crispy",
        "isAvailable": true,
        "slug": "chicken-65",
        "image": "/bandiwala-items-pics/items/Snagamesh Lkashmi bahavani fast food/chicken 65.jpg",
        "subcategories": [
          {
            "title": "Half plate",
            "quantity": "Half plate",
            "price": 135
          },
          {
            "title": "Full Plate",
            "quantity": "Full Plate",
            "price": 225
          }
        ]
      },
      {
        "vendorId": vendors[3]._id,
        "itemCategory": "Manchurian",
        "itemName": "CHILLY CHICKEN",
        "description": "Indo-Chinese chilly chicken with peppers",
        "isAvailable": true,
        "slug": "chilly-chicken",
        "image": "/bandiwala-items-pics/items/Snagamesh Lkashmi bahavani fast food/chill chicken.jpg",
        "subcategories": [
          {
            "title": "Half plate",
            "quantity": "Half plate",
            "price": 135
          },
          {
            "title": "Full Plate",
            "quantity": "Full Plate",
            "price": 225
          }
        ]
      },
      {
        "vendorId": vendors[3]._id,
        "itemCategory": "Manchurian",
        "itemName": "EGG MANCHURIAN",
        "description": "Unique egg manchurian with special sauce",
        "isAvailable": true,
        "slug": "egg-manchurian",
        "image": "/bandiwala-items-pics/items/Snagamesh Lkashmi bahavani fast food/egg manchurian.jpg",
        "subcategories": [
          {
            "title": "Half plate",
            "quantity": "Half plate",
            "price": 85
          },
          {
            "title": "Full Plate",
            "quantity": "Full Plate",
            "price": 145
          }
        ]
      },
      {
        "vendorId": vendors[3]._id,
        "itemCategory": "Manchurian",
        "itemName": "VEG 65",
        "description": "Vegetarian version of the famous 65 preparation",
        "isAvailable": true,
        "slug": "veg-65",
        "image": "/bandiwala-items-pics/items/Snagamesh Lkashmi bahavani fast food/veg 65.jpg",
        "subcategories": [
          {
            "title": "Half plate",
            "quantity": "Half plate",
            "price": 85
          },
          {
            "title": "Full Plate",
            "quantity": "Full Plate",
            "price": 145
          }
        ]
      },

      // Amma Hot Soups Menu Items
      {
        "vendorId": vendors[4]._id,
        "itemCategory": "Veg Soups",
        "itemName": "Mushroom Soup",
        "description": "Fresh mushroom soup with aromatic herbs",
        "isAvailable": true,
        "slug": generateMenuItemSlug("Mushroom Soup"),
        "image": "/bandiwala-items-pics/items/Amma soups/Mushroom soup.jpg",
        "subcategories": [
          {
            "title": "1 Bowl",
            "quantity": "1 Bowl",
            "price": 60
          }
        ]
      },
      {
        "vendorId": vendors[4]._id,
        "itemCategory": "Veg Soups",
        "itemName": "Sweet Corn Soup",
        "description": "Creamy sweet corn soup with vegetables",
        "isAvailable": true,
        "slug": generateMenuItemSlug("Sweet Corn Soup"),
        "image": "/bandiwala-items-pics/items/Amma soups/Sweet corn soup.jpeg",
        "subcategories": [
          {
            "title": "1 Bowl",
            "quantity": "1 Bowl",
            "price": 60
          }
        ]
      },
      {
        "vendorId": vendors[4]._id,
        "itemCategory": "Veg Soups",
        "itemName": "Tomato soup",
        "description": "Classic tomato soup with fresh herbs",
        "isAvailable": true,
        "slug": generateMenuItemSlug("Tomato soup"),
        "image": "/bandiwala-items-pics/items/Rajabundry Vaari special muntha masala/BAJJI/tomato bajji.jpg",
        "subcategories": [
          {
            "title": "1 Bowl",
            "quantity": "1 Bowl",
            "price": 60
          }
        ]
      },
      {
        "vendorId": vendors[4]._id,
        "itemCategory": "Veg Soups",
        "itemName": "Vegetable Soup",
        "description": "Mixed vegetable soup with seasonal vegetables",
        "isAvailable": true,
        "slug": generateMenuItemSlug("Vegetable Soup"),
        "image": "/bandiwala-items-pics/items/Amma soups/Vegitable soups.JPG",
        "subcategories": [
          {
            "title": "1 Bowl",
            "quantity": "1 Bowl",
            "price": 60
          }
        ]
      },
      {
        "vendorId": vendors[4]._id,
        "itemCategory": "Veg Soups",
        "itemName": "Hot & Soup",
        "description": "Spicy hot soup with vegetables",
        "isAvailable": true,
        "slug": generateMenuItemSlug("Hot & Soup"),
        "image": "/bandiwala-items-pics/items/Amma soups/Hot-and-Sour-Soup-3-500x375.jpg",
        "subcategories": [
          {
            "title": "1 Bowl",
            "quantity": "1 Bowl",
            "price": 60
          }
        ]
      },
      {
        "vendorId": vendors[4]._id,
        "itemCategory": "Veg Soups",
        "itemName": "Veg Manchow Soup",
        "description": "Indo-Chinese style vegetable manchow soup",
        "isAvailable": true,
        "slug": generateMenuItemSlug("Veg Manchow Soup"),
        "image": "/bandiwala-items-pics/items/Amma soups/Veg manchow soup.jpg",
        "subcategories": [
          {
            "title": "1 Bowl",
            "quantity": "1 Bowl",
            "price": 70
          }
        ]
      },
      {
        "vendorId": vendors[4]._id,
        "itemCategory": "Chicken Soup",
        "itemName": "Chicken Hot Soup",
        "description": "Spicy hot chicken soup with tender pieces",
        "isAvailable": true,
        "slug": generateMenuItemSlug("Chicken Hot Soup"),
        "image": "/bandiwala-items-pics/items/Amma soups/chicken hot soup.jpeg",
        "subcategories": [
          {
            "title": "1 Bowl",
            "quantity": "1 Bowl",
            "price": 70
          }
        ]
      },
      {
        "vendorId": vendors[4]._id,
        "itemCategory": "Chicken Soup",
        "itemName": "Chicken Corn Soup",
        "description": "Chicken soup with sweet corn and herbs",
        "isAvailable": true,
        "slug": generateMenuItemSlug("Chicken Corn Soup"),
        "image": "/bandiwala-items-pics/items/Amma soups/Chicken corn soup.jpg",
        "subcategories": [
          {
            "title": "1 Bowl",
            "quantity": "1 Bowl",
            "price": 80
          }
        ]
      },
      {
        "vendorId": vendors[4]._id,
        "itemCategory": "Chicken Soup",
        "itemName": "Chicken Pakods Soup",
        "description": "Chicken soup with crispy pakodas",
        "isAvailable": true,
        "slug": generateMenuItemSlug("Chicken Pakods Soup"),
        "image": "/bandiwala-items-pics/items/Amma soups/chicken pakoda soup.jpg",
        "subcategories": [
          {
            "title": "1 Bowl",
            "quantity": "1 Bowl",
            "price": 90
          }
        ]
      },
      {
        "vendorId": vendors[4]._id,
        "itemCategory": "Chicken Soup",
        "itemName": "Chicken Manchow Soup",
        "description": "Indo-Chinese style chicken manchow soup",
        "isAvailable": true,
        "slug": generateMenuItemSlug("Chicken Manchow Soup"),
        "image": "/bandiwala-items-pics/items/Amma soups/chicken manchow.jpg",
        "subcategories": [
          {
            "title": "1 Bowl",
            "quantity": "1 Bowl",
            "price": 90
          }
        ]
      },
      {
        "vendorId": vendors[4]._id,
        "itemCategory": "Mutton Soup",
        "itemName": "Mutton Bone Soup",
        "description": "Rich mutton bone soup with aromatic spices",
        "isAvailable": true,
        "slug": generateMenuItemSlug("Mutton Bone Soup"),
        "image": "/bandiwala-items-pics/items/Amma soups/mutton bone soup.jpg",
        "subcategories": [
          {
            "title": "1 Bowl",
            "quantity": "1 Bowl",
            "price": 99
          }
        ]
      },
      {
        "vendorId": vendors[4]._id,
        "itemCategory": "Egg soup",
        "itemName": "Egg soup",
        "description": "Comforting egg soup with vegetables",
        "isAvailable": true,
        "slug": generateMenuItemSlug("Egg soup"),
        "image": "/bandiwala-items-pics/items/Rajabundry Vaari special muntha masala/BAJJI/egg bajji.jpg",
        "subcategories": [
          {
            "title": "1 Bowl",
            "quantity": "1 Bowl",
            "price": 80
          }
        ]
      },

      // Apsara Bandam Milk Menu Items
      {
        "vendorId": vendors[5]._id,
        "itemCategory": "Bandam Milk",
        "itemName": "Plain Badam Milk",
        "description": "Rich and creamy plain badam milk",
        "isAvailable": true,
        "slug": generateMenuItemSlug("Plain Badam Milk"),
        "image": "/bandiwala-items-pics/items/apsara/Kesar-Pista-Badaam-Milk.jpeg",
        "subcategories": [
          {
            "title": "1 Glass",
            "quantity": "1 Glass",
            "price": 60
          }
        ]
      },
      {
        "vendorId": vendors[5]._id,
        "itemCategory": "Bandam Milk",
        "itemName": "kova Ice Cream",
        "description": "Delicious kova flavored ice cream",
        "isAvailable": true,
        "slug": generateMenuItemSlug("kova Ice Cream"),
        "image": "/bandiwala-items-pics/items/apsara/kovaIceCream.jpeg",
        "subcategories": [
          {
            "title": "1 Scoop",
            "quantity": "1 Scoop",
            "price": 70
          }
        ]
      },
      {
        "vendorId": vendors[5]._id,
        "itemCategory": "Bandam Milk",
        "itemName": "Kova Badam Milk",
        "description": "Premium kova badam milk with rich texture",
        "isAvailable": true,
        "slug": generateMenuItemSlug("Kova Badam Milk"),
        "image": "/bandiwala-items-pics/items/apsara/KovaBadamMilk.jpeg",
        "subcategories": [
          {
            "title": "1 Glass",
            "quantity": "1 Glass",
            "price": 70
          }
        ]
      }
    ];

    // Insert all menu items at once
    const menuItems = await MenuItem.insertMany(menuItemsData);

    // Count items per vendor for detailed output
    const vendorItemCounts = {};
    menuItems.forEach(item => {
      const vendor = vendors.find(v => v._id.equals(item.vendorId));
      if (vendor) {
        vendorItemCounts[vendor.name] = (vendorItemCounts[vendor.name] || 0) + 1;
      }
    });

    console.log(`\n📊 ${menuItems.length} menu items created successfully!\n`);

    // Display vendor-wise breakdown with checkmarks
    console.log('📋 Vendor-wise Menu Items:');
    console.log(`${vendors[0].name} (${vendorItemCounts[vendors[0].name]} items) ✅`);
    console.log(`${vendors[1].name} (${vendorItemCounts[vendors[1].name]} items) ✅`);
    console.log(`${vendors[2].name} (${vendorItemCounts[vendors[2].name]} items) ✅`);
    console.log(`${vendors[3].name} (${vendorItemCounts[vendors[3].name]} items) ✅`);
    console.log(`${vendors[4].name} (${vendorItemCounts[vendors[4].name]} items) ✅`);
    console.log(`${vendors[5].name} (${vendorItemCounts[vendors[5].name]} items) ✅`);
    console.log('');

    // Create promo codes
    const promoCodes = await PromoCode.insertMany([
      {
        code: "FREESHIP3",
        type: "free_delivery",
        value: 0, 
        maxDiscount: 0,
        minOrderValue: 0,
        isActive: true,
        maxUsagePerUser: 3, 
        isFirstTimeUserOnly: false, 
      },
    ]);

    console.log(`${promoCodes.length} promo codes created`);
    console.log('Database seeded successfully');
    process.exit(0);
  } catch (error) {
    console.error('Error seeding database:', error);
    process.exit(1);
  }
};
