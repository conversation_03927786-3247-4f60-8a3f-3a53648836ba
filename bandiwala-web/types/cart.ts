export interface Subcategory {
  title: string;
  quantity: string;
  price: number;
}

export interface CartItem {
  selectedSubcategory: Subcategory;
  id?: string;
  menuItemId: string;
  quantity: number;
  notes?: string;
  name: string;
  price?: number; // Optional since price is in selectedSubcategory
  image: string;
  vendorId?: string;
  vendorName?: string;
  vendorPhone?: string;
  order?: number; // For reordering functionality
}

export interface Cart {
  userId: string;
  items: CartItem[];
  _id?: string;
}

export interface CartResponse {
  success: boolean;
  message?: string;
  data: Cart;
  useLocalStorage?: boolean;
  error?: CartError;
}

export interface CartError {
  code: string;
  message: string;
  timestamp?: string;
}

export interface CartTotals {
  subtotal: number;
  platformFee: number;
  deliveryCharge: number;
  tax: number;
  discount: number;
  total: number;
}