import { CartItem } from './cart';

export interface Coordinates {
  lat: number;
  lng: number;
}

export interface DeliveryAddress {
  formatted: string;
  coordinates: Coordinates;
  mapUrl?: string;
}

export type PaymentMethod = 'cash' | 'card' | 'upi';

export type OrderStatus =
  | 'placed'
  | 'confirmed'
  | 'preparing'
  | 'out_for_delivery'
  | 'delivered'
  | 'cancelled';

export type PaymentStatus = 'pending' | 'paid' | 'failed';

export interface OrderTotals {
  subtotal: number;
  platformFee: number;
  deliveryCharge: number;
  tax: number;
  discount: number;
  total: number;
}

export interface OrderSummary extends OrderTotals {
  items: CartItem[];
  deliveryAddress: DeliveryAddress;
  paymentMethod: PaymentMethod;
  promoCode?: string;
  estimatedDeliveryTime: string;
}

export interface StatusTimelineEntry {
  status: OrderStatus;
  timestamp: string;
}

export interface Order extends OrderSummary {
  _id: string;
  orderNumber?: string; // Custom order number for better user experience
  user: string;
  orderStatus: OrderStatus;
  paymentStatus: PaymentStatus;
  statusTimeline?: StatusTimelineEntry[];
  createdAt: string;
  updatedAt: string;
}

export interface OrderCreateRequest {
  deliveryAddress: DeliveryAddress;
  paymentMethod: PaymentMethod;
  promoCode?: string;
}
