export interface Review {
  _id: string;
  userId: {
    _id: string;
    name: string;
    profileImage?: string;
  };
  targetType: 'MenuItem' | 'Vendor';
  targetId: string;
  rating: number;
  comment: string;
  isVerifiedPurchase: boolean;
  helpfulVotes: number;
  // Moderation fields
  isModerated: boolean;
  moderationStatus: 'pending' | 'approved' | 'rejected' | 'flagged';
  moderationReason?: string;
  moderatedBy?: {
    _id: string;
    name: string;
  };
  moderatedAt?: string;
  flaggedBy?: Array<{
    userId: {
      _id: string;
      name: string;
    };
    reason: string;
    flaggedAt: string;
  }>;
  createdAt: string;
  updatedAt: string;
}

export interface ReviewStats {
  averageRating: number;
  totalReviews: number;
}

export interface ReviewPagination {
  currentPage: number;
  totalPages: number;
  totalReviews: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}

export interface ReviewsResponse {
  reviews: Review[];
  pagination: ReviewPagination;
  stats: ReviewStats;
}

export interface CreateReviewData {
  targetType: 'MenuItem' | 'Vendor';
  targetId: string;
  rating: number;
  comment: string;
  orderId: string;
}

export interface UpdateReviewData {
  rating?: number;
  comment?: string;
}

export interface OrderReviewItem {
  menuItemId: string;
  itemName: string;
  image: string;
  slug: string;
  quantity: number;
  selectedSubcategory: {
    title: string;
    quantity: string;
    price: number;
  };
  canReview: boolean;
  hasReviewed: boolean;
  existingReview?: Review;
}

export interface OrderReviewStatus {
  orderId: string;
  orderNumber: string;
  orderDate: string;
  items: OrderReviewItem[];
}
