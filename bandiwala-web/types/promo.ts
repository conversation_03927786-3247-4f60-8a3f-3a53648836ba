export interface PromoCodeValidationRequest {
  code: string;
  subtotal: number;
}

export interface PromoCodeValidationResponse {
  success: boolean;
  message: string;
  data?: {
    code: string;
    type: 'percentage' | 'fixed' | 'free_delivery';
    discountAmount: number;
    isFreeDelivery: boolean;
    remainingUses: number;
    maxUsagePerUser: number;
  };
}

export interface PromoCodeUsage {
  _id: string;
  user: string;
  promoCode: string;
  order: {
    _id: string;
    orderNumber: string;
    total: number;
    createdAt: string;
  };
  discountAmount: number;
  usedAt: string;
  createdAt: string;
  updatedAt: string;
}

export interface PromoCodeUsageResponse {
  success: boolean;
  data: PromoCodeUsage[];
}
