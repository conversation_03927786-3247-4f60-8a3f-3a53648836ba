'use client';

import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import api, { authService } from '@/services/api';

interface Coordinates {
  lat: number;
  lng: number;
}

interface Location {
  coordinates?: Coordinates;
  formattedAddress?: string;
}

interface User {
  _id: string;
  phone: string;
  name?: string;
  email?: string;
  address?: string;
  location?: Location;
  profileImage?: string;
  role?: string;
}

interface AuthContextType {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  login: (token: string, userData?: any) => Promise<void>;
  logout: () => void;
  updateUser: (userData: Partial<User>) => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  // Helper function to clean up user data
  const cleanUserData = (userData: any) => {
    if (!userData) return null;

    // Clean up email - remove placeholder emails
    let cleanEmail = userData.email || '';
    if (cleanEmail.includes('@bandiwala.com') || cleanEmail.includes('@example.com') || cleanEmail.includes('user-')) {
      cleanEmail = '';
    }

    // Clean up name - provide better default
    let cleanName = userData.name || '';
    if (cleanName === 'User' || cleanName.startsWith('user-') || cleanName.startsWith('User ') || !cleanName.trim()) {
      cleanName = '';
    }

    return {
      ...userData,
      email: cleanEmail,
      name: cleanName
    };
  };

  // Utility function to clear all auth data
  const clearAuthData = useCallback(() => {
    console.log('AuthContext: Clearing all auth data');
    setUser(null);
    setToken(null);
    localStorage.removeItem('auth_token');
    localStorage.removeItem('user_data');
    localStorage.removeItem('token_payload');
    if (api.defaults.headers) {
      delete api.defaults.headers.common['Authorization'];
    }
    document.cookie = 'token=; path=/; max-age=0';
  }, []);

  // Fetch user data from the backend
  const fetchUserData = useCallback(async (authToken: string) => {
    try {
      console.log('AuthContext: Fetching user data with token:', authToken.substring(0, 10) + '...');
      setIsLoading(true);
      setError(null);

      // Make sure we're using the latest token
      const currentToken = localStorage.getItem('auth_token');
      if (currentToken && currentToken !== authToken) {
        console.log('AuthContext: Token changed during fetch, using current token');
        authToken = currentToken;
      }

      try {
        // First try the backend API
        console.log('AuthContext: Trying to fetch user data from backend API');
        const data = await authService.getCurrentUser(authToken);
        console.log('AuthContext: User data received from backend:', data);

        if (!data.success) {
          console.error('AuthContext: Error response data from backend:', data);
          throw new Error(data.message || 'Failed to fetch user data from backend');
        }

        if (data.success && data.user) {
          console.log('AuthContext: Setting user data from backend:', data.user);
          // Only allow users with "user" role or no role (defaults to "user")
          if (!data.user.role || data.user.role === "user") {
            const cleanedUser = cleanUserData(data.user);
            setUser(cleanedUser);
            return;
          } else {
            console.log('AuthContext: User has non-user role:', data.user.role, 'Rejecting login');
            throw new Error('Access denied: This application is only for regular users');
          }
        } else {
          console.error('AuthContext: Invalid data format received from backend:', data);
          throw new Error('Failed to fetch user data from backend: Invalid response format');
        }
      } catch (backendError) {
        console.error('AuthContext: Error fetching from backend, trying fallback API:', backendError);

        // If backend fails, try our fallback API
        try {
          console.log('AuthContext: Trying fallback API at /api/me');
          const response = await fetch('/api/me', {
            headers: {
              'Authorization': `Bearer ${authToken}`
            }
          });

          if (!response.ok) {
            throw new Error(`Fallback API returned status ${response.status}`);
          }

          const data = await response.json();
          console.log('AuthContext: User data received from fallback API:', data);

          if (data.success && data.user) {
            console.log('AuthContext: Setting user data from fallback API:', data.user);
            // Only allow users with "user" role or no role (defaults to "user")
            if (!data.user.role || data.user.role === "user") {
              const cleanedUser = cleanUserData(data.user);
              setUser(cleanedUser);
              return;
            } else {
              console.log('AuthContext: User has non-user role:', data.user.role, 'Rejecting login');
              throw new Error('Access denied: This application is only for regular users');
            }
          } else {
            throw new Error('Invalid data format from fallback API');
          }
        } catch (fallbackError) {
          console.error('AuthContext: Fallback API also failed:', fallbackError);
          // Clear authentication data on failure
          clearAuthData();
          throw fallbackError;
        }
      }
    } catch (err) {
      console.error('Error fetching user data:', err);
      setError(err instanceof Error ? err.message : 'An unknown error occurred');

      // If we get a 401 Unauthorized or authentication failed, clear the token
      if (err instanceof Error && (err.message.includes('401') || err.message.includes('Authentication failed'))) {
        console.log('AuthContext: Authentication error, clearing token');
        clearAuthData();
      }
      // For other errors, don't clear the token as it might be a temporary server issue
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Initialize auth state from localStorage on mount and listen for changes
  useEffect(() => {
    // Function to load auth state from localStorage
    const loadAuthState = () => {
      const storedToken = localStorage.getItem('auth_token');
      const storedUserData = localStorage.getItem('user_data');

      console.log('AuthContext: Loading auth state, token:', storedToken ? 'Token exists' : 'No token');
      console.log('AuthContext: Loading auth state, user data:', storedUserData ? 'User data exists' : 'No user data');

      // Check if stored user data has non-user role and clear it
      if (storedUserData) {
        try {
          const userData = JSON.parse(storedUserData);
          if (userData.role && userData.role !== "user") {
            console.log('AuthContext: Found stored user with non-user role:', userData.role, 'Clearing all auth data');
            clearAuthData();
            setIsLoading(false);
            return;
          }
        } catch (error) {
          console.error('AuthContext: Error parsing stored user data during role check:', error);
        }
      }

      if (storedToken) {
        setToken(storedToken);
        // Set the token in the API headers
        if (api.defaults.headers) {
          api.defaults.headers.common['Authorization'] = `Bearer ${storedToken}`;
        }

        // Also set the token in document cookie for API routes
        document.cookie = `token=${storedToken}; path=/; max-age=604800; SameSite=Strict`;
        console.log('AuthContext: Token set in cookie for API routes');

        // If we have stored user data, set it immediately to avoid flashing login screen
        if (storedUserData) {
          try {
            const userData = JSON.parse(storedUserData);
            console.log('AuthContext: Setting user from stored data:', userData);
            // Only allow users with "user" role or no role (defaults to "user")
            if (!userData.role || userData.role === "user") {
              const cleanedUser = cleanUserData(userData);
              setUser(cleanedUser);
            } else {
              console.log('AuthContext: Stored user has non-user role:', userData.role, 'Clearing data');
              clearAuthData();
            }
          } catch (error) {
            console.error('AuthContext: Error parsing stored user data:', error);
          }
        }

        // Still fetch fresh user data from the backend
        fetchUserData(storedToken);
      } else {
        setToken(null);
        setUser(null);
        setIsLoading(false);
        // Clear the token from API headers
        if (api.defaults.headers) {
          delete api.defaults.headers.common['Authorization'];
        }
        // Clear the token cookie
        document.cookie = 'token=; path=/; max-age=0';
        // Clear stored user data
        localStorage.removeItem('user_data');
        localStorage.removeItem('token_payload');
      }
    };

    // Initial load
    loadAuthState();

    // Set up storage event listener to handle changes from other tabs/windows
    const handleStorageChange = (event: StorageEvent) => {
      if (event.key === 'auth_token') {
        console.log('AuthContext: auth_token changed in localStorage, reloading auth state');
        loadAuthState();
      }
    };

    window.addEventListener('storage', handleStorageChange);

    // Clean up
    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, [fetchUserData, clearAuthData]);

  // Login function
  const login = useCallback(async (authToken: string, userData?: any) => {
    try {
      // Validate token
      if (!authToken) {
        throw new Error('No authentication token provided');
      }

      console.log('AuthContext: Login called with token:', authToken.substring(0, 10) + '...');
      setIsLoading(true);
      setError(null);

      // Store token in state and localStorage
      setToken(authToken);
      localStorage.setItem('auth_token', authToken);
      console.log('AuthContext: Token stored in localStorage');

      // If we have user data from the login response, store it temporarily
      // This helps ensure we have user data even if fetchUserData fails
      if (userData) {
        console.log('AuthContext: Storing user data from login response:', userData);
        localStorage.setItem('user_data', JSON.stringify(userData));
      }

      // Try to decode the token to get user info
      try {
        const base64Payload = authToken.split('.')[1];
        const payload = JSON.parse(Buffer.from(base64Payload, 'base64').toString());
        console.log('AuthContext: Decoded token payload:', payload);

        // Store token payload in localStorage for fallback
        localStorage.setItem('token_payload', JSON.stringify(payload));

        // If we have user data from the token payload, store it temporarily
        // This helps ensure we have user data even if fetchUserData fails
        if (payload.id) {
          const tokenUser = {
            _id: payload.id,
            name: payload.name || (userData?.name || ''),
            email: payload.email || (userData?.email || ''),
            phone: payload.phone || (userData?.phone || ''),
            role: payload.role || 'user'
          };
          console.log('AuthContext: Storing user data from token payload:', tokenUser);
          localStorage.setItem('user_data', JSON.stringify(tokenUser));
        }
      } catch (decodeError) {
        console.error('AuthContext: Error decoding token:', decodeError);
      }

      // Set the token in the API headers
      if (api.defaults.headers) {
        api.defaults.headers.common['Authorization'] = `Bearer ${authToken}`;
        console.log('AuthContext: Token set in API headers');
      }

      // Also set the token in document cookie for API routes
      document.cookie = `token=${authToken}; path=/; max-age=604800; SameSite=Strict`;
      console.log('AuthContext: Token set in cookie for API routes');

      // Fetch user data
      await fetchUserData(authToken);
      console.log('AuthContext: Login complete, isAuthenticated should be true');
    } catch (err) {
      console.error('Login error:', err);
      setError(err instanceof Error ? err.message : 'Failed to login');

      // Try to use stored user data as fallback
      const storedUserData = localStorage.getItem('user_data');
      if (storedUserData) {
        try {
          const userData = JSON.parse(storedUserData);
          console.log('AuthContext: Using stored user data as fallback:', userData);
          setUser(userData);
        } catch (parseError) {
          console.error('AuthContext: Error parsing stored user data:', parseError);

          // Clear token on login error if we couldn't use fallback
          setToken(null);
          localStorage.removeItem('auth_token');
          localStorage.removeItem('user_data');
          localStorage.removeItem('token_payload');
          delete api.defaults.headers.common['Authorization'];
          // Clear the token cookie
          document.cookie = 'token=; path=/; max-age=0';
          console.log('AuthContext: Login failed, cleared token');
        }
      } else {
        // Clear token on login error
        setToken(null);
        localStorage.removeItem('auth_token');
        localStorage.removeItem('user_data');
        localStorage.removeItem('token_payload');
        delete api.defaults.headers.common['Authorization'];
        // Clear the token cookie
        document.cookie = 'token=; path=/; max-age=0';
        console.log('AuthContext: Login failed, cleared token');
      }
    } finally {
      setIsLoading(false);
    }
  }, [fetchUserData]);

  // Logout function
  const logout = useCallback(async () => {
    console.log('AuthContext: Logout called');

    try {
      // Use the authService to logout
      await authService.logout();
      console.log('AuthContext: Backend logout successful');
    } catch (error) {
      console.error('AuthContext: Error during backend logout:', error);
    }

    // Clear auth state
    setUser(null);
    setToken(null);

    // Remove token and user data from localStorage
    localStorage.removeItem('auth_token');
    localStorage.removeItem('user_data');
    localStorage.removeItem('token_payload');
    console.log('AuthContext: Token and user data removed from localStorage');

    // Remove token from API headers
    if (api.defaults.headers) {
      delete api.defaults.headers.common['Authorization'];
      console.log('AuthContext: Token removed from API headers');
    }

    // Clear the token cookie
    document.cookie = 'token=; path=/; max-age=0';
    console.log('AuthContext: Token removed from cookie');

    // Redirect to home page
    router.push('/');
    console.log('AuthContext: Redirected to home page');
  }, [router]);

  // Update user data
  const updateUser = useCallback(async (userData: Partial<User>) => {
    try {
      console.log('AuthContext: Updating user data:', userData);
      setIsLoading(true);
      setError(null);

      if (!token) {
        throw new Error('Not authenticated');
      }

      try {
        // First try the backend API
        console.log('AuthContext: Trying to update user data via backend API');
        const data = await authService.updateUserProfile(userData, token);
        console.log('AuthContext: Update user response data from backend:', data);

        if (data.success && data.user) {
          console.log('AuthContext: Setting updated user data from backend:', data.user);
          // Only allow users with "user" role or no role (defaults to "user")
          if (!data.user.role || data.user.role === "user") {
            const cleanedUser = cleanUserData(data.user);
            setUser(cleanedUser);
            return;
          } else {
            console.log('AuthContext: Updated user has non-user role:', data.user.role, 'Rejecting update');
            throw new Error('Access denied: This application is only for regular users');
          }
        } else {
          throw new Error(data.message || 'Failed to update user data via backend');
        }
      } catch (backendError) {
        console.error('AuthContext: Error updating via backend, trying fallback:', backendError);

        // If backend fails, try our fallback approach - update locally
        try {
          console.log('AuthContext: Using fallback approach - updating user locally');

          // If we have a user, update it locally
          if (user) {
            const updatedUser = {
              ...user,
              ...userData,
              updatedAt: new Date()
            };

            console.log('AuthContext: Setting updated user data locally:', updatedUser);
            const cleanedUser = cleanUserData(updatedUser);
            setUser(cleanedUser);

            // Try to update the user in our fallback API
            try {
              const response = await fetch('/api/me', {
                method: 'PUT',
                headers: {
                  'Authorization': `Bearer ${token}`,
                  'Content-Type': 'application/json'
                },
                body: JSON.stringify(userData)
              });

              console.log('AuthContext: Fallback API update response:', response.status);
            } catch (fallbackApiError) {
              console.error('AuthContext: Failed to update user in fallback API:', fallbackApiError);
              // Continue anyway since we've updated locally
            }

            return;
          } else {
            throw new Error('No user data available to update');
          }
        } catch (fallbackError) {
          console.error('AuthContext: Fallback update approach also failed:', fallbackError);
          throw fallbackError;
        }
      }
    } catch (err) {
      console.error('Error updating user data:', err);
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
    } finally {
      setIsLoading(false);
    }
  }, [token, user]);

  // Calculate isAuthenticated value
  const isAuthenticated = !!token && !!user;

  // Log authentication state changes
  useEffect(() => {
    console.log('AuthContext: Authentication state updated:', {
      isAuthenticated,
      hasToken: !!token,
      hasUser: !!user,
      userData: user
    });
  }, [isAuthenticated, token, user]);

  return (
    <AuthContext.Provider value={{
      user,
      token,
      isAuthenticated,
      isLoading,
      error,
      login,
      logout,
      updateUser
    }}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
