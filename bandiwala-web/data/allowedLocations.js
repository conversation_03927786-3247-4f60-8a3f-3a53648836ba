/**
 * @typedef {Object} Coordinates
 * @property {number} lat
 * @property {number} lng
 */

// List of allowed locations with their coordinates
// These are example locations - replace with your actual service areas
const allowedLocations = [
  { lat: 17.3850, lng: 78.4867 }, // Hyderabad
];

// Maximum allowed distance in kilometers
export const MAX_ALLOWED_DISTANCE = 5;

export default allowedLocations;