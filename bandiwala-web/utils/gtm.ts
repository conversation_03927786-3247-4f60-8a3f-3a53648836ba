// Type definitions for Google Tag Manager
interface GTMDataLayer {
  push(data: GTMEvent): void;
}

interface GTMWindow extends Window {
  dataLayer?: GTMDataLayer;
  gtag?: (...args: unknown[]) => void;
}

// GTM Event types
interface GTMEvent {
  event: string;
  [key: string]: unknown;
}

interface GTMItem {
  item_id: string | number;
  item_name: string;
  price: number;
  quantity: number;
  category?: string;
  brand?: string;
}

interface GTMCartData {
  total: number;
  items: GTMItem[];
  currency?: string;
}

// Generic GTM event function
export const gtmEvent = (event: string, params: Record<string, unknown> = {}): void => {
  if (typeof window !== 'undefined') {
    const gtmWindow = window as GTMWindow;

    // Support both GTM dataLayer and GA4 gtag
    if (gtmWindow.dataLayer) {
      gtmWindow.dataLayer.push({ event, ...params });
    } else if (gtmWindow.gtag) {
      gtmWindow.gtag('event', event, params);
    }
  }
};

export const gtmAddToCart = (item: GTMItem): void => {
  gtmEvent('add_to_cart', {
    item_id: item.item_id,
    item_name: item.item_name,
    price: item.price,
    quantity: item.quantity,
    category: item.category,
    brand: item.brand,
  });
};

export const gtmBeginCheckout = (cart: GTMCartData): void => {
  gtmEvent('begin_checkout', {
    value: cart.total,
    currency: cart.currency || 'INR',
    items: cart.items,
  });
};

export const gtmPurchase = (transactionId: string, cart: GTMCartData): void => {
  gtmEvent('purchase', {
    transaction_id: transactionId,
    value: cart.total,
    currency: cart.currency || 'INR',
    items: cart.items,
  });
};

export const gtmViewItem = (item: GTMItem): void => {
  gtmEvent('view_item', {
    item_id: item.item_id,
    item_name: item.item_name,
    price: item.price,
    category: item.category,
    brand: item.brand,
  });
};

export const gtmRemoveFromCart = (item: GTMItem): void => {
  gtmEvent('remove_from_cart', {
    item_id: item.item_id,
    item_name: item.item_name,
    price: item.price,
    quantity: item.quantity,
    category: item.category,
    brand: item.brand,
  });
};

export const gtmSearch = (searchTerm: string): void => {
  gtmEvent('search', {
    search_term: searchTerm,
  });
};

export const gtmLogin = (method: string = 'email'): void => {
  gtmEvent('login', {
    method,
  });
};

export const gtmSignUp = (method: string = 'email'): void => {
  gtmEvent('sign_up', {
    method,
  });
};

export type { GTMItem, GTMCartData, GTMEvent, GTMWindow };
