/**
 * Utility functions for handling image URLs
 */

// The base URL for the API server that serves images
const API_SERVER_URL = process.env.NEXT_PUBLIC_API_URL;

const DEFAULT_IMAGE = '/images/vendor.jpeg';

console.log('Image Utils: Using API_SERVER_URL:', API_SERVER_URL);

const PROBLEMATIC_PATTERNS = [
  'Jai%20Bhavani%20Chat%20',
  'Jai Bhavani Chat ',
  'rajamandri.jpeg',
  'sangamesh.jpeg',
  'bandiwala-items-pics/items/Jai%20Bhavani%20Chat%20',
  'bandiwala-items-pics/items/Jai Bhavani Chat ',
  '/bandiwala-items-pics/vendors/rajamandri.jpeg',
  '/bandiwala-items-pics/vendors/sangamesh.jpeg',
  'bandiwala-items-pics/vendors/',
  'bandiwala-items-pics/items/',
  'Lkashmi%20bahavani%20fast%20food',
  'Lkashmi bahavani fast food',
  '%20', // Any URL-encoded spaces
  'bahavani', // Common misspelling
  'manchurian',
  'noodles',
  'fried%20rice',
  'fried rice'
];

/**
 * Converts a relative image path to an absolute URL
 * @param imagePath The relative image path (e.g., "/images/vendor1.jpg")
 * @returns The absolute URL for the image
 */
export const getImageUrl = (imagePath: string): string => {
  console.log('getImageUrl called with:', imagePath);

  // If the path is already an absolute URL, return it as is
  if (imagePath && (imagePath.startsWith('http://') || imagePath.startsWith('https://'))) {
    console.log('Already absolute URL, returning as is');
    return imagePath;
  }
  if (!imagePath || imagePath === 'undefined' || imagePath === 'null') {
    console.log('Invalid path, using default image');
    return `${API_SERVER_URL}${DEFAULT_IMAGE}`;
  }
  const normalizedPath = imagePath.startsWith('/') ? imagePath : `/${imagePath}`;
  console.log('Normalized path:', normalizedPath);
  let encodedPath;
  if (normalizedPath.includes(' ') || normalizedPath.includes('(') || normalizedPath.includes(')')) {
    console.log('Path contains spaces/special chars, encoding segments');
    encodedPath = normalizedPath.split('/').map(segment => {
      return segment === '' ? '' : encodeURIComponent(segment);
    }).join('/');
    console.log('Encoded path:', encodedPath);
  } else {
    encodedPath = encodeURI(normalizedPath);
    console.log('Simple encoding:', encodedPath);
  }
  const finalUrl = `${API_SERVER_URL}${encodedPath}`;
  console.log('Final URL:', finalUrl);
  return finalUrl;
};

/**
 * Gets a safe fallback image based on the type
 * @param fallbackType The type of fallback image needed ('food' or 'vendor')
 * @returns The path to a safe fallback image
 */
export const getSafeFallbackImage = (fallbackType: 'food' | 'vendor' = 'food'): string => {
  const fallbackImages = {
    food: '/images/vendor.jpeg',
    vendor: '/images/default-vendor.jpg'
  };

  return fallbackImages[fallbackType] || fallbackImages.food;
};

/**
 * Checks if an image path contains problematic patterns that might cause loading issues
 * @param imagePath The image path to check
 * @returns True if the path contains problematic patterns
 */
export const isProblematicImage = (imagePath: string): boolean => {
  if (!imagePath) return false;

  return PROBLEMATIC_PATTERNS.some(pattern =>
    imagePath.includes(pattern)
  );
};

/**
 * Gets the appropriate loading strategy for an image
 * @param imagePath The image path
 * @returns Object with loading strategy properties
 */
export const getImageLoadingStrategy = (imagePath: string) => {
  // For any image that's not a local static image, disable optimization
  const isLocalStaticImage = imagePath && imagePath.startsWith('/images/') && !imagePath.includes('bandiwala-items-pics');

  // Always disable optimization for non-local images to avoid 402 errors
  const shouldDisableOptimization = !isLocalStaticImage;

  return {
    unoptimized: shouldDisableOptimization,
    priority: false,
    loading: 'lazy' as const,
    // Only use blur placeholder for local static images
    placeholder: isLocalStaticImage ? 'blur' as const : undefined,
    blurDataURL: isLocalStaticImage ? 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q==' : undefined
  };
};