import { MetadataRoute } from 'next';

const baseUrl = 'https://www.bandiwala.co.in';

// Array of static routes
const staticRoutes = [
  {
    url: `${baseUrl}/`,
    lastModified: new Date(),
    changeFreq: 'daily',
    priority: 1.0
  },
  {
    url: `${baseUrl}/login`,
    lastModified: new Date(),
    changeFreq: 'monthly',
    priority: 0.9
  },
  {
    url: `${baseUrl}/dishes`,
    lastModified: new Date(),
    changeFreq: 'weekly',
    priority: 0.9
  },
  {
    url: `${baseUrl}/vendors`,
    lastModified: new Date(),
    changeFreq: 'weekly',
    priority: 0.9
  },
  {
    url: `${baseUrl}/cart`,
    lastModified: new Date(),
    changeFreq: 'daily',
    priority: 0.8
  },
  {
    url: `${baseUrl}/about-us`,
    lastModified: new Date(),
    changeFreq: 'monthly',
    priority: 0.8
  },
  {
    url: `${baseUrl}/contact`,
    lastModified: new Date(),
    changeFreq: 'monthly',
    priority: 0.7
  },
  {
    url: `${baseUrl}/terms`,
    lastModified: new Date(),
    changeFreq: 'yearly',
    priority: 0.5
  }
];

// Function to generate sitemap.xml
export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  // Dynamically generate sitemap entries for vendors
  // Temporarily using static vendor slugs - in production, fetch from API
  const vendorSlugs = [
    'alis-street-food',
    'mayas-home-kitchen',
    'street-wok-masters',
    'biryani-house'
  ];

  const vendorRoutes = vendorSlugs.map(slug => ({
    url: `https://www.bandiwala.co.in/vendors/${slug}`,
    lastModified: new Date(),
    changeFreq: 'weekly' as const,
    priority: 0.9
  }));

  // Combine static and dynamic routes
  return [...staticRoutes, ...vendorRoutes];
}