import { cartService } from '../services/api';
import { Cart } from '../types/cart';

const STORAGE_KEY = 'bandiwala-cart';

export const cartSync = {
  async syncWithServer(localCart: Cart): Promise<Cart> {
    try {
      console.log('Syncing cart with server...');

      // Validate the local cart before syncing
      if (!localCart || !Array.isArray(localCart.items)) {
        console.error('Invalid local cart format:', localCart);
        throw new Error('Invalid local cart format');
      }

      // Filter out invalid items
      const validItems = localCart.items.filter(item =>
        item &&
        item.menuItemId &&
        typeof item.quantity === 'number' &&
        item.quantity > 0
      );

      if (validItems.length !== localCart.items.length) {
        console.warn(`Filtered out ${localCart.items.length - validItems.length} invalid items from local cart`);
      }

      // Clear server cart first
      try {
        await cartService.clearCart();
        console.log('Server cart cleared successfully');
      } catch (clearError) {
        console.error('Failed to clear server cart:', clearError);
        // Continue with sync even if clear fails
      }

      // Add items one by one with error handling for each item
      const syncErrors: Array<{item: any, error: any}> = [];
      for (const item of validItems) {
        try {
          console.log('Adding item to server cart:', item);
          await cartService.addToCart(item.menuItemId, item.quantity, item.notes);
        } catch (itemError) {
          console.error(`Failed to add item ${item.menuItemId} to server cart:`, itemError);
          syncErrors.push({ item, error: itemError });
          // Continue with next item
        }
      }

      // Get final server state
      console.log('Getting updated cart from server...');
      const response = await cartService.getCart();
      console.log('Server cart after sync:', response.data);

      // If there were sync errors, log them
      if (syncErrors.length > 0) {
        console.warn(`${syncErrors.length} items failed to sync with server`);
      }

      return response.data;
    } catch (error) {
      console.error('Failed to sync cart with server:', error);
      return localCart;
    }
  },

  getLocalCart(): Cart {
    if (typeof window === 'undefined') {
      return { userId: '', items: [] };
    }

    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      if (!stored) {
        return { userId: '', items: [] };
      }

      let cart: Cart;
      try {
        cart = JSON.parse(stored);
      } catch (parseError) {
        console.error('Error parsing stored cart:', parseError);
        return { userId: '', items: [] };
      }

      // Validate cart structure
      if (!cart || !Array.isArray(cart.items)) {
        console.error('Invalid cart structure in localStorage:', cart);
        return { userId: '', items: [] };
      }

      // Filter out invalid items
      const validItems = cart.items.filter(item =>
        item &&
        item.menuItemId &&
        typeof item.quantity === 'number' &&
        item.quantity > 0
      );

      if (validItems.length !== cart.items.length) {
        console.warn(`Filtered out ${cart.items.length - validItems.length} invalid items from stored cart`);
      }

      cart.items = validItems;

      // Enhance cart items with stored item details if available
      if (cart.items.length > 0) {
        cart.items = cart.items.map(item => {
          try {
            const storedItemDetails = localStorage.getItem(`item-details-${item.menuItemId}`);
            if (storedItemDetails) {
              const details = JSON.parse(storedItemDetails);
              return {
                ...item,
                name: item.name === 'Loading...' ? details.name : item.name,
                price: item.price === 0 ? details.price : item.price,
                image: item.image === '/placeholder.jpg' ? details.image : item.image,
                vendorId: item.vendorId || details.vendorId,
                vendorName: item.vendorName || details.vendorName
              };
            }
          } catch (error) {
            console.error('Error enhancing cart item with stored details:', error);
          }
          return item;
        });
      }

      return cart;
    } catch (error) {
      console.error('Error getting local cart:', error);
      return { userId: 'guest-user', items: [] };
    }
  },

  saveLocalCart(cart: Cart): void {
    if (typeof window === 'undefined') {
      return;
    }

    try {
      // Validate cart before saving
      if (!cart) {
        console.error('Attempted to save invalid cart:', cart);
        return;
      }

      // Ensure cart has the correct structure
      const safeCart: Cart = {
        userId: cart.userId || '',
        items: Array.isArray(cart.items) ? cart.items : [],
        _id: cart._id
      };

      // Filter out invalid items
      safeCart.items = safeCart.items.filter(item =>
        item &&
        item.menuItemId &&
        typeof item.quantity === 'number' &&
        item.quantity > 0
      );

      localStorage.setItem(STORAGE_KEY, JSON.stringify(safeCart));
    } catch (error) {
      console.error('Error saving local cart:', error);
    }
  }
};