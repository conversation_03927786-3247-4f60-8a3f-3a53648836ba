import { cartSync } from './cartSync';
import { cartService } from '../services/api';

export class NetworkMonitor {
  private static instance: NetworkMonitor;
  private isOnline: boolean = true;
  private checkInterval: NodeJS.Timeout | null = null;

  private constructor() {
    if (typeof window !== 'undefined') {
      window.addEventListener('online', this.handleOnline);
      window.addEventListener('offline', this.handleOffline);
      this.startHealthCheck();
    }
  }

  static getInstance(): NetworkMonitor {
    if (!NetworkMonitor.instance) {
      NetworkMonitor.instance = new NetworkMonitor();
    }
    return NetworkMonitor.instance;
  }

  private handleOnline = async () => {
    this.isOnline = true;
    try {
      const localCart = cartSync.getLocalCart();
      if (localCart.items.length > 0) {
        await cartSync.syncWithServer(localCart);
      }
    } catch (error) {
      console.error('Failed to sync cart after coming online:', error);
    }
  };

  private handleOffline = () => {
    this.isOnline = false;
  };

  private startHealthCheck() {
    this.checkInterval = setInterval(async () => {
      try {
        // Pass empty token to avoid using any cached token
        await cartService.getCart();
        if (!this.isOnline) {
          this.handleOnline();
        }
      } catch (_error) {
        if (this.isOnline) {
          this.handleOffline();
        }
      }
    }, 30000); // Check every 30 seconds
  }

  public cleanup() {
    if (typeof window !== 'undefined') {
      window.removeEventListener('online', this.handleOnline);
      window.removeEventListener('offline', this.handleOffline);
      if (this.checkInterval) {
        clearInterval(this.checkInterval);
      }
    }
  }
}