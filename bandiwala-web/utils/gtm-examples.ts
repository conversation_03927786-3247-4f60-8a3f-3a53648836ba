import { 
  gtmAddToCart, 
  gtmBeginCheckout, 
  gtmPurchase, 
  gtmViewItem, 
  gtmRemoveFromCart, 
  gtmSearch, 
  gtmLogin, 
  gtmSignUp,
  type GTMItem,
  type GTMCartData 
} from './gtm';

export const handleAddToCartExample = (dishId: string, dishName: string, price: number, vendorName?: string) => {
  const item: GTMItem = {
    item_id: dishId,
    item_name: dishName,
    price: price,
    quantity: 1,
    category: 'food',
    brand: vendorName
  };
  
  gtmAddToCart(item);
};

export const handleViewDishExample = (dishId: string, dishName: string, price: number, vendorName?: string) => {
  const item: GTMItem = {
    item_id: dishId,
    item_name: dishName,
    price: price,
    quantity: 1,
    category: 'food',
    brand: vendorName
  };
  
  gtmViewItem(item);
};

export const handleCheckoutStartExample = (cartItems: any[], totalAmount: number) => {
  const gtmItems: GTMItem[] = cartItems.map(item => ({
    item_id: item.id || item._id,
    item_name: item.name,
    price: item.price,
    quantity: item.quantity,
    category: 'food',
    brand: item.vendorName
  }));

  const cartData: GTMCartData = {
    total: totalAmount,
    items: gtmItems,
    currency: 'INR'
  };
  
  gtmBeginCheckout(cartData);
};

export const handlePurchaseExample = (orderId: string, cartItems: any[], totalAmount: number) => {
  const gtmItems: GTMItem[] = cartItems.map(item => ({
    item_id: item.id || item._id,
    item_name: item.name,
    price: item.price,
    quantity: item.quantity,
    category: 'food',
    brand: item.vendorName
  }));

  const cartData: GTMCartData = {
    total: totalAmount,
    items: gtmItems,
    currency: 'INR'
  };
  
  gtmPurchase(orderId, cartData);
};

export const handleSearchExample = (searchQuery: string) => {
  gtmSearch(searchQuery);
};

export const handleLoginExample = (loginMethod: 'email' | 'phone' | 'google' = 'email') => {
  gtmLogin(loginMethod);
};

export const handleSignUpExample = (signUpMethod: 'email' | 'phone' | 'google' = 'email') => {
  gtmSignUp(signUpMethod);
};

export const handleRemoveFromCartExample = (dishId: string, dishName: string, price: number, quantity: number, vendorName?: string) => {
  const item: GTMItem = {
    item_id: dishId,
    item_name: dishName,
    price: price,
    quantity: quantity,
    category: 'food',
    brand: vendorName
  };
  
  gtmRemoveFromCart(item);
};
