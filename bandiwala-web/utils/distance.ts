interface Coordinates {
  lat: number;
  lng: number;
}

/**
 * Convert degrees to radians
 * @param degrees Angle in degrees
 * @returns Angle in radians
 */
function toRadians(degrees: number): number {
  return degrees * (Math.PI / 180);
}

/**
 * Calculate the distance between two coordinates using the Haversine formula
 * @param coords1 First coordinates
 * @param coords2 Second coordinates
 * @returns Distance in kilometers
 */
export function calculateDistance(coords1: Coordinates, coords2: Coordinates): number {
  const R = 6371; // Earth's radius in kilometers
  const dLat = toRadians(coords2.lat - coords1.lat);
  const dLng = toRadians(coords2.lng - coords1.lng);

  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(toRadians(coords1.lat)) * Math.cos(toRadians(coords2.lat)) *
    Math.sin(dLng / 2) * Math.sin(dLng / 2);

  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c;
}

/**
 * Service area center coordinates for order delivery
 */
export const SERVICE_AREA_CENTER: Coordinates = {
  lat: 17.49328,
  lng: 78.39433
};

/**
 * Maximum delivery radius in kilometers (1.001 km = 1001 meters)
 */
export const SERVICE_AREA_RADIUS_KM = 2.001;

/**
 * Check if coordinates are within the delivery service area
 * @param userCoords User's coordinates
 * @returns True if within delivery area, false otherwise
 */
export function isWithinDeliveryArea(userCoords: Coordinates): boolean {
  const distance = calculateDistance(userCoords, SERVICE_AREA_CENTER);
  return distance <= SERVICE_AREA_RADIUS_KM;
}

/**
 * Get the distance from user location to service area center
 * @param userCoords User's coordinates
 * @returns Distance in kilometers
 */
export function getDistanceFromServiceCenter(userCoords: Coordinates): number {
  return calculateDistance(userCoords, SERVICE_AREA_CENTER);
}

/**
 * Check if coordinates are within a specified distance from a center point
 * @param userCoords User's coordinates
 * @param centerCoords Center coordinates (defaults to SERVICE_AREA_CENTER if not provided)
 * @param radiusKm Maximum allowed distance in kilometers
 * @returns True if within allowed distance, false otherwise
 */
export function isWithinAllowedDistance(
  userCoords: Coordinates,
  centerCoords?: Coordinates,
  radiusKm: number = SERVICE_AREA_RADIUS_KM
): boolean {
  const center = centerCoords || SERVICE_AREA_CENTER;
  const distance = calculateDistance(userCoords, center);
  return distance <= radiusKm;
}