# Google Analytics (GA4) Integration for Bandiwala

## Overview
This project uses Google Analytics 4 (GA4) for tracking user interactions and e-commerce events. The GTM utility functions have been fixed and improved to work seamlessly with your existing GA4 setup.

## Current GA4 Setup

### ✅ What's Configured

1. **GA4 Script in Layout** (`app/layout.tsx`)
   ```tsx
   {/* Google Analytics GA4 */}
   <script async src={`https://www.googletagmanager.com/gtag/js?id=${process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID}`}></script>
   <script dangerouslySetInnerHTML={{
     __html: `window.dataLayer = window.dataLayer || [];
       function gtag(){dataLayer.push(arguments);}
       gtag('js', new Date());
       gtag('config', '${process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID}');`
   }} />
   ```

2. **Environment Variable** (`.env`)
   ```bash
   NEXT_PUBLIC_GA_MEASUREMENT_ID=G-xxxxx
   ```

3. **Fixed GTM Utility** (`utils/gtm.ts`)
   - ✅ TypeScript strict mode compliant
   - ✅ Automatically detects and uses GA4 `gtag` function
   - ✅ Proper type definitions for all events
   - ✅ Ready for e-commerce tracking

## How GA4 Works with This Project

The `gtm.ts` utility automatically detects your GA4 setup and sends events using the `gtag` function:

```typescript
// The utility checks for both GTM dataLayer and GA4 gtag
export const gtmEvent = (event: string, params: Record<string, unknown> = {}): void => {
  if (typeof window !== 'undefined') {
    const gtmWindow = window as GTMWindow;

    // Uses GA4 gtag since you don't have GTM dataLayer
    if (gtmWindow.dataLayer) {
      gtmWindow.dataLayer.push({ event, ...params });
    } else if (gtmWindow.gtag) {
      gtmWindow.gtag('event', event, params); 
    }
  }
};

## Usage Examples with GA4

### Track Add to Cart
```tsx
import { gtmAddToCart } from '@/utils/gtm';

const handleAddToCart = (dish: any) => {

  // This will send to GA4 using gtag('event', 'add_to_cart', {...})
  gtmAddToCart({
    item_id: dish.id,
    item_name: dish.name,
    price: dish.price,
    quantity: 1,
    category: 'food',
    brand: dish.vendorName
  });
};
```

### Track Purchase
```tsx
import { gtmPurchase } from '@/utils/gtm';

const handleOrderComplete = (orderId: string, cartItems: any[], total: number) => {

  gtmPurchase(orderId, {
    total: total,
    items: cartItems.map(item => ({
      item_id: item.id,
      item_name: item.name,
      price: item.price,
      quantity: item.quantity,
      category: 'food',
      brand: item.vendorName
    })),
    currency: 'INR'
  });
};
```

### Track Search
```tsx
import { gtmSearch } from '@/utils/gtm';

const handleSearch = (query: string) => {
  // This will send to GA4 using gtag('event', 'search', {...})
  gtmSearch(query);
};
```

## Available GA4 Event Functions

All these functions automatically send events to your GA4 property:

- `gtmAddToCart(item)` - Tracks add_to_cart events
- `gtmRemoveFromCart(item)` - Tracks remove_from_cart events
- `gtmBeginCheckout(cart)` - Tracks begin_checkout events
- `gtmPurchase(transactionId, cart)` - Tracks purchase events
- `gtmViewItem(item)` - Tracks view_item events
- `gtmSearch(searchTerm)` - Tracks search events
- `gtmLogin(method)` - Tracks login events
- `gtmSignUp(method)` - Tracks sign_up events
- `gtmEvent(event, params)` - Generic event tracking

## How to Verify GA4 Events

1. **Browser Developer Tools**
   - Open Network tab
   - Look for requests to `google-analytics.com/g/collect`
   - Check if events are being sent with correct parameters

2. **GA4 Real-time Reports**
   - Go to your GA4 property
   - Navigate to Reports > Real-time
   - Perform actions on your site and see events appear

3. **GA4 DebugView** (Recommended)
   - Install Google Analytics Debugger extension
   - Enable debug mode in your browser
   - See detailed event information in GA4 DebugView

## TypeScript Support

All functions are fully typed:

```tsx
import type { GTMItem, GTMCartData, GTMEvent } from '@/utils/gtm';

// Example with proper typing
const item: GTMItem = {
  item_id: 'dish_123',
  item_name: 'Butter Chicken',
  price: 299,
  quantity: 1,
  category: 'food',
  brand: 'Vendor Name'
};
```