import { Cart, CartItem } from '@/types/cart';

interface CartStateChange {
  type: 'ADD' | 'UPDATE' | 'REMOVE' | 'CLEAR';
  timestamp: number;
  menuItemId?: string;
  quantity?: number;
  notes?: string;
}

export class CartStateTracker {
  private static instance: CartStateTracker;
  private changes: CartStateChange[] = [];
  private lastSyncTime: number = Date.now();

  private constructor() {}

  static getInstance(): CartStateTracker {
    if (!CartStateTracker.instance) {
      CartStateTracker.instance = new CartStateTracker();
    }
    return CartStateTracker.instance;
  }

  trackChange(change: Omit<CartStateChange, 'timestamp'>) {
    this.changes.push({
      ...change,
      timestamp: Date.now()
    });
  }

  hasPendingChanges(): boolean {
    return this.changes.length > 0;
  }

  getLastChangeTime(): number {
    if (this.changes.length === 0) return this.lastSyncTime;
    return Math.max(...this.changes.map(c => c.timestamp));
  }

  clearChanges() {
    this.changes = [];
    this.lastSyncTime = Date.now();
  }

  getPendingChanges(): CartStateChange[] {
    return [...this.changes];
  }
}