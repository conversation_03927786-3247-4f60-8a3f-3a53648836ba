'use client';

import { useEffect, useRef, useState } from 'react';
import { Loader } from '@googlemaps/js-api-loader';
import { Button } from '@/components/ui/button';
import { Navigation } from 'lucide-react';
import { isWithinDeliveryArea } from '@/utils/distance';

export interface Coordinates {
  lat: number;
  lng: number;
}

export interface PickableMapProps {
  initialCoordinates?: Coordinates;
  onCoordinatesChange: (coords: Coordinates) => void;
  height?: string;
  width?: string;
  zoom?: number;
  showCurrentLocationButton?: boolean;
  onGetCurrentLocation?: () => void;
  isLoading?: boolean;
  showServiceAreaValidation?: boolean;
}

export default function PickableMap({
  initialCoordinates = { lat: 17.3850, lng: 78.4867 },
  onCoordinatesChange,
  height = '400px',
  width = '100%',
  zoom = 13,
  showCurrentLocationButton = true,
  onGetCurrentLocation,
  isLoading = false,
  showServiceAreaValidation = true,
}: PickableMapProps) {
  const mapRef = useRef<HTMLDivElement>(null);
  const [mapInstance, setMapInstance] = useState<google.maps.Map | null>(null);
  const [marker, setMarker] = useState<google.maps.Marker | null>(null);
  const [mapLoaded, setMapLoaded] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isWithinServiceArea, setIsWithinServiceArea] = useState(true);

  // Initialize the map
  useEffect(() => {
    if (!mapRef.current) return;

    const apiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_KEY;
    if (!apiKey) {
      setError('Google Maps API key is missing');
      return;
    }

    const loader = new Loader({
      apiKey,
      version: 'weekly',
    });

    loader
      .load()
      .then(() => {
        const map = new google.maps.Map(mapRef.current!, {
          center: initialCoordinates,
          zoom,
          mapTypeControl: false,
          streetViewControl: false,
          fullscreenControl: false,
        });

        const newMarker = new google.maps.Marker({
          position: initialCoordinates,
          map,
          draggable: true,
          animation: google.maps.Animation.DROP,
        });

        setMapInstance(map);
        setMarker(newMarker);
        setMapLoaded(true);

        // Notify parent of initial coordinates
        onCoordinatesChange(initialCoordinates);
      })
      .catch((err) => {
        setError(`Failed to load Google Maps: ${err.message}`);
      });
  }, [initialCoordinates, zoom, onCoordinatesChange]);

  // Set up event listeners once map and marker are loaded
  useEffect(() => {
    if (!mapInstance || !marker || !mapLoaded) return;

    // Handle map click
    const clickListener = mapInstance.addListener('click', (e: google.maps.MapMouseEvent) => {
      if (!e.latLng) return;

      const newPosition = {
        lat: e.latLng.lat(),
        lng: e.latLng.lng(),
      };

      // Check if location is within delivery area
      if (showServiceAreaValidation) {
        const withinArea = isWithinDeliveryArea(newPosition);
        setIsWithinServiceArea(withinArea);

        // Update marker color based on service area (but keep it green for now)
        marker.setIcon(undefined); // Always use default green marker
      }

      marker.setPosition(newPosition);
      onCoordinatesChange(newPosition);
    });

    // Handle marker drag end
    const dragEndListener = marker.addListener('dragend', () => {
      const position = marker.getPosition();
      if (!position) return;

      const newPosition = {
        lat: position.lat(),
        lng: position.lng(),
      };

      // Check if location is within delivery area
      if (showServiceAreaValidation) {
        const withinArea = isWithinDeliveryArea(newPosition);
        setIsWithinServiceArea(withinArea);

        // Update marker color based on service area (but keep it green for now)
        marker.setIcon(undefined); // Always use default green marker
      }

      onCoordinatesChange(newPosition);
    });

    // Cleanup listeners
    return () => {
      google.maps.event.removeListener(clickListener);
      google.maps.event.removeListener(dragEndListener);
    };
  }, [mapInstance, marker, mapLoaded, onCoordinatesChange]);

  // Update marker position if initialCoordinates change
  useEffect(() => {
    if (!marker || !mapInstance) return;

    marker.setPosition(initialCoordinates);
    mapInstance.panTo(initialCoordinates);

    // Check delivery area for the new position
    if (showServiceAreaValidation) {
      const withinArea = isWithinDeliveryArea(initialCoordinates);
      setIsWithinServiceArea(withinArea);

      // Update marker color based on service area (but keep it green for now)
      marker.setIcon(undefined); // Always use default green marker
    }
  }, [initialCoordinates, marker, mapInstance, showServiceAreaValidation]);

  return (
    <div className="relative w-full" style={{ height }}>
      {error && (
        <div className="absolute top-0 left-0 right-0 z-10 p-2 text-white bg-red-600 rounded-t-md">
          {error}
        </div>
      )}

      {/* Temporarily disabled service area warning */}
      {false && showServiceAreaValidation && !isWithinServiceArea && mapLoaded && (
        <div className="absolute top-0 left-0 right-0 z-10 p-2 text-white bg-orange-600 rounded-t-md">
          ⚠️ This location is outside our delivery area. Please select a location within our service zone.
        </div>
      )}

      <div
        ref={mapRef}
        className="w-full h-full rounded-md overflow-hidden"
      />

      {!mapLoaded && !error && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100 bg-opacity-70 rounded-md">
          <div className="flex flex-col items-center">
            <div className="w-8 h-8 border-4 border-bandiwala-orange border-t-transparent rounded-full animate-spin mb-2"></div>
            <p className="text-gray-700">Loading map...</p>
          </div>
        </div>
      )}

      {showCurrentLocationButton && mapLoaded && (
        <Button
          onClick={() => {
            console.log('Use My Location button clicked');
            if (onGetCurrentLocation) {
              onGetCurrentLocation();
            } else {
              console.error('onGetCurrentLocation callback not provided');
            }
          }}
          className="absolute bottom-4 right-4 bg-white text-gray-800 hover:bg-gray-100 shadow-md"
          disabled={isLoading}
          size="sm"
        >
          {isLoading ? (
            <div className="w-4 h-4 border-2 border-bandiwala-orange border-t-transparent rounded-full animate-spin mr-2"></div>
          ) : (
            <Navigation className="w-4 h-4 mr-2" />
          )}
          Use My Location
        </Button>
      )}
    </div>
  );
}
