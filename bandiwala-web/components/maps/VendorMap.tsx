'use client';

import { useEffect, useRef, useState } from 'react';
import { Loader } from '@googlemaps/js-api-loader';
import { vendorLocations, VendorLocation } from '@/data/vendorLocations';
import { SERVICE_AREA_CENTER } from '@/utils/distance';
import VendorModal from '@/components/modals/VendorModal';

export interface VendorMapProps {
  height?: string;
  width?: string;
  zoom?: number;
}

export default function VendorMap({
  height = '400px',
  width = '100%',
  zoom = 14,
}: VendorMapProps) {
  const mapRef = useRef<HTMLDivElement>(null);
  const [mapInstance, setMapInstance] = useState<google.maps.Map | null>(null);
  const [markers, setMarkers] = useState<google.maps.Marker[]>([]);
  const [infoWindows, setInfoWindows] = useState<google.maps.InfoWindow[]>([]);
  const [mapLoaded, setMapLoaded] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedVendor, setSelectedVendor] = useState<VendorLocation | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  // Initialize the map
  useEffect(() => {
    if (!mapRef.current) return;

    const apiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_KEY;
    if (!apiKey) {
      setError('Google Maps API key is missing');
      return;
    }

    const loader = new Loader({
      apiKey,
      version: 'weekly',
    });

    loader
      .load()
      .then(() => {
        const map = new google.maps.Map(mapRef.current!, {
          center: SERVICE_AREA_CENTER,
          zoom,
          mapTypeControl: false,
          streetViewControl: false,
          fullscreenControl: false,
          styles: [
            {
              featureType: 'poi',
              elementType: 'labels',
              stylers: [{ visibility: 'off' }]
            }
          ]
        });

        setMapInstance(map);
        setMapLoaded(true);
      })
      .catch((err) => {
        setError(`Failed to load Google Maps: ${err.message}`);
      });
  }, [zoom]);

  // Create vendor markers
  useEffect(() => {
    if (!mapInstance || !mapLoaded) return;

    // Clear existing markers and info windows
    markers.forEach(marker => marker.setMap(null));
    infoWindows.forEach(infoWindow => infoWindow.close());

    const newMarkers: google.maps.Marker[] = [];
    const newInfoWindows: google.maps.InfoWindow[] = [];

    // Create custom marker icon with vendor image
    const createMarkerIcon = async (imgUrl: string): Promise<string> => {
      return new Promise((resolve) => {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        canvas.width = 60;
        canvas.height = 80;

        if (!ctx) {
          resolve(createDefaultMarkerIcon());
          return;
        }

        const img = new Image();
        img.crossOrigin = 'anonymous';

        img.onload = () => {
          // Clear canvas
          ctx.clearRect(0, 0, canvas.width, canvas.height);

          // Draw marker background (pin shape)
          ctx.fillStyle = '#FF6B35';
          ctx.strokeStyle = 'white';
          ctx.lineWidth = 3;
          ctx.beginPath();
          ctx.moveTo(30, 75);
          ctx.bezierCurveTo(30, 75, 9, 43, 9, 26);
          ctx.bezierCurveTo(9, 14.4, 18.4, 5, 30, 5);
          ctx.bezierCurveTo(41.6, 5, 51, 14.4, 51, 26);
          ctx.bezierCurveTo(51, 43, 30, 75, 30, 75);
          ctx.closePath();
          ctx.fill();
          ctx.stroke();

          // Draw white circle for image background
          ctx.fillStyle = 'white';
          ctx.strokeStyle = '#FF6B35';
          ctx.lineWidth = 2;
          ctx.beginPath();
          ctx.arc(30, 26, 18, 0, 2 * Math.PI);
          ctx.fill();
          ctx.stroke();

          // Draw vendor image in circle
          ctx.save();
          ctx.beginPath();
          ctx.arc(30, 26, 15, 0, 2 * Math.PI);
          ctx.clip();
          ctx.drawImage(img, 15, 11, 30, 30);
          ctx.restore();

          resolve(canvas.toDataURL());
        };

        img.onerror = () => {
          resolve(createDefaultMarkerIcon());
        };

        img.src = imgUrl;
      });
    };

    const createDefaultMarkerIcon = (): string => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      canvas.width = 60;
      canvas.height = 80;

      if (!ctx) return '';

      // Draw marker background
      ctx.fillStyle = '#FF6B35';
      ctx.strokeStyle = 'white';
      ctx.lineWidth = 3;
      ctx.beginPath();
      ctx.moveTo(30, 75);
      ctx.bezierCurveTo(30, 75, 9, 43, 9, 26);
      ctx.bezierCurveTo(9, 14.4, 18.4, 5, 30, 5);
      ctx.bezierCurveTo(41.6, 5, 51, 14.4, 51, 26);
      ctx.bezierCurveTo(51, 43, 30, 75, 30, 75);
      ctx.closePath();
      ctx.fill();
      ctx.stroke();

      // Draw white circle
      ctx.fillStyle = 'white';
      ctx.strokeStyle = '#FF6B35';
      ctx.lineWidth = 2;
      ctx.beginPath();
      ctx.arc(30, 26, 18, 0, 2 * Math.PI);
      ctx.fill();
      ctx.stroke();

      // Draw food emoji as fallback
      ctx.font = '20px Arial';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.fillStyle = '#FF6B35';
      ctx.fillText('🍽️', 30, 26);

      return canvas.toDataURL();
    };

    // Process all vendors
    const markerPromises = vendorLocations.map(async (vendor) => {
      // Get the proper image URL
      const imageUrl = vendor.image.startsWith('/')
        ? `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:6111'}${vendor.image}`
        : vendor.image;

      const iconUrl = await createMarkerIcon(imageUrl);

      // Create custom marker with vendor image
      const marker = new google.maps.Marker({
        position: vendor.coordinates,
        map: mapInstance,
        title: vendor.name,
        icon: {
          url: iconUrl,
          scaledSize: new google.maps.Size(60, 80),
          anchor: new google.maps.Point(30, 75),
        },
        animation: google.maps.Animation.DROP,
      });

      // Create info window with vendor image popup

      const infoWindow = new google.maps.InfoWindow({
        content: `
          <div style="padding: 8px; max-width: 200px;">
            <img
              src="${imageUrl}"
              alt="${vendor.name}"
              style="width: 100%; height: 120px; object-fit: cover; border-radius: 8px; margin-bottom: 8px;"
              onerror="this.src='https://via.placeholder.com/200x120/f3f4f6/9ca3af?text=No+Image'"
            />
            <h3 style="margin: 0 0 4px 0; font-size: 14px; font-weight: bold; color: #333;">
              ${vendor.name}
            </h3>
            <div style="display: flex; align-items: center; gap: 4px; margin-bottom: 8px;">
              <span style="background: #16a34a; color: white; padding: 2px 6px; border-radius: 4px; font-size: 12px;">
                ${vendor.rating} ⭐
              </span>
            </div>
            <p style="margin: 0 0 8px 0; font-size: 12px; color: #666; line-height: 1.3;">
              ${vendor.description.length > 80 ? vendor.description.substring(0, 80) + '...' : vendor.description}
            </p>
            <button
              onclick="window.openVendorModal('${vendor.vendorId}')"
              style="
                background: #FF6B35;
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 6px;
                font-size: 12px;
                cursor: pointer;
                width: 100%;
                transition: background-color 0.2s;
              "
              onmouseover="this.style.background='#e55a2b'"
              onmouseout="this.style.background='#FF6B35'"
            >
              View Details
            </button>
          </div>
        `,
      });

      // Add click listener to marker
      marker.addListener('click', () => {
        // Close all other info windows
        newInfoWindows.forEach(iw => iw.close());
        // Open this info window
        infoWindow.open(mapInstance, marker);
      });

      return { marker, infoWindow };
    });

    // Wait for all markers to be created
    Promise.all(markerPromises).then((results) => {
      const newMarkers = results.map(r => r.marker);
      const newInfoWindows = results.map(r => r.infoWindow);

      setMarkers(newMarkers);
      setInfoWindows(newInfoWindows);
    });

    // Cleanup function
    return () => {
      markers.forEach(marker => marker.setMap(null));
      infoWindows.forEach(infoWindow => infoWindow.close());
    };
  }, [mapInstance, mapLoaded]);

  // Global function to open vendor modal (called from info window)
  useEffect(() => {
    (window as any).openVendorModal = (vendorId: string) => {
      const vendor = vendorLocations.find(v => v.vendorId === vendorId);
      if (vendor) {
        setSelectedVendor(vendor);
        setIsModalOpen(true);
        // Close all info windows
        infoWindows.forEach(iw => iw.close());
      }
    };

    return () => {
      delete (window as any).openVendorModal;
    };
  }, [infoWindows]);

  const handleModalClose = () => {
    setIsModalOpen(false);
    setSelectedVendor(null);
  };

  return (
    <>
      <div className="relative w-full" style={{ height }}>
        {error && (
          <div className="absolute top-0 left-0 right-0 z-10 p-2 text-white bg-red-600 rounded-t-md">
            {error}
          </div>
        )}

        <div
          ref={mapRef}
          className="w-full h-full rounded-xl overflow-hidden"
        />

        {!mapLoaded && !error && (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-100 bg-opacity-70 rounded-xl">
            <div className="flex flex-col items-center">
              <div className="w-8 h-8 border-4 border-orange-500 border-t-transparent rounded-full animate-spin mb-2"></div>
              <p className="text-gray-700">Loading vendor map...</p>
            </div>
          </div>
        )}
      </div>

      <VendorModal
        vendor={selectedVendor}
        isOpen={isModalOpen}
        onClose={handleModalClose}
      />
    </>
  );
}
