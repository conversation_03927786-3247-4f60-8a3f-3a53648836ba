'use client';

import { useEffect, useRef, useState } from 'react';
import { Loader } from '@googlemaps/js-api-loader';
import { SERVICE_AREA_CENTER, SERVICE_AREA_RADIUS_KM, isWithinDeliveryArea } from '@/utils/distance';

export interface Coordinates {
  lat: number;
  lng: number;
}

export interface DeliveryAreaMapProps {
  selectedCoordinates?: Coordinates;
  onCoordinatesChange?: (coords: Coordinates) => void;
  height?: string;
  width?: string;
  zoom?: number;
  showTestMarkers?: boolean;
  testLocations?: Array<{ name: string; coords: Coordinates }>;
}

export default function DeliveryAreaMap({
  selectedCoordinates = SERVICE_AREA_CENTER,
  onCoordinatesChange,
  height = '500px',
  width = '100%',
  zoom = 14,
  showTestMarkers = false,
  testLocations = [],
}: DeliveryAreaMapProps) {
  const mapRef = useRef<HTMLDivElement>(null);
  const [mapInstance, setMapInstance] = useState<google.maps.Map | null>(null);
  const [marker, setMarker] = useState<google.maps.Marker | null>(null);
  const [deliveryCircle, setDeliveryCircle] = useState<google.maps.Circle | null>(null);
  const [testMarkers, setTestMarkers] = useState<google.maps.Marker[]>([]);
  const [mapLoaded, setMapLoaded] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Initialize the map
  useEffect(() => {
    if (!mapRef.current) return;

    const apiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_KEY;
    if (!apiKey) {
      setError('Google Maps API key is missing');
      return;
    }

    const loader = new Loader({
      apiKey,
      version: 'weekly',
    });

    loader
      .load()
      .then(() => {
        const map = new google.maps.Map(mapRef.current!, {
          center: SERVICE_AREA_CENTER,
          zoom,
          mapTypeControl: false,
          streetViewControl: false,
          fullscreenControl: false,
        });

        // Create delivery area circle
        const circle = new google.maps.Circle({
          strokeColor: '#FF6B6B',
          strokeOpacity: 0.8,
          strokeWeight: 2,
          fillColor: '#FF6B6B',
          fillOpacity: 0.15,
          map,
          center: SERVICE_AREA_CENTER,
          radius: SERVICE_AREA_RADIUS_KM * 1000, // Convert km to meters
        });

        // Create center marker for service area
        const centerMarker = new google.maps.Marker({
          position: SERVICE_AREA_CENTER,
          map,
          title: 'Service Area Center',
          icon: {
            url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="12" cy="12" r="8" fill="#FF6B6B" stroke="#FFFFFF" stroke-width="2"/>
                <circle cx="12" cy="12" r="3" fill="#FFFFFF"/>
              </svg>
            `),
            scaledSize: new google.maps.Size(24, 24),
            anchor: new google.maps.Point(12, 12),
          },
        });

        // Create draggable test marker
        const testMarker = new google.maps.Marker({
          position: selectedCoordinates,
          map,
          draggable: true,
          title: 'Test Location',
          animation: google.maps.Animation.DROP,
        });

        setMapInstance(map);
        setMarker(testMarker);
        setDeliveryCircle(circle);
        setMapLoaded(true);

        // Update marker color based on delivery area
        updateMarkerColor(testMarker, selectedCoordinates);

        // Notify parent of initial coordinates
        if (onCoordinatesChange) {
          onCoordinatesChange(selectedCoordinates);
        }
      })
      .catch((err) => {
        setError(`Failed to load Google Maps: ${err.message}`);
      });
  }, [zoom]);

  // Function to update marker color based on delivery area
  const updateMarkerColor = (marker: google.maps.Marker, coords: Coordinates) => {
    const isWithin = isWithinDeliveryArea(coords);
    const color = isWithin ? '#10B981' : '#EF4444'; // Green if within, red if outside
    
    marker.setIcon({
      url: `data:image/svg+xml;charset=UTF-8,${encodeURIComponent(`
        <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M16 2C11.6 2 8 5.6 8 10C8 16 16 30 16 30S24 16 24 10C24 5.6 20.4 2 16 2ZM16 14C13.8 14 12 12.2 12 10S13.8 6 16 6S20 7.8 20 10S18.2 14 16 14Z" fill="${color}"/>
          <circle cx="16" cy="10" r="3" fill="white"/>
        </svg>
      `)}`,
      scaledSize: new google.maps.Size(32, 32),
      anchor: new google.maps.Point(16, 32),
    });
  };

  // Set up event listeners once map and marker are loaded
  useEffect(() => {
    if (!mapInstance || !marker || !mapLoaded) return;

    // Handle map click
    const clickListener = mapInstance.addListener('click', (e: google.maps.MapMouseEvent) => {
      if (!e.latLng) return;

      const newPosition = {
        lat: e.latLng.lat(),
        lng: e.latLng.lng(),
      };

      marker.setPosition(newPosition);
      updateMarkerColor(marker, newPosition);
      
      if (onCoordinatesChange) {
        onCoordinatesChange(newPosition);
      }
    });

    // Handle marker drag end
    const dragEndListener = marker.addListener('dragend', () => {
      const position = marker.getPosition();
      if (!position) return;

      const newPosition = {
        lat: position.lat(),
        lng: position.lng(),
      };

      updateMarkerColor(marker, newPosition);
      
      if (onCoordinatesChange) {
        onCoordinatesChange(newPosition);
      }
    });

    // Cleanup listeners
    return () => {
      google.maps.event.removeListener(clickListener);
      google.maps.event.removeListener(dragEndListener);
    };
  }, [mapInstance, marker, mapLoaded, onCoordinatesChange]);

  // Update marker position when selectedCoordinates change
  useEffect(() => {
    if (!marker || !mapInstance) return;

    marker.setPosition(selectedCoordinates);
    updateMarkerColor(marker, selectedCoordinates);
    mapInstance.panTo(selectedCoordinates);
  }, [selectedCoordinates, marker, mapInstance]);

  // Add test location markers
  useEffect(() => {
    if (!mapInstance || !showTestMarkers || !mapLoaded) return;

    // Clear existing test markers
    testMarkers.forEach(marker => marker.setMap(null));

    // Create new test markers
    const newTestMarkers = testLocations.map((location, index) => {
      const isWithin = isWithinDeliveryArea(location.coords);
      const color = isWithin ? '#10B981' : '#EF4444';
      
      return new google.maps.Marker({
        position: location.coords,
        map: mapInstance,
        title: `${location.name} - ${isWithin ? 'Within' : 'Outside'} delivery area`,
        icon: {
          url: `data:image/svg+xml;charset=UTF-8,${encodeURIComponent(`
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <circle cx="12" cy="12" r="10" fill="${color}" stroke="white" stroke-width="2"/>
              <text x="12" y="16" text-anchor="middle" fill="white" font-size="12" font-weight="bold">${index + 1}</text>
            </svg>
          `)}`,
          scaledSize: new google.maps.Size(24, 24),
          anchor: new google.maps.Point(12, 12),
        },
      });
    });

    setTestMarkers(newTestMarkers);

    return () => {
      newTestMarkers.forEach(marker => marker.setMap(null));
    };
  }, [mapInstance, showTestMarkers, testLocations, mapLoaded]);

  return (
    <div className="relative w-full" style={{ height }}>
      {error && (
        <div className="absolute top-0 left-0 right-0 z-10 p-2 text-white bg-red-600 rounded-t-md">
          {error}
        </div>
      )}

      <div
        ref={mapRef}
        className="w-full h-full rounded-md overflow-hidden"
      />

      {!mapLoaded && !error && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100 bg-opacity-70 rounded-md">
          <div className="flex flex-col items-center">
            <div className="w-8 h-8 border-4 border-red-500 border-t-transparent rounded-full animate-spin mb-2"></div>
            <p className="text-gray-700">Loading delivery area map...</p>
          </div>
        </div>
      )}

      {/* Legend */}
      {mapLoaded && (
        <div className="absolute bottom-4 left-4 bg-white rounded-lg shadow-lg p-3 text-sm">
          <h4 className="font-semibold mb-2">Legend</h4>
          <div className="space-y-1">
            <div className="flex items-center">
              <div className="w-4 h-4 rounded-full bg-red-400 border-2 border-red-600 mr-2"></div>
              <span>Delivery Area (1.001km radius)</span>
            </div>
            <div className="flex items-center">
              <div className="w-4 h-4 rounded-full bg-red-600 mr-2"></div>
              <span>Service Center</span>
            </div>
            <div className="flex items-center">
              <div className="w-4 h-4 rounded-full bg-green-500 mr-2"></div>
              <span>Within Area</span>
            </div>
            <div className="flex items-center">
              <div className="w-4 h-4 rounded-full bg-red-500 mr-2"></div>
              <span>Outside Area</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
