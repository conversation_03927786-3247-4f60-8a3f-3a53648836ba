import React from 'react';
import Link from 'next/link';
import { useCart } from '@/contexts/CartContext';
import { CartItem } from '@/types/cart';
import { toast } from 'sonner';
// import { CartStatus } from './CartStatus';
// import { SyncIndicator } from './SyncIndicator';

export default function Cart() {
  const { cart, loading, error, isOnline, updateCartItem, removeFromCart } = useCart();

  if (loading) {
    return (
      <div className="p-4 text-center">
        {/* <SyncIndicator
          isOnline={isOnline}
          isSyncing={loading}
          pendingChanges={!isOnline && cart.items.length > 0}
        /> */}
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
        <p className="mt-2">Loading cart...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 text-center">
        {/* <SyncIndicator
          isOnline={isOnline}
          isSyncing={loading}
          pendingChanges={!isOnline && cart.items.length > 0}
        /> */}
        <p className="text-red-500">{error}</p>
        <button
          onClick={() => window.location.reload()}
          className="mt-2 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Try Again
        </button>
      </div>
    );
  }

  if (!cart.items.length) {
    return (
      <div className="p-8 text-center text-gray-500">
        {/* <SyncIndicator
          isOnline={isOnline}
          isSyncing={loading}
          pendingChanges={!isOnline && cart.items.length > 0}
        /> */}
        <p className="text-xl">Your cart is empty</p>
        <p className="mt-2">Add some delicious items to get started!</p>
      </div>
    );
  }

  const calculateTotal = (items: CartItem[]) => {
    return items.reduce((sum, item) => sum + (item.price ?? 0) * item.quantity, 0);
  };

  return (
    <>
      <div className="p-4">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold">Your Cart</h2>
          {/* <SyncIndicator
            isOnline={isOnline}
            isSyncing={loading}
            pendingChanges={!isOnline && cart.items.length > 0}
          /> */}
        </div>
        <div className="space-y-4">
          {cart.items.map((item) => (
            <div key={item.menuItemId} className="flex items-center justify-between border-b pb-4">
              <div className="flex items-center space-x-4">
                <img
                  src={item.image}
                  alt={item.name}
                  className="w-16 h-16 object-cover rounded"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.src = '/placeholder-image.jpg';
                  }}
                />
                <div>
                  <h3 className="font-semibold">{item.name}</h3>
                  <p className="text-gray-600">₹{item.price}</p>
                  {item.notes && (
                    <p className="text-sm text-gray-500">Notes: {item.notes}</p>
                  )}
                </div>
              </div>
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => updateCartItem(item.menuItemId, Math.max(0, item.quantity - 1))}
                    className="px-2 py-1 bg-gray-100 rounded hover:bg-gray-200 transition-colors"
                    disabled={loading}
                  >
                    -
                  </button>
                  <span className="w-8 text-center">{item.quantity}</span>
                  <button
                    onClick={() => updateCartItem(item.menuItemId, item.quantity + 1)}
                    className="px-2 py-1 bg-gray-100 rounded hover:bg-gray-200 transition-colors"
                    disabled={loading}
                  >
                    +
                  </button>
                </div>
                <button
                  onClick={() => removeFromCart(item.menuItemId)}
                  className="text-red-500 hover:text-red-600 transition-colors"
                  disabled={loading}
                >
                  Remove
                </button>
              </div>
            </div>
          ))}
        </div>
        <div className="mt-6 text-right">
          <p className="text-lg text-gray-600">Subtotal: ₹{calculateTotal(cart.items)}</p>
          <p className="text-2xl font-bold">Total: ₹{calculateTotal(cart.items)}</p>
          <div className="flex justify-end space-x-4 mt-4">
            <button
              onClick={() => {
                toast(`Test Notification`, {
                  description: `This is a test toast notification`,
                  position: 'top-right',
                  duration: 3000
                });
              }}
              className="px-6 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
            >
              Test Toast
            </button>
            <Link href="/checkout/summary">
              <button
                className="px-6 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition-colors"
                disabled={loading || !isOnline || cart.items.length === 0}
              >
                {isOnline ? 'Proceed to Payment' : 'Please wait for connection'}
              </button>
            </Link>
          </div>
        </div>
      </div>
      {/* <CartStatus /> */}
    </>
  );
}