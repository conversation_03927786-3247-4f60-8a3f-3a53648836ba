import React from 'react';
import { useCart } from '@/contexts/CartContext';

export const CartStatus: React.FC = () => {
  const { loading, error, isOnline } = useCart();

  if (error) {
    return (
      <div className="fixed bottom-4 right-4 bg-red-100 border border-red-400 text-red-700 px-4 py-2 rounded-md shadow-lg">
        <p className="font-medium">Error: {error}</p>
        <p className="text-sm">Your changes are saved locally</p>
      </div>
    );
  }

  if (!isOnline) {
    return (
      <div className="fixed bottom-4 right-4 bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-2 rounded-md shadow-lg">
        <p className="font-medium">🔄 Offline Mode</p>
        <p className="text-sm">Changes will sync when connection is restored</p>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="fixed bottom-4 right-4 bg-blue-100 border border-blue-400 text-blue-700 px-4 py-2 rounded-md shadow-lg">
        <p className="font-medium">💫 Syncing...</p>
        <p className="text-sm">Please wait</p>
      </div>
    );
  }

  return null;
};