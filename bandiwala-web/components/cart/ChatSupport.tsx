'use client';

import React, { useState } from 'react';
import { MessageCircle, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';

export default function ChatSupport() {
  const [isOpen, setIsOpen] = useState(false);
  const [message, setMessage] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // In production, integrate with your chat support system
    console.log('Support message:', message);
    setMessage('');
    setIsOpen(false);
  };

  return (
    <>
      {/* Chat Button */}
      <Button
        onClick={() => setIsOpen(true)}
        className="fixed bottom-6 right-6 rounded-full w-12 h-12 p-0 bg-bandiwala-orange hover:bg-bandiwala-red shadow-lg"
      >
        <MessageCircle className="h-6 w-6" />
      </Button>

      {/* Chat <PERSON> */}
      {isOpen && (
        <div className="fixed bottom-24 right-6 w-[350px] bg-white rounded-lg shadow-xl border">
          <div className="flex items-center justify-between p-4 border-b">
            <h3 className="font-semibold">Need Help?</h3>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setIsOpen(false)}
              className="h-8 w-8"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
          
          <form onSubmit={handleSubmit} className="p-4">
            <Textarea
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              placeholder="Type your message here..."
              className="min-h-[100px] mb-4"
            />
            <Button 
              type="submit"
              className="w-full bg-bandiwala-orange hover:bg-bandiwala-red"
              disabled={!message.trim()}
            >
              Send Message
            </Button>
          </form>
        </div>
      )}
    </>
  );
}
