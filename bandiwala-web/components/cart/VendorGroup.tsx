'use client';

import React from 'react';
import { Clock, Truck, Plus, Minus, Trash2 } from 'lucide-react';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { getImageUrl } from '@/utils/imageUtils';
import { SortableList } from '@/components/ui/sortable-list';

interface Subcategory {
  title: string;
  quantity: string;
  price: number;
}

interface CartItem {
  selectedSubcategory: Subcategory;
  menuItemId: string;
  name: string;
  price?: number; // Optional since price is in selectedSubcategory
  quantity: number;
  image: string;
  notes?: string;
  vendorId?: string;
  vendorName?: string;
}

interface VendorGroupProps {
  vendorName: string;
  deliveryTime: string;
  deliveryFee: number;
  minOrderValue: number;
  items: CartItem[];
  onUpdateQuantity: (id: string, quantity: number, item?: CartItem) => void;
  onUpdateNotes: (id: string, notes: string, item?: CartItem) => void;
  onRemoveItem: (id: string, item?: CartItem) => void;
  onAddMoreItems: (vendorName: string) => void;
  onReorderItems?: (items: CartItem[]) => void;
}

export default function VendorGroup({
  vendorName,
  deliveryTime,
  deliveryFee,
  items,
  onUpdateQuantity,
  onRemoveItem,
  onAddMoreItems,
  onReorderItems
}: VendorGroupProps) {

  return (
    <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
      <div className="border-b pb-4 mb-4">
        <h3 className="text-lg font-semibold">{vendorName}</h3>
        <div className="flex items-center gap-4 mt-2 text-sm text-gray-600">
          <div className="flex items-center gap-1">
            <Clock size={16} />
            <span>{deliveryTime}</span>
          </div>
          <div className="flex items-center gap-1">
            <Truck size={16} />
            <span>₹{deliveryFee} delivery fee</span>
          </div>
        </div>
      </div>

      <SortableList
        items={items}
        onReorder={onReorderItems || (() => {})}
        getItemId={(item) => `${item.menuItemId}-${item.selectedSubcategory.title}`}
        disabled={!onReorderItems}
        className="space-y-4"
        renderItem={(item) => (
          <div className="flex gap-4 pb-4 border-b last:border-0">
            <div className="relative h-20 w-20 rounded-md overflow-hidden">
              <Image
                src={getImageUrl(item.image) || '/placeholder.jpg'}
                alt={item.name}
                fill
                className="object-cover"
              />
            </div>

            <div className="flex-1">
              <div className="flex justify-between">
                <div>
                  <h4 className="font-medium">{item.name}</h4>
                  <p className="text-sm text-gray-600">{item.selectedSubcategory.title}</p>
                </div>
                <p className="font-medium">₹{(item.selectedSubcategory?.price || item.price || 0) * item.quantity}</p>
              </div>

              <div className="flex items-center gap-2 mt-2">
                <button
                  onClick={() => onUpdateQuantity(item.menuItemId, item.quantity - 1, item)}
                  className="p-1 rounded-full hover:bg-gray-100"
                >
                  <Minus size={16} />
                </button>
                <span className="w-8 text-center">{item.quantity}</span>
                <button
                  onClick={() => onUpdateQuantity(item.menuItemId, item.quantity + 1, item)}
                  className="p-1 rounded-full hover:bg-gray-100"
                >
                  <Plus size={16} />
                </button>
                <button
                  onClick={() => onRemoveItem(item.menuItemId, item)}
                  className="p-1 rounded-full hover:bg-gray-100 ml-2"
                >
                  <Trash2 size={16} className="text-red-500" />
                </button>
              </div>
            </div>
          </div>
        )}
      />



      <Button
        variant="outline"
        className="w-full mt-4"
        onClick={() => onAddMoreItems(vendorName)}
      >
        Add More Items
      </Button>
    </div>
  );
}
