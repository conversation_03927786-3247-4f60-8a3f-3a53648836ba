'use client';

import React from 'react';
import Link from 'next/link';
import { ShoppingCart } from 'lucide-react';
import { Button } from '@/components/ui/button';

export default function EmptyCart() {
  return (
    <div className="flex flex-col items-center justify-center py-16">
      <div className="bg-gray-100 p-6 rounded-full mb-6">
        <ShoppingCart size={48} className="text-gray-400" />
      </div>
      <h2 className="text-2xl font-semibold mb-2">Your cart is empty</h2>
      <p className="text-gray-600 mb-6 text-center max-w-md">
        Looks like you haven&apos;t added anything to your cart yet.
        Browse our menu and discover tasty dishes!
      </p>
      <Link href="/dishes">
        <Button className="bg-bandiwala-orange hover:bg-bandiwala-red">
          Browse Menu
        </Button>
      </Link>
    </div>
  );
}
