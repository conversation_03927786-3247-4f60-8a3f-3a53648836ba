'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { Clock } from 'lucide-react';
import PromoCodeInput from './PromoCodeInput';
import type { CartTotals } from '@/types/cart';

interface OrderSummaryProps extends CartTotals {
  appliedPromoCode?: string;
  isFreeDelivery?: boolean;
  originalDeliveryCharge?: number;
  estimatedDeliveryTime: string;
  onApplyPromo: (code: string) => void;
  onProceedToPayment: () => void;
}

export default function OrderSummary({
  subtotal,
  platformFee,
  deliveryCharge,
  tax,
  discount,
  total,
  appliedPromoCode,
  isFreeDelivery = false,
  originalDeliveryCharge,
  estimatedDeliveryTime,
  onApplyPromo,
  onProceedToPayment
}: OrderSummaryProps) {
  return (
    <div className="bg-white rounded-lg shadow-sm p-6 sticky top-20">
      <h3 className="text-lg font-semibold mb-4">Order Summary</h3>
      
      <div className="flex items-center gap-2 text-sm text-gray-600 mb-4">
        <Clock size={16} />
        <span>Estimated delivery in {estimatedDeliveryTime}</span>
      </div>

      <div className="space-y-3 mb-6">
        <div className="flex justify-between text-sm">
          <span>Subtotal</span>
          <span>₹{subtotal.toFixed(2)}</span>
        </div>
        <div className="flex justify-between text-sm">
          <span>Platform fee</span>
          <span>₹{platformFee.toFixed(2)}</span>
        </div>
        <div className="flex justify-between text-sm">
          <span>Delivery charge</span>
          <span className={isFreeDelivery ? 'line-through text-gray-400' : ''}>
            ₹{(originalDeliveryCharge || deliveryCharge).toFixed(2)}
          </span>
        </div>
        {isFreeDelivery && (
          <div className="flex justify-between text-green-600 text-sm">
            <span>Free delivery ({appliedPromoCode})</span>
            <span>-₹{(originalDeliveryCharge || deliveryCharge).toFixed(2)}</span>
          </div>
        )}
        <div className="flex justify-between text-sm">
          <span>Taxes</span>
          <span>₹{tax.toFixed(2)}</span>
        </div>
        {discount > 0 && !isFreeDelivery && (
          <div className="flex justify-between text-green-600 text-sm">
            <span>Discount{appliedPromoCode ? ` (${appliedPromoCode})` : ''}</span>
            <span>-₹{discount.toFixed(2)}</span>
          </div>
        )}
        <div className="border-t pt-3 flex justify-between font-semibold">
          <span>Total</span>
          <span>₹{total.toFixed(2)}</span>
        </div>
      </div>

      <PromoCodeInput
        onApply={onApplyPromo}
        isApplied={!!appliedPromoCode}
        className="mb-6"
      />

      <Button 
        onClick={onProceedToPayment}
        className="w-full bg-bandiwala-orange hover:bg-bandiwala-red"
      >
        Proceed to Payment
      </Button>
    </div>
  );
}
