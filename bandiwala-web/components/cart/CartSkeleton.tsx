'use client';

import React from 'react';
import { Skeleton } from "@/components/ui/skeleton";
import Header from '@/components/Header';
import Footer from '@/components/Footer';

export default function CartSkeleton() {
  return (
    <>
      <Header />
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-6">
          <Skeleton className="h-8 w-32" />
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2">
            {[1, 2].map((i) => (
              <div key={i} className="mb-8 bg-white rounded-lg p-6 shadow-sm">
                <Skeleton className="h-6 w-48 mb-4" />
                <div className="space-y-4">
                  {[1, 2, 3].map((j) => (
                    <div key={j} className="flex gap-4">
                      <Skeleton className="h-20 w-20 rounded-md" />
                      <div className="flex-1">
                        <Skeleton className="h-4 w-32 mb-2" />
                        <Skeleton className="h-4 w-24 mb-2" />
                        <Skeleton className="h-4 w-16" />
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg p-6 shadow-sm">
              <Skeleton className="h-6 w-32 mb-4" />
              {[1, 2, 3, 4].map((i) => (
                <div key={i} className="flex justify-between mb-3">
                  <Skeleton className="h-4 w-24" />
                  <Skeleton className="h-4 w-16" />
                </div>
              ))}
              <Skeleton className="h-12 w-full mt-6" />
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </>
  );
}