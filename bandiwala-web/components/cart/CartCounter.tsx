'use client';

import { useCart } from '@/contexts/CartContext';
import { ShoppingBag } from 'lucide-react';

interface CartCounterProps {
  showAlways?: boolean;
}

export default function CartCounter({ showAlways = false }: CartCounterProps) {
  const { cart } = useCart();

  // Calculate total items
  const totalItems = cart?.items?.reduce((sum, item) => sum + item.quantity, 0) || 0;

  // If showAlways is false and no items, return null (original behavior)
  if (!showAlways && totalItems === 0) return null;

  return (
    <div className="relative">
      <ShoppingBag className="h-5 w-5" />
      {totalItems > 0 && (
        <span className="absolute -top-2 -right-2 h-4 w-4 text-xs flex items-center justify-center bg-bandiwala-orange text-white rounded-full">
          {totalItems}
        </span>
      )}
    </div>
  );
}