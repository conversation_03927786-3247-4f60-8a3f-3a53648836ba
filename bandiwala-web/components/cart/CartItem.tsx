import React, { useState } from 'react';
import { Minus, Plus, X } from 'lucide-react';
import Image from 'next/image';

interface CartItemProps {
  id: string;
  name: string;
  price: number;
  quantity: number;
  image: string;
  tag: string;
  notes: string;
  maxQuantity?: number;
  onUpdateQuantity: (id: string, quantity: number) => void;
  onUpdateNotes: (id: string, notes: string) => void;
  onRemoveItem: (id: string) => void;
}

const CartItem: React.FC<CartItemProps> = ({
  id,
  name,
  price,
  quantity,
  image,
  tag,
  notes,
  maxQuantity = 10,
  onUpdateQuantity,
  onUpdateNotes,
  onRemoveItem,
}) => {
  const [itemNotes, setItemNotes] = useState(notes);

  const handleQuantityChange = (newQuantity: number) => {
    if (newQuantity > 0 && newQuantity <= maxQuantity) {
      onUpdateQuantity(id, newQuantity);
    }
  };

  const handleNotesChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newNotes = e.target.value;
    setItemNotes(newNotes);
    onUpdateNotes(id, newNotes);
  };

  return (
    <div className="flex flex-col gap-4 p-4 border-b border-gray-200">
      <div className="flex gap-4">
        <div className="relative w-24 h-24 rounded-lg overflow-hidden">
          <Image
            src={image}
            alt={name}
            width={80}
            height={80}
            className="rounded-lg object-cover"
          />
          {tag && (
            <div className="absolute top-0 left-0 bg-bandiwala-yellow px-2 py-1 text-xs font-medium text-bandiwala-dark">
              {tag}
            </div>
          )}
        </div>

        <div className="flex-grow">
          <h3 className="text-lg font-medium text-gray-900">{name}</h3>
          <p className="text-lg font-medium text-bandiwala-dark">₹{price.toFixed(2)}</p>

          <div className="flex items-center mt-2">
            <button
              onClick={() => handleQuantityChange(quantity - 1)}
              className="p-1 rounded-md hover:bg-gray-100"
              aria-label="Decrease quantity"
            >
              <Minus size={16} />
            </button>
            <span className="px-3 py-1 text-gray-800">{quantity}</span>
            <button
              onClick={() => handleQuantityChange(quantity + 1)}
              className="p-1 rounded-md hover:bg-gray-100"
              aria-label="Increase quantity"
              disabled={quantity >= maxQuantity}
            >
              <Plus size={16} />
            </button>
          </div>
        </div>

        <button
          onClick={() => onRemoveItem(id)}
          className="text-gray-400 hover:text-bandiwala-error transition-colors"
          aria-label="Remove item"
        >
          <X size={18} />
        </button>
      </div>

      <div className="w-full">
        <input
          type="text"
          placeholder="Add special instructions..."
          value={itemNotes}
          onChange={handleNotesChange}
          className="text-sm w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-1 focus:ring-bandiwala-yellow focus:border-transparent"
        />
      </div>
    </div>
  );
};

export default CartItem;
