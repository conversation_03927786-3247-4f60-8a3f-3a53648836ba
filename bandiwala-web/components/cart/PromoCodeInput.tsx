'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

interface PromoCodeInputProps {
  onApply: (code: string) => void;
  isApplied?: boolean;
  className?: string;
}

export default function PromoCodeInput({ onApply, isApplied, className }: PromoCodeInputProps) {
  const [code, setCode] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (code.trim()) {
      onApply(code.trim().toUpperCase());
      setCode('');
    }
  };

  if (isApplied) return null;

  return (
    <form onSubmit={handleSubmit} className={`flex gap-2 ${className}`}>
      <Input
        placeholder="Enter promo code"
        value={code}
        onChange={(e) => setCode(e.target.value)}
        className="flex-1"
        autoComplete="off"
      />
      <Button 
        type="submit"
        variant="outline"
        disabled={!code.trim()}
      >
        Apply
      </Button>
    </form>
  );
}