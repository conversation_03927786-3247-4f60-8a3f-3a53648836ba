'use client';

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { 
  CheckCircle, 
  XCircle, 
  AlertTriangle, 
  Clock, 
  Flag,
  ChevronLeft,
  ChevronRight,
  Eye
} from 'lucide-react';
import RatingStars from './RatingStars';
import { Review } from '@/types/review';
import { reviewService } from '@/services/api';
import { toast } from 'sonner';

export default function ModerationDashboard() {
  const [reviews, setReviews] = useState<Review[]>([]);
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 0,
    totalReviews: 0,
    hasNextPage: false,
    hasPrevPage: false
  });
  const [loading, setLoading] = useState(true);
  const [moderatingReview, setModeratingReview] = useState<string | null>(null);
  const [moderationReason, setModerationReason] = useState('');

  const fetchPendingReviews = async (page: number = 1) => {
    try {
      setLoading(true);
      const response = await reviewService.getPendingReviews(page, 10);

      if (response.success && 'data' in response && response.data) {
        setReviews(response.data.reviews);
        setPagination(response.data.pagination);
      } else {
        // Handle error case
        const errorMessage = 'message' in response ? response.message : 'Failed to load pending reviews';
        toast.error(errorMessage);
      }
    } catch (error) {
      console.error('Error fetching pending reviews:', error);
      toast.error('Failed to load pending reviews');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPendingReviews();
  }, []);

  const handleModerate = async (reviewId: string, action: 'approve' | 'reject') => {
    try {
      setModeratingReview(reviewId);
      const response = await reviewService.moderateReview(
        reviewId, 
        action, 
        moderationReason.trim() || undefined
      );

      if (response.success) {
        toast.success(`Review ${action}d successfully`);
        setReviews(prev => prev.filter(review => review._id !== reviewId));
        setModerationReason('');
      } else {
        toast.error(response.message || `Failed to ${action} review`);
      }
    } catch (error) {
      console.error(`Error ${action}ing review:`, error);
      toast.error(`An error occurred while ${action}ing the review`);
    } finally {
      setModeratingReview(null);
    }
  };

  const handlePageChange = (newPage: number) => {
    fetchPendingReviews(newPage);
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800"><Clock size={12} className="mr-1" />Pending</Badge>;
      case 'flagged':
        return <Badge variant="destructive" className="bg-orange-100 text-orange-800"><Flag size={12} className="mr-1" />Flagged</Badge>;
      case 'approved':
        return <Badge variant="default" className="bg-green-100 text-green-800"><CheckCircle size={12} className="mr-1" />Approved</Badge>;
      case 'rejected':
        return <Badge variant="destructive"><XCircle size={12} className="mr-1" />Rejected</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Review Moderation</h1>
          <p className="text-gray-600 mt-1">Manage and moderate user reviews</p>
        </div>
        <div className="text-sm text-gray-500">
          {pagination.totalReviews} reviews pending moderation
        </div>
      </div>

      {/* Reviews List */}
      {reviews.length > 0 ? (
        <div className="space-y-4">
          {reviews.map((review) => (
            <Card key={review._id} className="w-full">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center text-white font-semibold">
                      {review.userId.name.charAt(0).toUpperCase()}
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900">{review.userId.name}</h3>
                      <p className="text-sm text-gray-500">
                        {review.targetType}: {review.targetId}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {getStatusBadge(review.moderationStatus)}
                    <span className="text-sm text-gray-500">
                      {new Date(review.createdAt).toLocaleDateString()}
                    </span>
                  </div>
                </div>
              </CardHeader>
              
              <CardContent className="space-y-4">
                {/* Rating and Comment */}
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <RatingStars rating={review.rating} size={16} />
                    <span className="text-sm text-gray-600">{review.rating}/5</span>
                  </div>
                  <p className="text-gray-700 bg-gray-50 p-3 rounded-lg">
                    "{review.comment}"
                  </p>
                </div>

                {/* Flags (if any) */}
                {review.flaggedBy && review.flaggedBy.length > 0 && (
                  <div className="bg-orange-50 border border-orange-200 rounded-lg p-3">
                    <div className="flex items-center gap-2 mb-2">
                      <AlertTriangle size={16} className="text-orange-600" />
                      <span className="font-medium text-orange-800">
                        Flagged by {review.flaggedBy.length} user(s)
                      </span>
                    </div>
                    <div className="space-y-1">
                      {review.flaggedBy.map((flag, index) => (
                        <div key={index} className="text-sm text-orange-700">
                          <strong>{flag.userId.name}:</strong> {flag.reason}
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Moderation Actions */}
                <div className="border-t pt-4">
                  <div className="space-y-3">
                    <Textarea
                      value={moderationReason}
                      onChange={(e) => setModerationReason(e.target.value)}
                      placeholder="Optional: Add a reason for your moderation decision..."
                      className="min-h-[60px]"
                      maxLength={200}
                    />
                    
                    <div className="flex gap-3">
                      <Button
                        onClick={() => handleModerate(review._id, 'approve')}
                        disabled={moderatingReview === review._id}
                        className="bg-green-600 hover:bg-green-700 text-white"
                      >
                        <CheckCircle size={16} className="mr-2" />
                        {moderatingReview === review._id ? 'Approving...' : 'Approve'}
                      </Button>
                      
                      <Button
                        onClick={() => handleModerate(review._id, 'reject')}
                        disabled={moderatingReview === review._id}
                        variant="destructive"
                      >
                        <XCircle size={16} className="mr-2" />
                        {moderatingReview === review._id ? 'Rejecting...' : 'Reject'}
                      </Button>
                      
                      <Button
                        variant="outline"
                        onClick={() => window.open(`/items/${review.targetId}`, '_blank')}
                      >
                        <Eye size={16} className="mr-2" />
                        View Item
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}

          {/* Pagination */}
          {pagination.totalPages > 1 && (
            <div className="flex justify-center items-center gap-4 mt-8">
              <Button
                variant="outline"
                onClick={() => handlePageChange(pagination.currentPage - 1)}
                disabled={!pagination.hasPrevPage}
              >
                <ChevronLeft size={16} className="mr-1" />
                Previous
              </Button>
              
              <span className="text-sm text-gray-600">
                Page {pagination.currentPage} of {pagination.totalPages}
              </span>
              
              <Button
                variant="outline"
                onClick={() => handlePageChange(pagination.currentPage + 1)}
                disabled={!pagination.hasNextPage}
              >
                Next
                <ChevronRight size={16} className="ml-1" />
              </Button>
            </div>
          )}
        </div>
      ) : (
        <Card>
          <CardContent className="p-8 text-center">
            <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-700 mb-2">
              All Caught Up!
            </h3>
            <p className="text-gray-500">
              There are no reviews pending moderation at the moment.
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
