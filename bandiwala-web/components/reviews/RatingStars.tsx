'use client';

import React from 'react';
import { Star } from 'lucide-react';

interface RatingStarsProps {
  rating: number;
  maxRating?: number;
  size?: number;
  interactive?: boolean;
  onRatingChange?: (rating: number) => void;
  className?: string;
}

export default function RatingStars({
  rating,
  maxRating = 5,
  size = 20,
  interactive = false,
  onRatingChange,
  className = ''
}: RatingStarsProps) {
  const [hoverRating, setHoverRating] = React.useState(0);

  const handleStarClick = (starRating: number) => {
    if (interactive && onRatingChange) {
      onRatingChange(starRating);
    }
  };

  const handleStarHover = (starRating: number) => {
    if (interactive) {
      setHoverRating(starRating);
    }
  };

  const handleMouseLeave = () => {
    if (interactive) {
      setHoverRating(0);
    }
  };

  const getStarColor = (starIndex: number) => {
    const currentRating = interactive && hoverRating > 0 ? hoverRating : rating;
    
    if (starIndex <= currentRating) {
      return 'text-yellow-400 fill-current';
    }
    return 'text-gray-300';
  };

  return (
    <div 
      className={`flex items-center ${className}`}
      onMouseLeave={handleMouseLeave}
    >
      {Array.from({ length: maxRating }, (_, index) => {
        const starIndex = index + 1;
        return (
          <Star
            key={starIndex}
            size={size}
            className={`${getStarColor(starIndex)} ${
              interactive 
                ? 'cursor-pointer hover:scale-110 transition-transform duration-150' 
                : ''
            }`}
            onClick={() => handleStarClick(starIndex)}
            onMouseEnter={() => handleStarHover(starIndex)}
          />
        );
      })}
    </div>
  );
}
