'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Star, Package, CheckCircle, Edit } from 'lucide-react';
import { reviewService } from '@/services/api';
import { OrderReviewStatus, OrderReviewItem } from '@/types/review';
import { toast } from 'sonner';
import Image from 'next/image';
import { getImageUrl } from '@/utils/imageUtils';
import OrderReviewForm from './OrderReviewForm';
import RatingStars from './RatingStars';

interface OrderReviewSectionProps {
  orderId: string;
  orderNumber?: string;
}

export default function OrderReviewSection({ orderId, orderNumber }: OrderReviewSectionProps) {
  const [reviewStatus, setReviewStatus] = useState<OrderReviewStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedItem, setSelectedItem] = useState<OrderReviewItem | null>(null);
  const [showReviewForm, setShowReviewForm] = useState(false);

  const fetchReviewStatus = async () => {
    try {
      setLoading(true);
      const response = await reviewService.getOrderReviewStatus(orderId);

      if (response.success && 'data' in response && response.data) {
        setReviewStatus(response.data);
      } else {
        // If order is not delivered or doesn't exist, don't show error
        const message = 'message' in response ? response.message : 'Order review status not available';
        console.log('Order review status:', message);
      }
    } catch (error) {
      console.error('Error fetching review status:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchReviewStatus();
  }, [orderId]);

  const handleReviewClick = (item: OrderReviewItem) => {
    setSelectedItem(item);
    setShowReviewForm(true);
  };

  const handleReviewSubmitted = () => {
    setShowReviewForm(false);
    setSelectedItem(null);
    fetchReviewStatus(); // Refresh the status
    toast.success('Thank you for your review!');
  };

  const handleCancelReview = () => {
    setShowReviewForm(false);
    setSelectedItem(null);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-8">
        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-bandiwala-orange"></div>
      </div>
    );
  }

  // Don't show anything if order is not reviewable
  if (!reviewStatus || reviewStatus.items.length === 0) {
    return null;
  }

  // Show review form if an item is selected
  if (showReviewForm && selectedItem) {
    return (
      <OrderReviewForm
        orderId={orderId}
        menuItemId={selectedItem.menuItemId}
        itemName={selectedItem.itemName}
        itemImage={selectedItem.image}
        onReviewSubmitted={handleReviewSubmitted}
        onCancel={handleCancelReview}
      />
    );
  }

  const reviewableItems = reviewStatus.items.filter(item => item.canReview);
  const reviewedItems = reviewStatus.items.filter(item => item.hasReviewed);

  return (
    <div className="space-y-6">
      {/* Reviewable Items */}
      {reviewableItems.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg flex items-center gap-2">
              <Star className="h-5 w-5 text-yellow-500" />
              Rate & Review Your Items
            </CardTitle>
            <p className="text-sm text-gray-600">
              Help other customers by sharing your experience with these items
            </p>
          </CardHeader>
          <CardContent className="space-y-4">
            {reviewableItems.map((item) => (
              <div
                key={item.menuItemId}
                className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:border-gray-300 transition-colors"
              >
                <div className="flex items-center gap-4">
                  <div className="w-16 h-16 rounded-lg overflow-hidden bg-gray-100 flex-shrink-0">
                    <Image
                      src={getImageUrl(item.image)}
                      alt={item.itemName}
                      width={64}
                      height={64}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-900">{item.itemName}</h3>
                    <p className="text-sm text-gray-600">
                      {item.selectedSubcategory.title} • Qty: {item.quantity}
                    </p>
                    <div className="flex items-center gap-1 mt-1">
                      <Package size={12} className="text-green-600" />
                      <span className="text-xs text-green-600">Delivered</span>
                    </div>
                  </div>
                </div>
                <Button
                  onClick={() => handleReviewClick(item)}
                  className="bg-bandiwala-orange hover:bg-bandiwala-red text-white"
                >
                  Write Review
                </Button>
              </div>
            ))}
          </CardContent>
        </Card>
      )}

      {/* Already Reviewed Items */}
      {reviewedItems.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              Your Reviews
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {reviewedItems.map((item) => (
              <div
                key={item.menuItemId}
                className="flex items-center justify-between p-4 border border-gray-200 rounded-lg bg-gray-50"
              >
                <div className="flex items-center gap-4">
                  <div className="w-16 h-16 rounded-lg overflow-hidden bg-gray-100 flex-shrink-0">
                    <Image
                      src={getImageUrl(item.image)}
                      alt={item.itemName}
                      width={64}
                      height={64}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-900">{item.itemName}</h3>
                    <p className="text-sm text-gray-600">
                      {item.selectedSubcategory.title} • Qty: {item.quantity}
                    </p>
                    {item.existingReview && (
                      <div className="flex items-center gap-2 mt-2">
                        <RatingStars rating={item.existingReview.rating} size={14} />
                        <span className="text-sm text-gray-600">
                          {item.existingReview.rating}/5
                        </span>
                      </div>
                    )}
                    {item.existingReview && (
                      <p className="text-sm text-gray-700 mt-1 line-clamp-2">
                        "{item.existingReview.comment}"
                      </p>
                    )}
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle size={20} className="text-green-600" />
                  <span className="text-sm text-green-600 font-medium">Reviewed</span>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>
      )}

      {/* No items to review */}
      {reviewableItems.length === 0 && reviewedItems.length === 0 && (
        <Card>
          <CardContent className="p-8 text-center">
            <Package className="h-12 w-12 mx-auto mb-4 text-gray-400" />
            <h3 className="text-lg font-medium text-gray-700 mb-2">
              Reviews Not Available
            </h3>
            <p className="text-gray-500">
              Reviews can only be written for delivered orders.
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
