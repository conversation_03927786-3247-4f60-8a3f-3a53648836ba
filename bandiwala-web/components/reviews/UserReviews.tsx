'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ChevronLeft, ChevronRight, Star, Calendar, Package } from 'lucide-react';
import RatingStars from './RatingStars';
import ReviewCard from './ReviewCard';
import { Review } from '@/types/review';
import { reviewService } from '@/services/api';
import { toast } from 'sonner';

interface UserReviewsProps {
  userId: string;
}

export default function UserReviews({ userId }: UserReviewsProps) {
  const [reviews, setReviews] = useState<Review[]>([]);
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 0,
    totalReviews: 0,
    hasNextPage: false,
    hasPrevPage: false
  });
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    totalReviews: 0,
    averageRating: 0,
    menuItemReviews: 0,
    vendorReviews: 0
  });

  const fetchUserReviews = async (page: number = 1) => {
    try {
      setLoading(true);
      const response = await reviewService.getUserReviews(page, 10);

      if (response.success && 'data' in response && response.data) {
        setReviews(response.data.reviews);
        setPagination(response.data.pagination);

        // Calculate stats
        const totalReviews = response.data.reviews.length;
        const averageRating = totalReviews > 0
          ? response.data.reviews.reduce((sum: number, review: Review) => sum + review.rating, 0) / totalReviews
          : 0;
        const menuItemReviews = response.data.reviews.filter((r: Review) => r.targetType === 'MenuItem').length;
        const vendorReviews = response.data.reviews.filter((r: Review) => r.targetType === 'Vendor').length;

        setStats({
          totalReviews: response.data.pagination.totalReviews,
          averageRating,
          menuItemReviews,
          vendorReviews
        });
      } else {
        // Handle error case
        const errorMessage = 'message' in response ? response.message : 'Failed to load user reviews';
        toast.error(errorMessage);
      }
    } catch (error) {
      console.error('Error fetching user reviews:', error);
      toast.error('Failed to load your reviews');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (userId) {
      fetchUserReviews();
    }
  }, [userId]);

  const handleReviewUpdated = (updatedReview: Review) => {
    setReviews(prev => 
      prev.map(review => 
        review._id === updatedReview._id ? updatedReview : review
      )
    );
    // Recalculate stats
    fetchUserReviews(pagination.currentPage);
  };

  const handleReviewDeleted = (reviewId: string) => {
    setReviews(prev => prev.filter(review => review._id !== reviewId));
    setStats(prev => ({
      ...prev,
      totalReviews: Math.max(0, prev.totalReviews - 1)
    }));
  };

  const handlePageChange = (newPage: number) => {
    fetchUserReviews(newPage);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[300px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-green-600">{stats.totalReviews}</div>
            <div className="text-sm text-gray-600">Total Reviews</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 text-center">
            <div className="flex items-center justify-center mb-1">
              <Star className="h-5 w-5 text-yellow-400 fill-current mr-1" />
              <span className="text-2xl font-bold text-green-600">
                {stats.averageRating.toFixed(1)}
              </span>
            </div>
            <div className="text-sm text-gray-600">Average Rating</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 text-center">
            <div className="flex items-center justify-center mb-1">
              <Package className="h-5 w-5 text-blue-500 mr-1" />
              <span className="text-2xl font-bold text-green-600">{stats.menuItemReviews}</span>
            </div>
            <div className="text-sm text-gray-600">Item Reviews</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 text-center">
            <div className="flex items-center justify-center mb-1">
              <Calendar className="h-5 w-5 text-purple-500 mr-1" />
              <span className="text-2xl font-bold text-green-600">{stats.vendorReviews}</span>
            </div>
            <div className="text-sm text-gray-600">Vendor Reviews</div>
          </CardContent>
        </Card>
      </div>

      {/* Reviews List */}
      {reviews.length > 0 ? (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-gray-800">Your Reviews</h3>
          
          {reviews.map((review) => (
            <div key={review._id} className="relative">
              {/* Target Type Badge */}
              <div className="absolute top-4 right-4 z-10">
                <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                  review.targetType === 'MenuItem' 
                    ? 'bg-blue-100 text-blue-800' 
                    : 'bg-purple-100 text-purple-800'
                }`}>
                  {review.targetType === 'MenuItem' ? 'Menu Item' : 'Vendor'}
                </span>
              </div>
              
              <ReviewCard
                review={review}
                currentUserId={userId}
                onReviewUpdated={handleReviewUpdated}
                onReviewDeleted={handleReviewDeleted}
                showActions={true}
              />
            </div>
          ))}

          {/* Pagination */}
          {pagination.totalPages > 1 && (
            <div className="flex justify-center items-center gap-4 mt-8">
              <Button
                variant="outline"
                onClick={() => handlePageChange(pagination.currentPage - 1)}
                disabled={!pagination.hasPrevPage}
              >
                <ChevronLeft size={16} className="mr-1" />
                Previous
              </Button>
              
              <span className="text-sm text-gray-600">
                Page {pagination.currentPage} of {pagination.totalPages}
              </span>
              
              <Button
                variant="outline"
                onClick={() => handlePageChange(pagination.currentPage + 1)}
                disabled={!pagination.hasNextPage}
              >
                Next
                <ChevronRight size={16} className="ml-1" />
              </Button>
            </div>
          )}
        </div>
      ) : (
        <Card>
          <CardContent className="p-8 text-center">
            <Star className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-700 mb-2">
              No Reviews Yet
            </h3>
            <p className="text-gray-500 mb-4">
              You haven't written any reviews yet. Start by ordering some delicious food and sharing your experience!
            </p>
            <Button
              onClick={() => window.location.href = '/'}
              className="bg-green-600 hover:bg-green-700 text-white"
            >
              Start Ordering
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
