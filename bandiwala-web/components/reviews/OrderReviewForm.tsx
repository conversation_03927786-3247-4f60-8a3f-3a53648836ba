'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { X, Star } from 'lucide-react';
import RatingStars from './RatingStars';
import { reviewService } from '@/services/api';
import { CreateReviewData } from '@/types/review';
import { toast } from 'sonner';
import Image from 'next/image';
import { getImageUrl } from '@/utils/imageUtils';

interface OrderReviewFormProps {
  orderId: string;
  menuItemId: string;
  itemName: string;
  itemImage: string;
  onReviewSubmitted?: () => void;
  onCancel?: () => void;
}

export default function OrderReviewForm({
  orderId,
  menuItemId,
  itemName,
  itemImage,
  onReviewSubmitted,
  onCancel
}: OrderReviewFormProps) {
  const [rating, setRating] = useState(0);
  const [comment, setComment] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (rating === 0) {
      toast.error('Please select a rating');
      return;
    }

    if (comment.trim().length < 10) {
      toast.error('Please write at least 10 characters in your review');
      return;
    }

    setIsSubmitting(true);

    try {
      const reviewData: CreateReviewData = {
        targetType: 'MenuItem',
        targetId: menuItemId,
        orderId,
        rating,
        comment: comment.trim()
      };

      const response = await reviewService.createReview(reviewData);

      if (response.success) {
        toast.success('Review submitted successfully!');
        
        if (onReviewSubmitted) {
          onReviewSubmitted();
        }

        // Reset form
        setRating(0);
        setComment('');
      } else {
        toast.error(response.message || 'Failed to submit review');
      }
    } catch (error) {
      console.error('Error submitting review:', error);
      toast.error('An error occurred while submitting your review');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">Write a Review</CardTitle>
          {onCancel && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onCancel}
              className="h-8 w-8 p-0"
            >
              <X size={16} />
            </Button>
          )}
        </div>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Product Info */}
        <div className="flex items-center gap-4 p-4 bg-gray-50 rounded-lg">
          <div className="w-16 h-16 rounded-lg overflow-hidden bg-gray-200 flex-shrink-0">
            <Image
              src={getImageUrl(itemImage)}
              alt={itemName}
              width={64}
              height={64}
              className="w-full h-full object-cover"
            />
          </div>
          <div>
            <h3 className="font-medium text-gray-900">{itemName}</h3>
            <p className="text-sm text-gray-600">
              <span className="inline-flex items-center gap-1">
                <Star size={12} className="text-green-600 fill-current" />
                Verified Purchase
              </span>
            </p>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Rating Section */}
          <div className="space-y-3">
            <label className="block text-sm font-medium text-gray-700">
              Rate this item *
            </label>
            <div className="flex items-center gap-3">
              <RatingStars
                rating={rating}
                interactive={true}
                onRatingChange={setRating}
                size={28}
                className="gap-1"
              />
              <span className="text-sm text-gray-600">
                {rating > 0 ? `${rating} out of 5 stars` : 'Click to rate'}
              </span>
            </div>
          </div>

          {/* Comment Section */}
          <div className="space-y-3">
            <label className="block text-sm font-medium text-gray-700">
              Share your experience *
            </label>
            <Textarea
              value={comment}
              onChange={(e) => setComment(e.target.value)}
              placeholder="Tell others about your experience with this item. What did you like or dislike about it?"
              className="min-h-[120px] resize-none"
              maxLength={500}
            />
            <div className="flex justify-between text-xs text-gray-500">
              <span>Minimum 10 characters required</span>
              <span>{comment.length}/500</span>
            </div>
          </div>

          {/* Guidelines */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h4 className="text-sm font-medium text-blue-900 mb-2">Review Guidelines</h4>
            <ul className="text-xs text-blue-800 space-y-1">
              <li>• Be honest and helpful to other customers</li>
              <li>• Focus on the product quality, taste, and value</li>
              <li>• Avoid inappropriate language or personal information</li>
              <li>• Your review will be publicly visible</li>
            </ul>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3 pt-4">
            <Button
              type="submit"
              disabled={isSubmitting || rating === 0 || comment.trim().length < 10}
              className="bg-bandiwala-orange hover:bg-bandiwala-red text-white flex-1"
            >
              {isSubmitting ? 'Submitting...' : 'Submit Review'}
            </Button>
            {onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={isSubmitting}
                className="flex-1"
              >
                Cancel
              </Button>
            )}
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
