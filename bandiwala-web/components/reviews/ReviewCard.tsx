'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { MoreVertical, Edit, Trash2, CheckCircle, Flag, AlertTriangle } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import RatingStars from './RatingStars';
import ReviewForm from './ReviewForm';
import { Review } from '@/types/review';
import { reviewService } from '@/services/api';
import { toast } from 'sonner';

interface ReviewCardProps {
  review: Review;
  currentUserId?: string;
  onReviewUpdated?: (updatedReview: Review) => void;
  onReviewDeleted?: (reviewId: string) => void;
  showActions?: boolean;
  showModerationInfo?: boolean;
}

export default function ReviewCard({
  review,
  currentUserId,
  onReviewUpdated,
  onReviewDeleted,
  showActions = true,
  showModerationInfo = false
}: ReviewCardProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [showFlagDialog, setShowFlagDialog] = useState(false);
  const [flagReason, setFlagReason] = useState('');

  const isOwner = currentUserId && review.userId._id === currentUserId;
  const formattedDate = new Date(review.createdAt).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });

  const getInitials = (name: string | undefined) => {
    if (!name) return 'U'; // Default to 'U' for User if name is undefined
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleDelete = async () => {
    if (!window.confirm('Are you sure you want to delete this review?')) {
      return;
    }

    setIsDeleting(true);

    try {
      const response = await reviewService.deleteReview(review._id);

      if (response.success) {
        toast.success('Review deleted successfully');
        if (onReviewDeleted) {
          onReviewDeleted(review._id);
        }
      } else {
        toast.error(response.message || 'Failed to delete review');
      }
    } catch (error) {
      console.error('Error deleting review:', error);
      toast.error('An error occurred while deleting the review');
    } finally {
      setIsDeleting(false);
    }
  };

  const handleReviewUpdated = (updatedReview: Review) => {
    setIsEditing(false);
    if (onReviewUpdated) {
      onReviewUpdated(updatedReview);
    }
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
  };

  const handleFlag = async () => {
    if (!flagReason.trim() || flagReason.trim().length < 5) {
      toast.error('Please provide a reason for flagging (minimum 5 characters)');
      return;
    }

    try {
      const response = await reviewService.flagReview(review._id, flagReason.trim());

      if (response.success) {
        toast.success('Review flagged successfully');
        setShowFlagDialog(false);
        setFlagReason('');
      } else {
        toast.error(response.message || 'Failed to flag review');
      }
    } catch (error) {
      console.error('Error flagging review:', error);
      toast.error('An error occurred while flagging the review');
    }
  };

  if (isEditing) {
    return (
      <ReviewForm
        targetType={review.targetType}
        targetId={review.targetId}
        targetName="this item" // This could be improved by passing the actual name
        existingReview={review}
        isEditing={true}
        onReviewSubmitted={handleReviewUpdated}
        onCancel={handleCancelEdit}
      />
    );
  }

  if (showFlagDialog) {
    return (
      <Card className="w-full">
        <CardContent className="p-4 lg:p-6">
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Flag size={20} className="text-red-500" />
              <h3 className="text-lg font-semibold">Flag Review</h3>
            </div>
            <p className="text-gray-600">
              Please provide a reason for flagging this review. This will help our moderation team review the content.
            </p>
            <Textarea
              value={flagReason}
              onChange={(e) => setFlagReason(e.target.value)}
              placeholder="Reason for flagging (minimum 5 characters)..."
              className="min-h-[80px]"
              maxLength={200}
            />
            <div className="flex justify-between text-xs text-gray-500">
              <span>Minimum 5 characters required</span>
              <span>{flagReason.length}/200</span>
            </div>
            <div className="flex gap-3">
              <Button
                onClick={handleFlag}
                disabled={flagReason.trim().length < 5}
                className="bg-red-500 hover:bg-red-600 text-white"
              >
                Submit Flag
              </Button>
              <Button
                variant="outline"
                onClick={() => {
                  setShowFlagDialog(false);
                  setFlagReason('');
                }}
              >
                Cancel
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full">
      {/* Moderation Status Banner */}
      {showModerationInfo && review.moderationStatus && review.moderationStatus !== 'approved' && (
        <div className={`px-4 py-2 text-sm font-medium ${
          review.moderationStatus === 'pending'
            ? 'bg-yellow-100 text-yellow-800'
            : review.moderationStatus === 'flagged'
            ? 'bg-orange-100 text-orange-800'
            : 'bg-red-100 text-red-800'
        }`}>
          <div className="flex items-center gap-2">
            <AlertTriangle size={16} />
            <span>
              Status: {review.moderationStatus.charAt(0).toUpperCase() + review.moderationStatus.slice(1)}
              {review.moderationReason && ` - ${review.moderationReason}`}
            </span>
          </div>
        </div>
      )}

      <CardContent className="p-4 lg:p-6">
        <div className="flex items-start gap-4">
          {/* Avatar */}
          <div className="w-12 h-12 bg-bandiwala-orange rounded-full flex items-center justify-center text-white font-semibold text-lg flex-shrink-0">
            {review.userId?.profileImage ? (
              <img
                src={review.userId.profileImage}
                alt={review.userId?.name || 'User'}
                className="w-full h-full rounded-full object-cover"
              />
            ) : (
              getInitials(review.userId?.name)
            )}
          </div>

          {/* Review Content */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center gap-2">
                <h3 className="font-semibold text-gray-900">
                  {review.userId?.name || 'Anonymous User'}
                </h3>
                {review.isVerifiedPurchase && (
                  <CheckCircle size={16} className="text-green-500" />
                )}
              </div>
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-500">{formattedDate}</span>
                {showActions && (
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 w-8 p-0"
                        disabled={isDeleting}
                      >
                        <MoreVertical size={16} />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      {isOwner ? (
                        <>
                          <DropdownMenuItem onClick={handleEdit}>
                            <Edit size={16} className="mr-2" />
                            Edit Review
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={handleDelete}
                            className="text-red-600"
                            disabled={isDeleting}
                          >
                            <Trash2 size={16} className="mr-2" />
                            {isDeleting ? 'Deleting...' : 'Delete Review'}
                          </DropdownMenuItem>
                        </>
                      ) : (
                        <DropdownMenuItem
                          onClick={() => setShowFlagDialog(true)}
                          className="text-orange-600"
                        >
                          <Flag size={16} className="mr-2" />
                          Flag Review
                        </DropdownMenuItem>
                      )}
                    </DropdownMenuContent>
                  </DropdownMenu>
                )}
              </div>
            </div>

            {/* Rating Stars */}
            <div className="flex items-center mb-3">
              <RatingStars rating={review.rating} size={16} />
              <span className="ml-2 text-sm text-gray-600">
                {review.rating} out of 5
              </span>
            </div>

            {/* Comment */}
            <p className="text-gray-700 leading-relaxed">{review.comment}</p>

            {/* Helpful votes (if implemented) */}
            {review.helpfulVotes > 0 && (
              <div className="mt-3 text-sm text-gray-500">
                {review.helpfulVotes} people found this helpful
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
