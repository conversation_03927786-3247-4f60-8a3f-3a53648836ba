'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import RatingStars from './RatingStars';
import { reviewService } from '@/services/api';
import { CreateReviewData, Review } from '@/types/review';
import { toast } from 'sonner';

interface ReviewFormProps {
  targetType: 'MenuItem' | 'Vendor';
  targetId: string;
  targetName: string;
  orderId?: string; // Required for new reviews, optional for editing
  onReviewSubmitted?: (review: Review) => void;
  onCancel?: () => void;
  existingReview?: Review;
  isEditing?: boolean;
}

export default function ReviewForm({
  targetType,
  targetId,
  targetName,
  orderId,
  onReviewSubmitted,
  onCancel,
  existingReview,
  isEditing = false
}: ReviewFormProps) {
  const [rating, setRating] = useState(existingReview?.rating || 0);
  const [comment, setComment] = useState(existingReview?.comment || '');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (rating === 0) {
      toast.error('Please select a rating');
      return;
    }

    if (comment.trim().length < 10) {
      toast.error('Please write at least 10 characters in your review');
      return;
    }

    setIsSubmitting(true);

    try {
      let response;

      if (isEditing && existingReview) {
        // Update existing review
        response = await reviewService.updateReview(existingReview._id, {
          rating,
          comment: comment.trim()
        });
      } else {
        // Create new review - require orderId for verified purchases
        if (!orderId) {
          toast.error('Order ID is required for verified purchase reviews');
          return;
        }

        const reviewData: CreateReviewData = {
          targetType,
          targetId,
          rating,
          comment: comment.trim(),
          orderId
        };
        response = await reviewService.createReview(reviewData);
      }

      if (response.success) {
        toast.success(
          isEditing
            ? 'Review updated successfully!'
            : 'Review submitted successfully!'
        );

        if (onReviewSubmitted && 'data' in response && response.data) {
          onReviewSubmitted(response.data);
        }

        // Reset form if creating new review
        if (!isEditing) {
          setRating(0);
          setComment('');
        }
      } else {
        const errorMessage = 'message' in response ? response.message : 'Failed to submit review';
        toast.error(errorMessage);
      }
    } catch (error) {
      console.error('Error submitting review:', error);
      toast.error('An error occurred while submitting your review');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="text-lg">
          {isEditing ? 'Edit Your Review' : `Write a Review for ${targetName}`}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Rating Section */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              Rating *
            </label>
            <div className="flex items-center gap-2">
              <RatingStars
                rating={rating}
                interactive={true}
                onRatingChange={setRating}
                size={24}
                className="gap-1"
              />
              <span className="text-sm text-gray-600 ml-2">
                {rating > 0 ? `${rating} out of 5 stars` : 'Select a rating'}
              </span>
            </div>
          </div>

          {/* Comment Section */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              Your Review *
            </label>
            <Textarea
              value={comment}
              onChange={(e) => setComment(e.target.value)}
              placeholder="Share your experience with this item/vendor..."
              className="min-h-[100px] resize-none"
              maxLength={500}
            />
            <div className="flex justify-between text-xs text-gray-500">
              <span>Minimum 10 characters required</span>
              <span>{comment.length}/500</span>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3 pt-4">
            <Button
              type="submit"
              disabled={isSubmitting || rating === 0 || comment.trim().length < 10}
              className="bg-bandiwala-orange hover:bg-bandiwala-red text-white flex-1"
            >
              {isSubmitting 
                ? (isEditing ? 'Updating...' : 'Submitting...') 
                : (isEditing ? 'Update Review' : 'Submit Review')
              }
            </Button>
            {onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={isSubmitting}
                className="flex-1"
              >
                Cancel
              </Button>
            )}
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
