'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Plus, ChevronLeft, ChevronRight, CheckCircle } from 'lucide-react';
import RatingStars from './RatingStars';
import ReviewCard from './ReviewCard';
import ReviewForm from './ReviewForm';
import { Review, ReviewsResponse } from '@/types/review';
import { reviewService } from '@/services/api';
import { toast } from 'sonner';

interface ReviewsListProps {
  targetType: 'MenuItem' | 'Vendor';
  targetId: string;
  targetName: string;
  currentUserId?: string;
  isAuthenticated?: boolean;
}

export default function ReviewsList({
  targetType,
  targetId,
  targetName,
  currentUserId,
  isAuthenticated = false
}: ReviewsListProps) {
  const [reviews, setReviews] = useState<Review[]>([]);
  const [stats, setStats] = useState({ averageRating: 0, totalReviews: 0 });
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 0,
    totalReviews: 0,
    hasNextPage: false,
    hasPrevPage: false
  });
  const [loading, setLoading] = useState(true);
  const [showReviewForm, setShowReviewForm] = useState(false);
  const [userHasReviewed, setUserHasReviewed] = useState(false);

  const fetchReviews = async (page: number = 1) => {
    try {
      setLoading(true);
      const response = await reviewService.getReviewsByTarget(
        targetType,
        targetId,
        page,
        10
      );

      if (response.success && response.data) {
        setReviews(response.data.reviews);
        setStats(response.data.stats);
        setPagination(response.data.pagination);

        // Check if current user has already reviewed
        if (currentUserId) {
          const userReview = response.data.reviews.find(
            (review: Review) => review.userId._id === currentUserId
          );
          setUserHasReviewed(!!userReview);
        }
      }
    } catch (error) {
      console.error('Error fetching reviews:', error);
      toast.error('Failed to load reviews');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchReviews();
  }, [targetType, targetId, currentUserId]);

  const handleReviewSubmitted = (newReview: Review) => {
    setReviews(prev => [newReview, ...prev]);
    setStats(prev => ({
      averageRating: ((prev.averageRating * prev.totalReviews) + newReview.rating) / (prev.totalReviews + 1),
      totalReviews: prev.totalReviews + 1
    }));
    setUserHasReviewed(true);
    setShowReviewForm(false);
    toast.success('Review submitted successfully!');
  };

  const handleReviewUpdated = (updatedReview: Review) => {
    setReviews(prev => 
      prev.map(review => 
        review._id === updatedReview._id ? updatedReview : review
      )
    );
    // Recalculate stats
    fetchReviews(pagination.currentPage);
  };

  const handleReviewDeleted = (reviewId: string) => {
    setReviews(prev => prev.filter(review => review._id !== reviewId));
    setStats(prev => ({
      averageRating: prev.totalReviews > 1 ? prev.averageRating : 0,
      totalReviews: Math.max(0, prev.totalReviews - 1)
    }));
    
    // Check if deleted review was user's review
    const deletedReview = reviews.find(review => review._id === reviewId);
    if (deletedReview && deletedReview.userId._id === currentUserId) {
      setUserHasReviewed(false);
    }
  };

  const handlePageChange = (newPage: number) => {
    fetchReviews(newPage);
  };

  if (loading) {
    return (
      <div className="mt-8 lg:mt-12">
        <div className="border-t border-gray-200 pt-8">
          <div className="flex justify-center items-center min-h-[200px]">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-bandiwala-orange"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="mt-8 lg:mt-12">
      <div className="border-t border-gray-200 pt-8">
        {/* Reviews Header */}
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl lg:text-3xl font-bold text-gray-900">
            Customer Reviews
          </h2>
          <div className="flex items-center gap-2">
            <div className="flex items-center">
              <RatingStars rating={Math.round(stats.averageRating)} size={20} />
            </div>
            <span className="text-lg font-semibold text-gray-700">
              {stats.averageRating.toFixed(1)} ({stats.totalReviews} reviews)
            </span>
          </div>
        </div>

        {/* Info about verified reviews */}
        <div className="mb-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center gap-2 mb-2">
            <CheckCircle size={16} className="text-blue-600" />
            <span className="font-medium text-blue-900">Verified Purchase Reviews</span>
          </div>
          <p className="text-sm text-blue-800">
            All reviews are from verified purchases. You can write reviews for items you've ordered from your Orders page.
          </p>
        </div>

        {/* Reviews List */}
        {reviews.length > 0 ? (
          <div className="space-y-4">
            {reviews.map((review) => (
              <ReviewCard
                key={review._id}
                review={review}
                currentUserId={currentUserId}
                onReviewUpdated={handleReviewUpdated}
                onReviewDeleted={handleReviewDeleted}
              />
            ))}

            {/* Pagination */}
            {pagination.totalPages > 1 && (
              <div className="flex justify-center items-center gap-4 mt-8">
                <Button
                  variant="outline"
                  onClick={() => handlePageChange(pagination.currentPage - 1)}
                  disabled={!pagination.hasPrevPage}
                >
                  <ChevronLeft size={16} className="mr-1" />
                  Previous
                </Button>
                
                <span className="text-sm text-gray-600">
                  Page {pagination.currentPage} of {pagination.totalPages}
                </span>
                
                <Button
                  variant="outline"
                  onClick={() => handlePageChange(pagination.currentPage + 1)}
                  disabled={!pagination.hasNextPage}
                >
                  Next
                  <ChevronRight size={16} className="ml-1" />
                </Button>
              </div>
            )}
          </div>
        ) : (
          <Card>
            <CardContent className="p-8 text-center">
              <p className="text-gray-500 text-lg">
                No reviews yet for this {targetType.toLowerCase()}.
              </p>
              <p className="text-gray-400 text-sm mt-2">
                Reviews can only be written by customers who have ordered and received this item.
              </p>
            </CardContent>
          </Card>
        )}

        {/* Info for unauthenticated users */}
        {!isAuthenticated && reviews.length === 0 && (
          <Card className="mt-6">
            <CardContent className="p-6 text-center">
              <p className="text-gray-600 mb-4">
                Reviews are from verified purchases only. Order items to share your experience!
              </p>
              <Button
                onClick={() => window.location.href = '/login'}
                className="bg-bandiwala-orange hover:bg-bandiwala-red text-white"
              >
                Log In to Order
              </Button>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
