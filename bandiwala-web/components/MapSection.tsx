import React from 'react';
import { MapPin } from 'lucide-react';
import VendorMap from '@/components/maps/VendorMap';

const MapSection = () => {
  return (
    <div className="section-container">
      <div className="max-w-5xl mx-auto">
        <h2 className="section-title">Find Vendors Near You</h2>
        <p className="section-subtitle">Discover street food vendors and home chefs in your neighborhood</p>
        <p className="text-gray-600">We&apos;re bringing the best street food vendors closer to you</p>
        
        <div className="mt-8">
          <VendorMap height="400px" zoom={15} />
        </div>
        
        <div className="mt-8 flex flex-col sm:flex-row justify-center gap-4">
          <button className="px-6 py-3 bg-bandiwala-orange text-white rounded-full font-medium hover:bg-bandiwala-red transition-colors" onClick={() => window.open('https://forms.gle/cF2iTJ1MjAxiky6v6', '_blank')}>
            Become a Food Vendor
          </button>
        </div>
      </div>
    </div>
  );
};

export default MapSection;
