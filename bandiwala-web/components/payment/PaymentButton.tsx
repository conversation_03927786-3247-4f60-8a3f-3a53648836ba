'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { loadRazorpay } from '@/lib/loadRazorpay';
import { toast } from 'sonner';

interface PaymentButtonProps {
  amount: number;
  onSuccess?: (response: RazorpayCheckout.RazorpayResponse) => void;
  onFailure?: (error: any) => void;
  onCancel?: () => void;
  buttonText?: string;
  className?: string;
  disabled?: boolean;
  userData?: {
    name?: string;
    email?: string;
    phone?: string;
  };
}

export default function PaymentButton({
  amount,
  onSuccess,
  onFailure,
  onCancel,
  buttonText = 'Proceed to Pay',
  className = '',
  disabled = false,
  userData = {}
}: PaymentButtonProps) {
  const router = useRouter();
  const [isProcessing, setIsProcessing] = useState(false);

  const handlePayment = async () => {
    setIsProcessing(true);

    try {
      // Create a Razorpay order
      const response = await fetch('/api/create-order', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ amount }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to create payment order');
      }
      
      const { orderId } = await response.json();
      
      // Load Razorpay SDK
      const isLoaded = await loadRazorpay();
      if (!isLoaded) {
        toast.error('Failed to load payment gateway. Please try again.');
        return;
      }
      
      // Configure Razorpay options
      const options: RazorpayCheckout.Options = {
        key: process.env.NEXT_PUBLIC_RAZORPAY_KEY_ID!,
        amount: amount * 100, // Amount in paise
        currency: 'INR',
        name: 'Bandiwala',
        description: 'Food Order Payment',
        order_id: orderId,
        handler: async (response: RazorpayCheckout.RazorpayResponse) => {
          // Verify the payment
          const verifyResponse = await fetch('/api/verify', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(response),
          });
          
          if (verifyResponse.ok) {
            const verifyData = await verifyResponse.json();
            if (verifyData.success) {
              if (onSuccess) {
                onSuccess(response);
              } else {
                toast.success('Payment successful!');
              }
            } else {
              if (onFailure) {
                onFailure(new Error(verifyData.message || 'Payment verification failed'));
              } else {
                toast.error(verifyData.message || 'Payment verification failed. Please contact support.');
              }
            }
          } else {
            if (onFailure) {
              onFailure(new Error('Payment verification failed'));
            } else {
              toast.error('Payment verification failed. Please contact support.');
            }
          }
        },
        prefill: {
          name: userData.name || '',
          email: userData.email || '',
          contact: userData.phone || '',
        },
        theme: {
          color: '#f97316', // bandiwala-orange
        },
        modal: {
          ondismiss: function() {
            if (onCancel) {
              onCancel();
            } else {
              toast.info('Payment cancelled. You can try again.');
            }
            setIsProcessing(false);
          },
        },
      };
      
      // Initialize Razorpay
      const razorpay = new window.Razorpay(options);
      razorpay.open();
    } catch (error) {
      console.error('Payment error:', error);
      if (onFailure) {
        onFailure(error);
      } else {
        toast.error('Failed to process payment. Please try again.');
      }
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <Button
      onClick={handlePayment}
      disabled={isProcessing || disabled}
      className={className}
    >
      {isProcessing ? (
        <>
          <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
          Processing...
        </>
      ) : (
        buttonText
      )}
    </Button>
  );
}
