'use client'
import React from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { MapPin, ShoppingBag, Utensils } from 'lucide-react';

const ServiceButtons = () => {
  const router = useRouter();
  const services = [
    {
      title: 'Food Delivery & pickup',
      description: 'Order your favorite food from local vendors',
      icon: <ShoppingBag className="h-8 w-8 mb-4 text-white" />,
      bgColor: 'bg-bandiwala-orange',
      action: () => router.push('/dishes')
    },
    {
      title: 'Home Food',
      description: 'Order your favorite food from home chefs',
      icon: <MapPin className="h-8 w-8 mb-4 text-white" />,
      bgColor: 'bg-bandiwala-red',
      action: () => router.push('/coming-soon')
    },
    {
      title: 'Catering',
      description: 'Perfect for events and gatherings',
      icon: <Utensils className="h-8 w-8 mb-4 text-white" />,
      bgColor: 'bg-bandiwala-brown',
      action: () => router.push('/coming-soon')
    },
  ];

  return (
    <div className="section-container">
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 max-w-5xl mx-auto">
        {services.map((service, index) => (
          <div
            key={index}
            className={`${service.bgColor} rounded-xl p-6 sm:p-8 text-center shadow-md transform transition-all duration-300 hover:scale-105 cursor-pointer`}
            onClick={service.action}
          >
            <div className="flex flex-col items-center">
              <div className="mb-3 sm:mb-4 flex items-center justify-center">
                {service.icon}
              </div>
              <h3 className="text-xl sm:text-2xl font-semibold text-white mb-2 sm:mb-3">{service.title}</h3>
              <p className="text-white text-opacity-90 mb-4 sm:mb-6 text-sm sm:text-base leading-relaxed">{service.description}</p>
              <Button
                className="bg-white hover:bg-gray-100 text-gray-800 font-semibold w-full sm:w-auto px-6 py-2"
                onClick={service.action}
              >
                Order Now
              </Button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ServiceButtons;
