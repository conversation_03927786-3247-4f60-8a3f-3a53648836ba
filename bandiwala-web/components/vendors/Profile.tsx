'use client'
import React from 'react';
import { <PERSON>, MapPin, <PERSON>CheckBig, ExternalLink } from 'lucide-react'
import Image from 'next/image';
import bandi from '@/assets/images/chat_bandi.png';
import { getImageUrl } from '@/utils/imageUtils';

interface ProfileProps {
  vendor?: {
    _id?: string;
    name: string;
    rating: number;
    description: string;
    location?: string;
    image?: string;
    deliveryTime?: string;
    deliveryFee?: number;
    minOrderValue?: number;
  };
}

function Profile({ vendor }: ProfileProps) {
  return (
    <div className='bg-[#fff5e2b7] min-h-[200px] sm:min-h-[250px] lg:min-h-[300px] rounded-b-[30px] sm:rounded-b-[50px] lg:rounded-b-[100px] px-3 sm:px-6 lg:px-8 py-4 sm:py-6 lg:py-8'>
      <div className='flex flex-row justify-between items-start gap-4 sm:gap-6 lg:gap-8 max-w-7xl mx-auto'>
        {/* Content Section */}
        <div className='flex flex-col gap-3 sm:gap-4 flex-1 min-w-0'>
          {/* Header with name and rating */}
          <div className='flex flex-col gap-3 sm:gap-4'>
            <h2 className='text-xl sm:text-2xl md:text-3xl lg:text-4xl text-black font-bold leading-tight break-words'>
              {vendor?.name || 'Loading...'}
            </h2>

            {/* Description */}
            {vendor?.description && (
              <p className='text-sm sm:text-base md:text-lg text-gray-700 leading-relaxed break-words'>
                {vendor.description}
              </p>
            )}

            <div className='bg-[#279400] text-white px-3 py-1.5 rounded-lg flex items-center gap-1 shrink-0 self-start w-fit'>
              <span className="text-sm md:text-base font-medium">{vendor?.rating || '0.0'}</span>
              <Star fill="#fff" size={16} className="text-white" />
            </div>
          </div>

          {/* Location Link */}
          {vendor?.location && (
            <div className='flex items-start gap-2'>
              <MapPin className="text-orange-500 shrink-0 mt-0.5" size={18} />
              <a
                href={vendor.location}
                target="_blank"
                rel="noopener noreferrer"
                className="text-sm md:text-base lg:text-lg text-blue-600 hover:text-blue-800 underline decoration-1 underline-offset-2 hover:decoration-2 transition-all duration-200 flex items-center gap-1 break-words"
              >
                <span>View Location</span>
                <ExternalLink size={14} className="shrink-0" />
              </a>
            </div>
          )}

          {/* Pickup and Delivery */}
          <div className='flex flex-wrap gap-4 sm:gap-6 pb-3 sm:pb-4 border-b border-[#ccc]'>
            <label className='flex items-center gap-2 text-sm md:text-base text-[#333]'>
              <CircleCheckBig className="text-green-600" size={18} />
              Delivery
            </label>

            <label className='flex items-center gap-2 text-sm md:text-base text-[#333]'>
              <CircleCheckBig className="text-green-600" size={18} />
              <span>{vendor?.deliveryTime || '20-30 min'}</span>
            </label>
          </div>
        </div>
        
        {/* Image Section - Always on the right */}
        <div className='shrink-0 self-start flex justify-end'>
          {vendor?.image ? (
            <Image
              src={getImageUrl(vendor.image)}
              alt={vendor.name}
              width={400}
              height={400}
              className="w-32 h-32 sm:w-40 sm:h-40 md:w-48 md:h-48 lg:w-56 lg:h-56 xl:w-64 xl:h-64 object-cover rounded-lg shadow-md"
            />
          ) : (
            <Image
              src={bandi}
              alt="Bandi Image"
              className="w-32 h-32 sm:w-40 sm:h-40 md:w-48 md:h-48 lg:w-56 lg:h-56 xl:w-64 xl:h-64 object-cover rounded-lg shadow-md"
            />
          )}
        </div>
      </div>
    </div>
  );
}

export default Profile;