import React from 'react';
import Image from 'next/image';
import { Star } from 'lucide-react';
import image from '@/assets/images/image.png';

function Recipes() {
  return (
    <div className="flex flex-col sm:flex-row justify-between items-start p-3 sm:p-4 md:p-6 rounded-lg shadow-lg bg-white max-w-full mx-auto my-2 sm:my-3 md:my-4">
      <div className="flex-1 w-full sm:w-auto mb-3 sm:mb-0 sm:pr-4">
        <h2 className="text-sm sm:text-lg md:text-xl lg:text-2xl font-bold mb-1 sm:mb-2">Veg <PERSON>ied <PERSON></h2>

        <div className="flex mb-1 sm:mb-2">
          <Star fill="gold" color="gold" className="text-xs sm:text-sm md:text-base"/>
          <Star fill="gold" color="gold" className="text-xs sm:text-sm md:text-base"/>
          <Star fill="gold" color="gold" className="text-xs sm:text-sm md:text-base"/>
          <Star fill="gold" color="gold" className="text-xs sm:text-sm md:text-base"/>
          <Star fill="gold" color="gold" className="text-xs sm:text-sm md:text-base"/>
        </div>

        <p className="font-bold text-sm sm:text-base md:text-lg mb-2 sm:mb-3">₹70</p>

        <p className="text-xs sm:text-sm md:text-base leading-relaxed text-gray-800 max-w-full sm:max-w-xl line-clamp-2 sm:line-clamp-3">
          Lorem ipsum dolor sit amet consectetur. Vestibulum et pellentesque viverra leo odio aenean mauris non.
          Morbi lacus et sed proin mauris pellentesque eget non. Sodales dictum vitae suspendisse condimentum est eu neque.
        </p>
      </div>

      <div className="flex flex-row sm:flex-col items-center gap-2 sm:gap-3 w-full sm:w-auto justify-between sm:justify-center">
        <Image
          src={image}
          alt="Veg Fried Rice"
          width={80} // Smaller for mobile
          height={80} // Smaller for mobile
          className="rounded-lg w-16 h-16 sm:w-20 sm:h-20 md:w-24 md:h-24 lg:w-32 lg:h-32 object-cover"
        />
        <button className="bg-orange-500 text-white font-bold py-1.5 px-4 sm:py-2 sm:px-6 md:px-8 rounded-lg cursor-pointer text-xs sm:text-sm md:text-base hover:bg-orange-600 transition-colors">
          Add
        </button>
      </div>
    </div>
  );
}

export default Recipes;