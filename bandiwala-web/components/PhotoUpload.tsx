'use client';

import React, { useState, useRef } from 'react';
import { Camera, Upload, X, User } from 'lucide-react';
import Image from 'next/image';
import { toast } from 'sonner';
import { getImageUrl } from '@/utils/imageUtils';
import { authService } from '@/services/api';

interface PhotoUploadProps {
  currentImage?: string;
  onUploadSuccess: (imageUrl: string) => void;
  isUploading?: boolean;
}

const PhotoUpload: React.FC<PhotoUploadProps> = ({
  currentImage,
  onUploadSuccess,
  isUploading = false
}) => {
  const [preview, setPreview] = useState<string | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = (file: File) => {
    console.log('PhotoUpload: File selected:', {
      name: file.name,
      size: file.size,
      type: file.type,
      lastModified: file.lastModified
    });

    // Validate file type
    if (!file.type.startsWith('image/')) {
      console.log('PhotoUpload: Invalid file type:', file.type);
      toast.error('Please select an image file');
      return;
    }

    // Validate file size (5MB limit)
    if (file.size > 5 * 1024 * 1024) {
      console.log('PhotoUpload: File too large:', file.size);
      toast.error('Image size should be less than 5MB');
      return;
    }

    console.log('PhotoUpload: File validation passed, creating preview...');

    // Create preview
    const reader = new FileReader();
    reader.onload = (e) => {
      console.log('PhotoUpload: Preview created');
      setPreview(e.target?.result as string);
    };
    reader.readAsDataURL(file);

    // Upload file
    console.log('PhotoUpload: Starting upload...');
    uploadFile(file);
  };

  const uploadFile = async (file: File) => {
    try {
      console.log('PhotoUpload: Starting upload for file:', file.name, 'Size:', file.size, 'Type:', file.type);
      const data = await authService.uploadProfilePhoto(file);
      console.log('PhotoUpload: Upload response:', data);

      if (data.success) {
        toast.success('Profile photo updated successfully!');
        onUploadSuccess(data.imageUrl);
        setPreview(null);
      } else {
        throw new Error(data.message || 'Upload failed');
      }
    } catch (error) {
      console.error('PhotoUpload: Upload error:', error);
      toast.error('Failed to upload photo. Please try again.');
      setPreview(null);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);

    const files = e.dataTransfer.files;
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  };

  const handleClick = () => {
    fileInputRef.current?.click();
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  const clearPreview = () => {
    setPreview(null);
  };

  return (
    <div className="relative">
      {/* Current/Preview Image */}
      <div className="h-32 w-32 rounded-full border-4 border-white bg-white shadow-md flex items-center justify-center overflow-hidden relative group">
        {preview ? (
          <>
            <Image
              src={preview}
              alt="Preview"
              width={128}
              height={128}
              className="object-cover w-full h-full"
            />
            <button
              onClick={clearPreview}
              className="absolute top-1 right-1 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
              disabled={isUploading}
            >
              <X size={12} />
            </button>
          </>
        ) : currentImage ? (
          <Image
            src={getImageUrl(currentImage)}
            alt="Profile"
            width={128}
            height={128}
            className="object-cover w-full h-full"
          />
        ) : (
          <div className="h-full w-full bg-green-100 flex items-center justify-center">
            <User className="h-16 w-16 text-green-600" />
          </div>
        )}

        {/* Upload overlay */}
        {!preview && (
          <div
            className={`absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity cursor-pointer ${
              isDragging ? 'opacity-100 bg-opacity-70' : ''
            } ${isUploading ? 'opacity-100' : ''}`}
            onClick={handleClick}
            onDrop={handleDrop}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
          >
            {isUploading ? (
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
            ) : (
              <Camera className="h-8 w-8 text-white" />
            )}
          </div>
        )}
      </div>

      {/* Upload Button */}
      <button
        onClick={handleClick}
        disabled={isUploading}
        className="absolute -bottom-2 -right-2 bg-green-600 hover:bg-green-700 text-white rounded-full p-2 shadow-lg transition-colors disabled:opacity-50"
      >
        <Upload size={16} />
      </button>

      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileInputChange}
        className="hidden"
      />
    </div>
  );
};

export default PhotoUpload;
