'use client';

import { useRouter } from 'next/navigation';
import React, { useEffect, useState } from 'react';
import Image from 'next/image';
import { Card, CardContent } from '@/components/ui/card';
import { Star, Plus, Minus } from 'lucide-react';
import { useCart } from '@/contexts/CartContext';
import { useAuth } from '@/contexts/AuthContext';
import { menuItemService, vendorService } from '@/services/api';
import { getImageUrl } from '@/utils/imageUtils';
import { toast } from 'sonner';
import useUserLocation from '@/hooks/useUserLocation';

interface MenuItem {
  _id: string;
  itemName: string;
  slug?: string;
  description: string;
  subcategories: Array<{
    title: string;
    quantity: string;
    price: number;
  }>;
  image: string;
  vendorId: string;
  itemCategory: string;
  isAvailable: boolean;
}

interface Vendor {
  _id: string;
  name: string;
  description: string;
  slug: string;
  rating: number;
  location: string;
  image: string;
  deliveryTime: string;
  deliveryFee: number;
  minOrderValue: number;
  isActive: boolean;
}

const TrendingDishes = () => {
  const { addToCart, updateCartItem, cart } = useCart();
  const { isAuthenticated, isLoading: authLoading } = useAuth();
  const { coordinates } = useUserLocation();
  const router = useRouter();
  const [menuItems, setMenuItems] = useState<MenuItem[]>([]);
  const [vendors, setVendors] = useState<Record<string, Vendor>>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      // Only fetch data if user is authenticated
      if (!isAuthenticated) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);

        // Fetch all menu items
        const menuItemsResponse = await menuItemService.getAllMenuItems();
        if (!menuItemsResponse.success) {
          throw new Error('Failed to fetch menu items');
        }

        // Fetch all vendors
        const vendorsResponse = await vendorService.getAllVendors();
        if (!vendorsResponse.success) {
          throw new Error('Failed to fetch vendors');
        }

        // Create a map of vendor IDs to vendor objects for easy lookup
        const vendorMap: Record<string, Vendor> = {};
        vendorsResponse.data.forEach((vendor: Vendor) => {
          vendorMap[vendor._id] = vendor;
        });

        setMenuItems(menuItemsResponse.data);
        setVendors(vendorMap);
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Failed to load menu items. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    // Wait for auth to be determined before fetching
    if (!authLoading) {
      fetchData();
    }
  }, [isAuthenticated, authLoading]);

  // Get unique categories from menu items
  const categories = menuItems.reduce((acc: Record<string, MenuItem[]>, item) => {
    if (!acc[item.itemCategory]) {
      acc[item.itemCategory] = [];
    }
    acc[item.itemCategory].push(item);
    return acc;
  }, {});

  // Filter menu items by selected category or show all if none selected
  const filteredItems = selectedCategory
    ? menuItems.filter(item => item.itemCategory === selectedCategory)
    : menuItems;

  // Limit to 6 items for trending section
  const displayItems = filteredItems.slice(0, 6);

  // Handle item click navigation
  const handleItemClick = (item: MenuItem) => {
    // Use slug if available, otherwise fall back to ID
    const identifier = item.slug || item._id;
    router.push(`/items/${identifier}`);
  };

  // Helper function to check if item is in cart and get quantity
  const getItemCartInfo = (itemId: string) => {
    const cartItem = cart.items.find(item => item.menuItemId === itemId);
    return {
      isInCart: !!cartItem,
      quantity: cartItem?.quantity || 0
    };
  };

  // Handle quantity increase
  const handleIncreaseQuantity = (e: React.MouseEvent, item: MenuItem, vendor: Vendor | undefined) => {
    e.preventDefault();
    e.stopPropagation();

    // Check authentication before allowing cart operations
    if (!isAuthenticated) {
      toast("Please login to add items to cart", {
        position: 'top-right',
        duration: 3000,
        style: { backgroundColor: '#f44336', color: 'white' }
      });
      router.push('/login');
      return;
    }

    const { isInCart, quantity } = getItemCartInfo(item._id);

    if (isInCart) {
      updateCartItem(item._id, quantity + 1);
    } else {
      // Add to cart for the first time
      try {
        const defaultSubcategory = item.subcategories[0] || {
          title: "unit",
          quantity: "unit",
          price: 0
        };

        const itemDetails = {
          menuItemId: item._id,
          name: item.itemName,
          price: defaultSubcategory.price,
          image: item.image,
          vendorId: item.vendorId,
          vendorName: vendor?.name || 'Unknown Vendor'
        };
        localStorage.setItem(`item-details-${item._id}`, JSON.stringify(itemDetails));
        addToCart(item._id, 1, '', defaultSubcategory, coordinates || undefined);
      } catch (error) {
        console.error('Error adding item to cart:', error);
        toast("Failed to add item to cart", {
          position: 'top-right',
          duration: 3000,
          style: { backgroundColor: '#f44336', color: 'white' }
        });
      }
    }
  };

  // Handle quantity decrease
  const handleDecreaseQuantity = (e: React.MouseEvent, itemId: string) => {
    e.preventDefault();
    e.stopPropagation();

    const { quantity } = getItemCartInfo(itemId);

    if (quantity > 1) {
      updateCartItem(itemId, quantity - 1);
    } else {
      updateCartItem(itemId, 0); // This will remove the item
    }
  };

  // Don't render anything if not authenticated
  if (!isAuthenticated) {
    return null;
  }

  return (
    <div className="section-container">
      <h2 className="section-title">Trending Dishes</h2>
      <p className="section-subtitle">Discover what&apos;s popular in your area right now</p>
      {/* <p className="text-gray-600">We&apos;ve curated today&apos;s most popular street food dishes just for you</p> */}

      {(authLoading || loading) && (
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-bandiwala-orange"></div>
        </div>
      )}

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative my-4" role="alert">
          <strong className="font-bold">Error!</strong>
          <span className="block sm:inline"> {error}</span>
        </div>
      )}

      {!loading && !error && (
        <>
          <div className="flex flex-wrap justify-center gap-2 sm:gap-4 mb-6 sm:mb-8">
            <button
              key="all"
              className={`px-3 sm:px-4 py-2 rounded-full bg-white border ${
                selectedCategory === null
                  ? 'border-bandiwala-orange text-bandiwala-orange'
                  : 'border-gray-300 hover:border-bandiwala-orange hover:text-bandiwala-orange'
              } transition-colors font-medium text-xs sm:text-sm`}
              onClick={() => setSelectedCategory(null)}
            >
              All
            </button>
            {Object.keys(categories).map((category) => (
              <button
                key={category}
                className={`px-3 sm:px-4 py-2 rounded-full bg-white border ${
                  selectedCategory === category
                    ? 'border-bandiwala-orange text-bandiwala-orange'
                    : 'border-gray-300 hover:border-bandiwala-orange hover:text-bandiwala-orange'
                } transition-colors font-medium text-xs sm:text-sm`}
                onClick={() => setSelectedCategory(category)}
              >
                {category}
              </button>
            ))}
          </div>

          <div className="grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4 md:gap-6">
            {displayItems.map((item) => {
              const vendor = vendors[item.vendorId];
              const { isInCart, quantity } = getItemCartInfo(item._id);

              return (
                <Card key={item._id} className="overflow-hidden card-hover border-gray-300 cursor-pointer" onClick={() => handleItemClick(item)}>
                  <div className="relative h-32 sm:h-40 md:h-48">
                    <Image
                      src={getImageUrl(item.image)}
                      alt={item.itemName}
                      width={400}
                      height={200}
                      className="w-full h-full object-cover"
                    />
                    <div className="absolute top-1 sm:top-2 left-1 sm:left-2 bg-white px-2 sm:px-3 py-1 rounded-full text-xs font-bold text-bandiwala-red">
                      {item.itemCategory}
                    </div>
                  </div>
                  <CardContent className="p-2 sm:p-3 md:p-4">
                    <h3 className="font-semibold text-sm sm:text-base md:text-lg line-clamp-1">{item.itemName}</h3>
                    <p className="text-gray-500 text-xs sm:text-sm line-clamp-1">{vendor?.name || 'Unknown Vendor'}</p>

                    <div className="flex justify-between items-center mt-2 sm:mt-3 md:mt-4">
                      <div className="flex items-center">
                        <Star className="h-3 w-3 sm:h-4 sm:w-4 fill-yellow-400 text-yellow-400 mr-1" />
                        <span className="text-xs sm:text-sm font-medium">{vendor?.rating || 4.5}</span>
                        <span className="text-xs text-gray-500 ml-1 hidden sm:inline">(Reviews)</span>
                      </div>

                      <p className="font-semibold text-sm sm:text-base">₹{item.subcategories[0]?.price || 0}</p>
                    </div>

                    {isInCart ? (
                      <div className="mt-2 sm:mt-3 md:mt-4 flex items-center gap-1 sm:gap-2">
                        <div className="flex items-center bg-gray-100 border border-gray-200 rounded-full py-1 sm:py-1.5 px-2 sm:px-3">
                          <button
                            onClick={(e) => handleDecreaseQuantity(e, item._id)}
                            className="flex items-center justify-center w-4 h-4 sm:w-5 sm:h-5 hover:bg-gray-200 rounded-full transition-colors"
                          >
                            <Minus className="h-2 w-2 sm:h-3 sm:w-3 text-gray-600" />
                          </button>
                          <span className="font-semibold text-center min-w-[16px] sm:min-w-[20px] text-xs sm:text-sm text-gray-800">{quantity}</span>
                          <button
                            onClick={(e) => handleIncreaseQuantity(e, item, vendor)}
                            className="flex items-center justify-center w-4 h-4 sm:w-5 sm:h-5 hover:bg-gray-200 rounded-full transition-colors"
                          >
                            <Plus className="h-2 w-2 sm:h-3 sm:w-3 text-gray-600" />
                          </button>
                        </div>
                        <button
                          onClick={(e) => handleIncreaseQuantity(e, item, vendor)}
                          className="flex-1 bg-bandiwala-orange hover:bg-bandiwala-red text-white rounded-full py-1.5 sm:py-2 font-medium transition-colors flex items-center justify-center text-xs sm:text-sm"
                        >
                          <Plus className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
                          <span className="hidden sm:inline">Add to Cart</span>
                          <span className="sm:hidden">Add</span>
                        </button>
                      </div>
                    ) : (
                      <button
                        onClick={(e) => handleIncreaseQuantity(e, item, vendor)}
                        className="mt-2 sm:mt-3 md:mt-4 w-full bg-bandiwala-orange hover:bg-bandiwala-red text-white rounded-full py-1.5 sm:py-2 font-medium transition-colors flex items-center justify-center text-xs sm:text-sm"
                      >
                        <Plus className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
                        <span className="hidden sm:inline">Add to Cart</span>
                        <span className="sm:hidden">Add</span>
                      </button>
                    )}
                  </CardContent>
                </Card>
              );
            })}
          </div>

          <div className="text-center mt-6 sm:mt-8 md:mt-10">
            <button
              className="bg-white hover:bg-gray-50 text-bandiwala-red border border-bandiwala-red px-4 sm:px-6 py-2 rounded-full font-medium inline-flex items-center transition-colors text-sm sm:text-base"
              onClick={() => router.push('/dishes')}
            >
              Explore More Dishes
            </button>
          </div>
        </>
      )}
    </div>
  );
};

export default TrendingDishes;
