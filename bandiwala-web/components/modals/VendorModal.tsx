'use client';

import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Star, MapPin, Clock, Phone, ExternalLink } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { VendorLocation } from '@/data/vendorLocations';
import { getImageUrl } from '@/utils/imageUtils';

interface VendorModalProps {
  vendor: VendorLocation | null;
  isOpen: boolean;
  onClose: () => void;
}

export default function VendorModal({ vendor, isOpen, onClose }: VendorModalProps) {
  if (!vendor) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md mx-auto">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold text-gray-900">
            {vendor.name}
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          {/* Vendor Image */}
          <div className="relative w-full h-48 rounded-lg overflow-hidden">
            <Image
              src={getImageUrl(vendor.image)}
              alt={vendor.name}
              fill
              className="object-cover"
            />
          </div>

          {/* Rating */}
          <div className="flex items-center gap-2">
            <div className="bg-green-600 text-white px-3 py-1 rounded-lg flex items-center gap-1">
              <span className="font-medium">{vendor.rating.toFixed(1)}</span>
              <Star fill="white" size={14} className="text-white" />
            </div>
          </div>

          {/* Description */}
          <p className="text-gray-600 text-sm leading-relaxed">
            {vendor.description}
          </p>

          {/* Details */}
          <div className="space-y-3">
            <div className="flex items-center gap-3 text-sm text-gray-600">
              <Clock size={16} className="text-gray-400" />
              <span>Delivery: {vendor.deliveryTime}</span>
            </div>
            
            <div className="flex items-center gap-3 text-sm text-gray-600">
              <MapPin size={16} className="text-gray-400" />
              <span>Delivery Fee: ₹{vendor.deliveryFee}</span>
            </div>
            {vendor.minOrderValue > 0 && (
              <div className="flex items-center gap-3 text-sm text-gray-600">
                <span className="w-4 h-4 bg-gray-400 rounded-full flex items-center justify-center text-xs text-white">₹</span>
                <span>Min Order: ₹{vendor.minOrderValue}</span>
              </div>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3 pt-4">
            <Link href={`/vendors/${vendor.slug}`} className="flex-1">
              <Button className="w-full bg-orange-500 hover:bg-orange-600 text-white">
                <ExternalLink size={16} className="mr-2" />
                View Menu
              </Button>
            </Link>
            <Button 
              variant="outline" 
              onClick={onClose}
              className="px-6"
            >
              Close
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
