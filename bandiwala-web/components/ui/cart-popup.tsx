'use client';

import React, { useEffect } from 'react';
import { X } from 'lucide-react';

interface CartPopupProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  description: string;
  icon?: React.ReactNode;
}

export function CartPopup({ isOpen, onClose, title, description, icon }: CartPopupProps) {
  // Debug log
  useEffect(() => {
    console.log('CartPopup isOpen:', isOpen);
  }, [isOpen]);

  if (!isOpen) return null;

  return (
    <>
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black bg-opacity-20 z-50"
        onClick={onClose}
      />

      {/* Popup */}
      <div className="fixed top-4 left-1/2 transform -translate-x-1/2 z-50 w-full max-w-sm mx-4">
        <div className="bg-white rounded-lg shadow-lg border border-gray-200 p-4">
          {/* Close button */}
          <button
            onClick={onClose}
            className="absolute top-3 right-3 text-gray-400 hover:text-gray-600"
            aria-label="Close"
          >
            <X size={18} />
          </button>

          {/* Content */}
          <div className="pr-6">
            <div className="flex items-start gap-3">
              {icon && (
                <div className="flex-shrink-0 mt-0.5">
                  {icon}
                </div>
              )}
              <div className="flex-1">
                <h3 className="font-semibold text-gray-900 text-base mb-1">
                  {title}
                </h3>
                <p className="text-gray-600 text-sm">
                  {description}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
