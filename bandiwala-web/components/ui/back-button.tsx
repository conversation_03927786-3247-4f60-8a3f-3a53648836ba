'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeft } from 'lucide-react';
import { Button } from './button';
import { cn } from '@/lib/utils';

interface BackButtonProps {
  /** Custom text for the back button. Defaults to "Back" */
  text?: string;
  /** Custom URL to navigate to. If not provided, uses browser history */
  href?: string;
  /** Additional CSS classes */
  className?: string;
  /** Button variant */
  variant?: 'default' | 'ghost' | 'outline' | 'secondary';
  /** Button size */
  size?: 'default' | 'sm' | 'lg' | 'icon';
  /** Whether to show the arrow icon */
  showIcon?: boolean;
  /** Custom onClick handler. If provided, overrides default navigation */
  onClick?: () => void;
  /** Whether the button should be disabled */
  disabled?: boolean;
}

export function BackButton({
  text = 'Back',
  href,
  className,
  variant = 'ghost',
  size = 'default',
  showIcon = true,
  onClick,
  disabled = false,
}: BackButtonProps) {
  const router = useRouter();

  const handleClick = () => {
    if (onClick) {
      onClick();
      return;
    }

    if (href) {
      router.push(href);
    } else {
      // Check if there's history to go back to
      if (window.history.length > 1) {
        router.back();
      } else {
        // Fallback to home page if no history
        router.push('/');
      }
    }
  };

  return (
    <Button
      variant={variant}
      size={size}
      onClick={handleClick}
      disabled={disabled}
      className={cn(
        'flex items-center gap-2 transition-colors',
        className
      )}
    >
      {showIcon && <ArrowLeft className="h-4 w-4" />}
      {size !== 'icon' && text}
    </Button>
  );
}

// Compact version for headers and tight spaces
export function BackButtonCompact({
  className,
  href,
  onClick,
  disabled = false,
}: Pick<BackButtonProps, 'className' | 'href' | 'onClick' | 'disabled'>) {
  return (
    <BackButton
      variant="ghost"
      size="icon"
      showIcon={true}
      className={cn('h-8 w-8', className)}
      href={href}
      onClick={onClick}
      disabled={disabled}
    />
  );
}

export default BackButton;
