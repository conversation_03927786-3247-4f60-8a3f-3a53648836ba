'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { getImageUrl, getSafeFallbackImage } from '@/utils/imageUtils';

interface SafeImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  className?: string;
  fill?: boolean;
  priority?: boolean;
  fallbackType?: 'food' | 'vendor';
  onLoad?: () => void;
  onError?: () => void;
}

/**
 * A safe image component that gracefully handles loading errors
 * and provides immediate fallbacks without causing Next.js optimization timeouts
 */
export default function SafeImage({
  src,
  alt,
  width,
  height,
  className,
  fill = false,
  priority = false,
  fallbackType = 'food',
  onLoad,
  onError
}: SafeImageProps) {
  const [imageSrc, setImageSrc] = useState<string>('');
  const [hasError, setHasError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Reset state when src changes
    setHasError(false);
    setIsLoading(true);

    // Define problematic patterns that we know will fail
    const problematicPatterns = [
      'Jai%20Bhavani%20Chat%20',  // URL encoded with trailing space
      'Jai Bhavani Chat ',        // With trailing space
      'bandiwala-items-pics/items/Jai%20Bhavani%20Chat%20',
      'bandiwala-items-pics/items/Jai Bhavani Chat ',
      'rajamandri.jpeg',          // Known problematic vendor image
      '/bandiwala-items-pics/vendors/rajamandri.jpeg'
    ];

    // If src is empty or invalid, use fallback immediately
    if (!src || src === 'undefined' || src === 'null') {
      setImageSrc(getSafeFallbackImage(fallbackType));
      setIsLoading(false);
      return;
    }

    // Check for problematic patterns in the original src
    const isProblematic = problematicPatterns.some(pattern =>
      src.includes(pattern)
    );

    if (isProblematic) {
      console.log('Detected problematic image pattern in src, using fallback immediately:', src);
      setImageSrc(getSafeFallbackImage(fallbackType));
      setIsLoading(false);
      return;
    }

    // If it's already a local image, use it directly
    if (src.startsWith('/images/')) {
      setImageSrc(src);
      setIsLoading(false);
      return;
    }

    // For remote images, try to use the processed URL
    try {
      const processedUrl = getImageUrl(src);

      // If the processed URL is local, use it
      if (processedUrl.startsWith('/images/')) {
        setImageSrc(processedUrl);
        setIsLoading(false);
        return;
      }

      // Check if the processed URL is also problematic
      const processedIsProblematic = problematicPatterns.some(pattern =>
        processedUrl.includes(pattern)
      );

      if (processedIsProblematic) {
        console.log('Detected problematic image URL in processed URL, using fallback immediately:', processedUrl);
        setImageSrc(getSafeFallbackImage(fallbackType));
        setIsLoading(false);
        return;
      }

      // For other remote URLs, set them but be ready to fallback on error
      setImageSrc(processedUrl);
      setIsLoading(false);
    } catch (error) {
      console.log('Error processing image URL:', error);
      setImageSrc(getSafeFallbackImage(fallbackType));
      setIsLoading(false);
    }
  }, [src, fallbackType]);

  const handleError = () => {
    if (!hasError) {
      console.log('Image failed to load, using fallback:', imageSrc);
      setHasError(true);
      setImageSrc(getSafeFallbackImage(fallbackType));
      onError?.();
    }
  };

  const handleLoad = () => {
    setIsLoading(false);
    onLoad?.();
  };

  // Show loading placeholder while determining the image source
  if (isLoading) {
    return (
      <div 
        className={`bg-gray-200 animate-pulse flex items-center justify-center ${className}`}
        style={fill ? {} : { width, height }}
      >
        <div className="text-gray-400 text-xs">Loading...</div>
      </div>
    );
  }

  // Common props for the Image component
  const imageProps = {
    src: imageSrc,
    alt,
    className,
    onError: handleError,
    onLoad: handleLoad,
    priority,
    // Add unoptimized for remote images that might fail or timeout
    unoptimized: !imageSrc.startsWith('/images/'),
    // Add loading strategy for better performance
    loading: priority ? 'eager' : 'lazy' as 'eager' | 'lazy',
  };

  if (fill) {
    return (
      <Image
        {...imageProps}
        fill
      />
    );
  }

  return (
    <Image
      {...imageProps}
      width={width || 400}
      height={height || 300}
    />
  );
}
