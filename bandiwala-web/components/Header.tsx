'use client';

import { useState, useEffect, useRef } from "react";
import Link from "next/link";
import { usePathname } from 'next/navigation';
import { Menu, User } from 'lucide-react';
import { Button } from "./ui/button";
import { cn } from "@/lib/utils";
import SearchBar from "./SearchBar";
import CartCounter from './cart/CartCounter';
import { useAuth } from "@/contexts/AuthContext";
import { toast } from 'sonner';
import Image from 'next/image';
import { getImageUrl } from '@/utils/imageUtils';
import DisclaimerBar from './DisclaimerBar';
import { BackButtonCompact } from './ui/back-button';
import {
  AlertDialog,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

interface HeaderProps {
  /** Whether to show a back button */
  showBackButton?: boolean;
  /** Custom back button href */
  backButtonHref?: string;
  /** Custom back button click handler */
  onBackClick?: () => void;
  /** Hide search bar */
  hideSearchBar?: boolean;
}

export default function Header({
  showBackButton = false,
  backButtonHref,
  onBackClick,
  hideSearchBar = false
}: HeaderProps = {}) {
  const pathname = usePathname();
  const { isAuthenticated, logout, user, token } = useAuth();
  const [isOpen, setIsOpen] = useState(false);
  const [isLoggingOut, setIsLoggingOut] = useState(false);
  const [showLogoutDialog, setShowLogoutDialog] = useState(false);

  const [forceUpdate, setForceUpdate] = useState(0);

  useEffect(() => {
    console.log('Header: Authentication state:', {
      isAuthenticated,
      hasUser: !!user,
      hasToken: !!token,
      forceUpdateCounter: forceUpdate
    });
  }, [isAuthenticated, user, token, forceUpdate]);

  useEffect(() => {
    if (typeof window === 'undefined') return;
    const checkAuthState = () => {
      const storedToken = localStorage.getItem('auth_token');
      if (!!storedToken !== !!token) {
        console.log('Header: Detected auth state change, forcing update');
        setForceUpdate(prev => prev + 1);
      }
    };

    // Log the current forceUpdate value
    console.log('Header: Current forceUpdate value:', forceUpdate);

    // Check auth state periodically
    const interval = setInterval(checkAuthState, 1000);
    return () => clearInterval(interval);
  }, [token, forceUpdate]);

  // Reference to the mobile menu
  const mobileMenuRef = useRef<HTMLDivElement>(null);

  // Handle logout with toast message
  const handleLogout = async (event?: React.MouseEvent) => {
    // Prevent multiple logout attempts
    if (isLoggingOut) {
      console.log('Header: Logout already in progress, ignoring');
      return;
    }

    try {
      // Prevent any event bubbling that might interfere
      if (event) {
        event.preventDefault();
        event.stopPropagation();
      }

      setIsLoggingOut(true);
      console.log('Header: Starting logout process');

      await logout();
      console.log('Header: Logout successful');
      toast.success('Logged out successfully');

      // Close dialogs and mobile menu
      setShowLogoutDialog(false);
      setIsOpen(false);
    } catch (error) {
      console.error('Header: Error logging out:', error);
      toast.error('Failed to logout. Please try again.');
    } finally {
      setIsLoggingOut(false);
    }
  };

  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      // If menu is open and click is outside the menu and outside the menu button
      if (isOpen && mobileMenuRef.current && !mobileMenuRef.current.contains(e.target as Node)) {
        // Check if the click was on the menu button (which has its own handler)
        const target = e.target as HTMLElement;
        if (target.closest('button') && target.closest('button')?.classList.contains('md:hidden')) {
          return;
        }
        setIsOpen(false);
      }
    };

    const handleEscKey = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        setIsOpen(false);
      }
    };

    // Add event listeners if menu is open
    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('keydown', handleEscKey);
    }

    // Clean up
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleEscKey);
    };
  }, [isOpen]);

  // NavLinks component with different rendering for mobile vs desktop
  const NavLinks = ({ isMobile = false }: { isMobile?: boolean }) => {
    // Common link styles
    const linkBaseStyles = "text-sm font-medium transition-colors hover:text-bandiwala-orange";

    // Common active link determination
    const isDishesActive = pathname === "/dishes";
    const isVendorsActive = pathname === "/vendors";
    const isCartActive = pathname === "/cart";
    const isOrdersActive = pathname.startsWith("/orders");

    if (isMobile) {
      // Mobile-specific rendering with full-width clickable areas
      return (
        <div className="flex flex-col w-full">
          <Link
            href="/dishes"
            className={cn(
              "block w-full py-3 px-2 rounded-md",
              linkBaseStyles,
              isDishesActive ? "text-bandiwala-orange bg-orange-50" : "text-gray-600"
            )}
            onClick={() => setIsOpen(false)}
          >
            Dishes
          </Link>
          <Link
            href="/vendors"
            className={cn(
              "block w-full py-3 px-2 rounded-md",
              linkBaseStyles,
              isVendorsActive ? "text-bandiwala-orange bg-orange-50" : "text-gray-600"
            )}
            onClick={() => setIsOpen(false)}
          >
            Vendors
          </Link>
          <Link
            href="/cart"
            className={cn(
              "block w-full py-3 px-2 rounded-md flex items-center gap-2",
              linkBaseStyles,
              isCartActive ? "text-bandiwala-orange bg-orange-50" : "text-gray-600"
            )}
            onClick={() => setIsOpen(false)}
          >
            <CartCounter />
            Cart
          </Link>
          {isAuthenticated && (
            <Link
              href="/orders"
              className={cn(
                "block w-full py-3 px-2 rounded-md",
                linkBaseStyles,
                isOrdersActive ? "text-bandiwala-orange bg-orange-50" : "text-gray-600"
              )}
              onClick={() => setIsOpen(false)}
            >
              Orders
            </Link>
          )}
        </div>
      );
    }

    // Desktop rendering
    return (
      <>
        <Link
          href="/dishes"
          className={cn(linkBaseStyles,
            isDishesActive ? "text-bandiwala-orange" : "text-gray-600")}
          onClick={() => setIsOpen(false)}
        >
          Dishes
        </Link>
        <Link
          href="/vendors"
          className={cn(linkBaseStyles,
            isVendorsActive ? "text-bandiwala-orange" : "text-gray-600")}
          onClick={() => setIsOpen(false)}
        >
          Vendors
        </Link>
        <Link
          href="/cart"
          className={cn(linkBaseStyles, "flex items-center gap-2",
            isCartActive ? "text-bandiwala-orange" : "text-gray-600")}
          onClick={() => setIsOpen(false)}
        >
          <CartCounter />
          Cart
        </Link>
        {isAuthenticated && (
          <Link
            href="/orders"
            className={cn(linkBaseStyles,
              isOrdersActive ? "text-bandiwala-orange" : "text-gray-600")}
            onClick={() => setIsOpen(false)}
          >
            Orders
          </Link>
        )}
      </>
    );
  };

  // AuthSection component with different rendering for mobile vs desktop
  const AuthSection = ({ isMobile = false }: { isMobile?: boolean }) => {
    const [localAuthState, setLocalAuthState] = useState(false);
    const [directTokenCheck, setDirectTokenCheck] = useState(false);

    // Update local auth state whenever the component renders
    // This ensures we always have the latest state
    useEffect(() => {
      // Only access localStorage on the client side
      if (typeof window !== 'undefined') {
        const hasToken = !!localStorage.getItem('auth_token');
        console.log('AuthSection: Checking local auth state, hasToken:', hasToken);
        setLocalAuthState(hasToken);
        setDirectTokenCheck(hasToken);

        // Set up an interval to check for token changes
        const interval = setInterval(() => {
          const currentHasToken = !!localStorage.getItem('auth_token');
          if (currentHasToken !== localAuthState) {
            console.log('AuthSection: Token state changed, updating local state');
            setLocalAuthState(currentHasToken);
          }
          setDirectTokenCheck(currentHasToken);
        }, 1000);

        return () => clearInterval(interval);
      }
    }, [localAuthState]);

    // Use both the context auth state and local auth state
    // This provides a fallback in case the context hasn't updated yet
    const effectiveAuthState = isAuthenticated || localAuthState || directTokenCheck;

    // Log the authentication state sources
    console.log('AuthSection: Auth state sources:', {
      isAuthenticated,
      localAuthState,
      directTokenCheck,
      effectiveAuthState
    });

    if (isMobile) {
      // Mobile-specific rendering
      return (
        <>
          {effectiveAuthState ? (
            <div className="flex flex-col w-full space-y-2">
              <Link
                href="/profile"
                className="flex items-center gap-2 py-3 px-2 rounded-md text-gray-600 hover:text-bandiwala-orange hover:bg-orange-50"
                onClick={() => setIsOpen(false)}
              >
                <div className="h-8 w-8 rounded-full bg-bandiwala-orange text-white flex items-center justify-center overflow-hidden">
                  {user?.profileImage ? (
                    <Image
                      src={getImageUrl(user.profileImage)}
                      alt="Profile"
                      width={32}
                      height={32}
                      className="object-cover w-full h-full"
                    />
                  ) : (
                    <User className="h-4 w-4" />
                  )}
                </div>
                <span className="text-sm font-medium">Profile</span>
              </Link>
              <AlertDialog open={showLogoutDialog} onOpenChange={setShowLogoutDialog}>
                <AlertDialogTrigger asChild>
                  <button
                    className="flex w-full items-center py-3 px-2 rounded-md text-sm font-medium text-gray-600 hover:text-bandiwala-orange hover:bg-orange-50 text-left"
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      console.log('Mobile logout trigger clicked');
                      setShowLogoutDialog(true);
                    }}
                  >
                    Logout
                  </button>
                </AlertDialogTrigger>
                <AlertDialogContent className="z-[9999] max-w-[90vw] sm:max-w-lg">
                  <AlertDialogHeader>
                    <AlertDialogTitle>Confirm Logout</AlertDialogTitle>
                    <AlertDialogDescription>
                      Are you sure you want to logout? You will need to login again to access your account.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter className="flex-col-reverse sm:flex-row gap-2 sm:gap-0">
                    <AlertDialogCancel
                      className="w-full sm:w-auto"
                      onClick={() => setShowLogoutDialog(false)}
                    >
                      Cancel
                    </AlertDialogCancel>
                    <Button
                      onClick={(event) => {
                        console.log('Mobile logout button clicked');
                        handleLogout(event);
                      }}
                      disabled={isLoggingOut}
                      className="bg-red-500 hover:bg-red-600 text-white w-full sm:w-auto disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {isLoggingOut ? 'Logging out...' : 'Logout'}
                    </Button>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </div>
          ) : (
            <div className="w-full">
              <Link
                href="/login"
                className="block w-full"
                onClick={() => setIsOpen(false)}
              >
                <Button variant="outline" className="text-bandiwala-orange border-bandiwala-orange hover:bg-bandiwala-orange hover:text-white w-full">
                  Login
                </Button>
              </Link>
            </div>
          )}
        </>
      );
    }

    // Desktop rendering
    return (
      <>
        {effectiveAuthState ? (
          <div className="flex items-center gap-4">
            <Link
              href="/profile"
              className="flex items-center gap-2"
              onClick={() => setIsOpen(false)}
            >
              <div className="h-8 w-8 rounded-full bg-bandiwala-orange text-white flex items-center justify-center overflow-hidden">
                {user?.profileImage ? (
                  <Image
                    src={getImageUrl(user.profileImage)}
                    alt="Profile"
                    width={32}
                    height={32}
                    className="object-cover w-full h-full"
                  />
                ) : (
                  <User className="h-4 w-4" />
                )}
              </div>
            </Link>
            <AlertDialog open={showLogoutDialog} onOpenChange={setShowLogoutDialog}>
              <AlertDialogTrigger asChild>
                <Button
                  variant="ghost"
                  className="text-sm font-medium text-gray-600 hover:text-bandiwala-orange"
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('Desktop logout trigger clicked');
                    setShowLogoutDialog(true);
                  }}
                >
                  Logout
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent className="z-[9999] max-w-[90vw] sm:max-w-lg">
                <AlertDialogHeader>
                  <AlertDialogTitle>Confirm Logout</AlertDialogTitle>
                  <AlertDialogDescription>
                    Are you sure you want to logout? You will need to login again to access your account.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter className="flex-col-reverse sm:flex-row gap-2 sm:gap-0">
                  <AlertDialogCancel
                    className="w-full sm:w-auto"
                    onClick={() => setShowLogoutDialog(false)}
                  >
                    Cancel
                  </AlertDialogCancel>
                  <Button
                    onClick={(event) => {
                      console.log('Desktop logout button clicked');
                      handleLogout(event);
                    }}
                    disabled={isLoggingOut}
                    className="bg-red-500 hover:bg-red-600 text-white w-full sm:w-auto disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isLoggingOut ? 'Logging out...' : 'Logout'}
                  </Button>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        ) : (
          <div className="flex items-center gap-2">
            <Link
              href="/login"
              onClick={() => setIsOpen(false)}
            >
              <Button variant="outline" className="text-bandiwala-orange border-bandiwala-orange hover:bg-bandiwala-orange hover:text-white">
                Login
              </Button>
            </Link>
          </div>
        )}
      </>
    );
  };

  return (
    <div className="sticky top-0 z-50 w-full">
      {/* Main Header */}
      <div className="backdrop-blur bg-white/80 border-b border-slate-200">
        <div className="max-w-[1440px] mx-auto px-4 md:px-6 w-full">
          <div className="flex h-16 items-center gap-4 md:gap-6">
            {/* Back Button */}
            {showBackButton && (
              <BackButtonCompact
                href={backButtonHref}
                onClick={onBackClick}
                className="flex-shrink-0"
              />
            )}

            <Link
              href="/"
              className="text-2xl font-bold tracking-tighter text-bandiwala-orange hover:text-bandiwala-red whitespace-nowrap"
            >
              <Image src="/images/logo.png" alt="Bandiwala Logo" width={50} height={50} />
            </Link>

            {/* Search Bar */}
            {!hideSearchBar && (
              <div className="flex-1 max-w-[400px]">
                <SearchBar />
              </div>
            )}

            {/* Spacer when search bar is hidden */}
            {hideSearchBar && <div className="flex-1" />}

            {/* Desktop Navigation Links */}
            <nav className="hidden md:flex items-center gap-6 ml-auto">
              <NavLinks />
              <AuthSection />
            </nav>

            {/* Mobile Cart Icon */}
            <Link
              href="/cart"
              className="md:hidden flex items-center justify-center p-2 text-gray-600 hover:text-bandiwala-orange"
            >
              <CartCounter showAlways={true} />
            </Link>

            {/* Mobile Menu Button */}
            <Button
              variant="ghost"
              size="icon"
              className="md:hidden"
              onClick={() => setIsOpen(!isOpen)}
            >
              <Menu className="h-6 w-6" />
            </Button>
          </div>

          {/* Mobile Menu Dropdown */}
          {isOpen && (
            <div
              ref={mobileMenuRef}
              className="md:hidden border-t border-gray-200 animate-in fade-in slide-in-from-top-5 duration-300"
            >
              <div className="py-4 px-4 flex flex-col gap-6">
                <div className="flex flex-col">
                  <NavLinks isMobile={true} />
                </div>
                <div className="pt-4 border-t border-gray-200">
                  <AuthSection isMobile={true} />
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Disclaimer Bar */}
      <DisclaimerBar />
    </div>
  );
}
