'use client';

import { useState, useEffect, useRef, KeyboardEvent } from 'react';
import { Search, X, Clock } from 'lucide-react';
import { useRouter, usePathname } from 'next/navigation';
import { Input } from "./ui/input";
import { useSearch } from '@/context/SearchContext';
// import { useDebounce } from '@/hooks/use-debounce';
import { useSearchHistory } from '@/hooks/use-search-history';
import { cn } from '@/lib/utils';
import { menuItemService, vendorService } from '@/services/api';

interface MenuItem {
  _id: string;
  itemName: string;
  description: string;
  itemCategory: string;
  subcategories: Array<{
    title: string;
    quantity: string;
    price: number;
  }>;
  image: string;
  vendorId: string;
  isAvailable: boolean;
}

interface Vendor {
  _id: string;
  name: string;
  description: string;
  slug: string;
  rating: number;
  location: string;
  image: string;
  deliveryTime: string;
  deliveryFee: number;
  minOrderValue: number;
  isActive: boolean;
}

interface SearchSuggestion {
  type: 'dish' | 'category' | 'recent' | 'vendor' | 'clear-history';
  label: string;
  value: string;
  slug?: string; // For vendor navigation
}

export default function SearchBar() {
  const router = useRouter();
  const pathname = usePathname();
  const { setSearchQuery } = useSearch();
  const [query, setQuery] = useState('');
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const [menuItems, setMenuItems] = useState<MenuItem[]>([]);
  const [vendors, setVendors] = useState<Vendor[]>([]);
  const [categories, setCategories] = useState<string[]>([]);
  const searchRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  // const debouncedQuery = useDebounce(query, 300); // Uncomment if needed for future use
  const { recentSearches, addToHistory, clearHistory } = useSearchHistory();

  console.log('SearchBar component rendered - vendors:', vendors.length, 'menuItems:', menuItems.length);

  // Fetch menu items and vendors on component mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch menu items
        const menuResponse = await menuItemService.getAllMenuItems();
        if (menuResponse && menuResponse.success && menuResponse.data) {
          const items = menuResponse.data as MenuItem[];
          setMenuItems(items);

          // Extract unique categories
          const uniqueCategories = [...new Set(items.map((item: MenuItem) => item.itemCategory))];
          setCategories(uniqueCategories.sort());
        }

        // Fetch vendors
        const vendorResponse = await vendorService.getAllVendors();
        if (vendorResponse && vendorResponse.success && vendorResponse.data) {
          const vendorItems = vendorResponse.data as Vendor[];
          setVendors(vendorItems);
        }
      } catch (error) {
        console.error('SearchBar: Error fetching data for search:', error);
      }
    };

    fetchData();
  }, []);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setShowSuggestions(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const getSuggestions = (input: string): SearchSuggestion[] => {
    const suggestions: SearchSuggestion[] = [];
    const lowercaseInput = input.toLowerCase();

    if (!input) {
      // When no input, show recent searches first
      recentSearches.forEach(search => {
        suggestions.push({
          type: 'recent',
          label: search,
          value: search
        });
      });

      // Add clear history option right after recent searches if there are any
      if (recentSearches.length > 0) {
        suggestions.push({
          type: 'clear-history',
          label: 'Clear all recent searches',
          value: 'clear-history'
        });
      }

      // Add some popular vendors (limit to 2 when no input)
      const popularVendors = vendors
        .slice(0, 2)
        .map(vendor => ({
          type: 'vendor' as const,
          label: vendor.name,
          value: vendor.name,
          slug: vendor.slug
        }));
      suggestions.push(...popularVendors);

      // Add some popular dishes (limit to 2 when no input to make room for clear option)
      const popularDishes = menuItems
        .slice(0, 2)
        .map(item => ({
          type: 'dish' as const,
          label: item.itemName,
          value: item.itemName
        }));
      suggestions.push(...popularDishes);

      // Add categories when no input (limit to 2 to make room for clear option)
      categories.slice(0, 2).forEach(category => {
        suggestions.push({
          type: 'category',
          label: category,
          value: category
        });
      });
    } else {
      const vendorSuggestions = vendors
        .filter(vendor =>
          vendor.name.toLowerCase().includes(lowercaseInput) ||
          vendor.description.toLowerCase().includes(lowercaseInput) ||
          vendor.location.toLowerCase().includes(lowercaseInput)
        )
        .slice(0, 3)
        .map(vendor => ({
          type: 'vendor' as const,
          label: vendor.name,
          value: vendor.name,
          slug: vendor.slug
        }));

      suggestions.push(...vendorSuggestions);

      // Add dish name suggestions (limit to 4 for better UX)
      const dishSuggestions = menuItems
        .filter(item =>
          item.itemName.toLowerCase().includes(lowercaseInput) ||
          item.description.toLowerCase().includes(lowercaseInput)
        )
        .slice(0, 4)
        .map(item => ({
          type: 'dish' as const,
          label: item.itemName,
          value: item.itemName
        }));

      suggestions.push(...dishSuggestions);

      // Add category suggestions
      categories.forEach(category => {
        if (category.toLowerCase().includes(lowercaseInput)) {
          suggestions.push({
            type: 'category',
            label: category,
            value: category
          });
        }
      });
    }

    // Limit total suggestions to 8 for better UX, but ensure clear-history is always included
    if (suggestions.length <= 8) {
      return suggestions;
    }

    // If we have more than 8 suggestions, check if clear-history is present
    const clearHistoryIndex = suggestions.findIndex(s => s.type === 'clear-history');
    if (clearHistoryIndex !== -1 && clearHistoryIndex >= 8) {
      // If clear-history is beyond the 8-item limit, include it by removing one other item
      const limitedSuggestions = suggestions.slice(0, 7);
      limitedSuggestions.push(suggestions[clearHistoryIndex]);
      return limitedSuggestions;
    }

    return suggestions.slice(0, 8);
  };

  const handleKeyDown = (e: KeyboardEvent) => {
    const suggestions = getSuggestions(query);

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex(prev =>
          prev < suggestions.length - 1 ? prev + 1 : prev
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev => prev > 0 ? prev - 1 : -1);
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedIndex >= 0 && suggestions[selectedIndex]) {
          handleSelection(suggestions[selectedIndex]);
        } else if (query) {
          handleSearch(query);
        }
        break;
      case 'Escape':
        setShowSuggestions(false);
        setSelectedIndex(-1);
        break;
    }
  };

  const handleSelection = (suggestion: SearchSuggestion) => {
    // Handle clear history action
    if (suggestion.type === 'clear-history') {
      clearHistory();
      setShowSuggestions(false);
      setSelectedIndex(-1);
      return;
    }

    setQuery(suggestion.value);
    addToHistory(suggestion.value);
    setShowSuggestions(false);
    setSelectedIndex(-1);

    // Handle vendor selection differently
    if (suggestion.type === 'vendor' && suggestion.slug) {
      router.push(`/vendors/${suggestion.slug}`);
    } else {
      // For dishes, categories, and recent searches, go to dishes page with search
      setSearchQuery(suggestion.value);
      if (pathname !== '/dishes') {
        router.push('/dishes');
      }
    }
  };

  const handleSearch = (searchQuery: string) => {
    addToHistory(searchQuery);
    setSearchQuery(searchQuery);
    if (pathname !== '/dishes') {
      router.push('/dishes');
    }
  };

  const highlightMatch = (text: string, query: string) => {
    if (!query) return text;
    const parts = text.split(new RegExp(`(${query})`, 'gi'));
    return parts.map((part, i) =>
      part.toLowerCase() === query.toLowerCase() ?
        <span key={i} className="bg-yellow-100 font-medium">{part}</span> : part
    );
  };

  const suggestions = getSuggestions(query);

  return (
    <div className="relative w-full max-w-[300px] md:max-w-[400px]" ref={searchRef}>
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
        <Input
          ref={inputRef}
          type="text"
          placeholder="Search vendors, dishes, categories..."
          value={query}
          onChange={(e) => {
            setQuery(e.target.value);
            setShowSuggestions(true);
            setSelectedIndex(-1);
          }}
          onFocus={() => setShowSuggestions(true)}
          onKeyDown={handleKeyDown}
          className="w-full pl-10 pr-10 rounded-full bg-gray-50 border-gray-200 focus:border-bandiwala-orange"
        />
        {query && (
          <button
            onClick={() => {
              setQuery('');
              setSearchQuery('');
              inputRef.current?.focus();
            }}
            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
          >
            <X size={18} />
          </button>
        )}
      </div>

      {showSuggestions && (suggestions.length > 0 || recentSearches.length > 0) && (
        <div className="absolute w-full mt-1 bg-white rounded-md shadow-lg border border-gray-200 max-h-[300px] overflow-auto z-50">
          {suggestions.length > 0 ? (
            suggestions.map((suggestion, index) => (
              <button
                key={index}
                className={cn(
                  "w-full px-4 py-2 text-left hover:bg-gray-50 flex items-center",
                  suggestion.type === 'clear-history' ? "justify-between" : "gap-2",
                  selectedIndex === index && "bg-gray-50"
                )}
                onClick={() => handleSelection(suggestion)}
                onMouseEnter={() => setSelectedIndex(index)}
              >
                {suggestion.type === 'clear-history' ? (
                  <>
                    <span className="text-red-500 text-sm">
                      {highlightMatch(suggestion.label, query)}
                    </span>
                    <X size={16} className="text-red-400" />
                  </>
                ) : (
                  <>
                    {suggestion.type === 'recent' ? (
                      <Clock size={16} className="text-gray-400" />
                    ) : suggestion.type === 'vendor' ? (
                      <span className="text-xs bg-green-100 text-green-600 px-2 py-1 rounded">Vendor</span>
                    ) : suggestion.type === 'dish' ? (
                      <span className="text-xs bg-orange-100 text-orange-600 px-2 py-1 rounded">Dish</span>
                    ) : (
                      <span className="text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded">Category</span>
                    )}
                    <span>
                      {highlightMatch(suggestion.label, query)}
                    </span>
                  </>
                )}
              </button>
            ))
          ) : recentSearches.length > 0 && (
            <div>
              <div className="flex items-center justify-between px-4 py-2 border-b">
                <span className="text-sm text-gray-500">Recent Searches</span>
                <button
                  onClick={clearHistory}
                  className="text-xs text-gray-400 hover:text-gray-600"
                >
                  Clear
                </button>
              </div>
              {recentSearches.map((search, index) => (
                <button
                  key={index}
                  className="w-full px-4 py-2 text-left hover:bg-gray-50 flex items-center gap-2"
                  onClick={() => handleSelection({
                    type: 'recent',
                    label: search,
                    value: search
                  })}
                >
                  <Clock size={16} className="text-gray-400" />
                  {search}
                </button>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
}