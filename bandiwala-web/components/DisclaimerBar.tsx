'use client';

import React, { useState, useEffect } from 'react';
import { usePathname } from 'next/navigation';
import { AlertTriangle } from 'lucide-react';

const DisclaimerBar: React.FC = () => {
  const pathname = usePathname();
  const isCartPage = pathname === '/cart';
  const [isServiceHours, setIsServiceHours] = useState(false);
  const [isClient, setIsClient] = useState(false);

  // Function to check if current time is within service hours (5 PM - 9 PM IST)
  const checkServiceHours = () => {
    try {
      const now = new Date();

      // Get current time in IST (UTC+5:30) using Intl.DateTimeFormat for better reliability
      const istTime = new Intl.DateTimeFormat('en-US', {
        timeZone: 'Asia/Kolkata',
        hour: 'numeric',
        hour12: false
      }).formatToParts(now);

      const hourPart = istTime.find(part => part.type === 'hour');
      const currentHour = hourPart ? parseInt(hourPart.value, 10) : 0;

      // Service hours: 17:00 (5 PM) to 21:00 (9 PM) IST
      const isWithinHours = currentHour >= 17 && currentHour < 21;

      // Debug logging in development
      if (process.env.NODE_ENV === 'development') {
        console.log('DisclaimerBar - Current IST Hour:', currentHour, 'Within Service Hours:', isWithinHours);
      }

      return isWithinHours;
    } catch (error) {
      console.error('Error checking service hours:', error);
      // Fallback: show banner if there's an error
      return true;
    }
  };

  useEffect(() => {
    // Set client flag to true after component mounts (hydration)
    setIsClient(true);

    // Initial check
    setIsServiceHours(checkServiceHours());

    // Set up interval to check every minute
    const interval = setInterval(() => {
      setIsServiceHours(checkServiceHours());
    }, 60000); // Check every 60 seconds

    // Cleanup interval on unmount
    return () => clearInterval(interval);
  }, []);

  // Don't render anything on server-side to avoid hydration mismatch
  if (!isClient) {
    return null;
  }

  if (isCartPage) {
    // Cart page disclaimer - only show when OUTSIDE service hours
    if (isServiceHours) {
      return null; // No disclaimer during service hours (5-9 PM IST)
    }

    // Cart page disclaimer - item availability and app launch (only when service unavailable)
    return (
      <div className="w-full bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-blue-200">
        <div className="max-w-[1440px] mx-auto px-4 md:px-6 py-3">
          <div className="flex items-start justify-center gap-3 text-center">
            <AlertTriangle className="h-4 w-4 text-blue-600 flex-shrink-0 mt-0.5" />
            <p className="text-xs md:text-sm font-medium text-blue-800 leading-relaxed">
              <span className="font-semibold">📝 Note before ordering:</span> The items added to your cart are subject to availability.
              In case of item unavailability, our representative will contact you shortly after you place your order.
              Thank you for your understanding. We're working on launching our app soon to provide you with a better experience.
            </p>
          </div>
        </div>
      </div>
    );
  }

  // Only show service hours disclaimer when OUTSIDE service hours (before 5 PM or after 9 PM IST)
  if (isServiceHours) {
    return null;
  }

  // Service hours disclaimer - only visible when service is UNAVAILABLE (outside 5 PM - 9 PM IST)
  return (
    <div className="w-full bg-gradient-to-r from-yellow-50 to-orange-50 border-b border-yellow-200 transition-all duration-500 ease-in-out">
      <div className="max-w-[1440px] mx-auto px-4 md:px-6 py-3">
        <div className="flex items-center justify-center gap-2 text-center">
          <AlertTriangle className="h-4 w-4 text-yellow-600 flex-shrink-0" />
          <p className="text-sm md:text-base font-bold text-yellow-800">
            ⚠️ Note: Our service is available only between 5:00 PM and 9:00 PM. Please place your orders during this time. ⚠️
          </p>
        </div>
      </div>
    </div>
  );
};

export default DisclaimerBar;
