import React from 'react';
import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { CalendarClock } from 'lucide-react';

const ComingSoon = () => {
  return (
    <div className="flex flex-col items-center justify-center min-h-[80vh] bg-gradient-to-b from-amber-50 to-white px-4 py-16">
      <div className="text-center max-w-3xl mx-auto">
        <CalendarClock className="w-16 h-16 text-bandiwala-orange mx-auto mb-6" />

        <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
          Coming Soon!
        </h1>

        <p className="text-xl text-gray-600 mb-8">
          We&apos;re working hard to bring you an amazing experience. This feature will be available soon.
        </p>

        <p className="text-gray-500 mb-10">
          Thank you for your patience as we continue to improve Bandiwala.
        </p>

        <Link href="/">
          <Button className="bg-bandiwala-orange hover:bg-bandiwala-red text-white px-8 py-2 rounded-full">
            Back to Home
          </Button>
        </Link>
      </div>
    </div>
  );
};

export default ComingSoon;
