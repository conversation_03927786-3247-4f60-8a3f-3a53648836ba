'use client';

import { useEffect, useState } from 'react';
import FoodCard from '../FoodCard';
import { menuItemService, vendorService } from '@/services/api';

interface CatalogProps {
  searchQuery: string;
  categoryFilter: string;
}

interface Subcategory {
  title: string;
  quantity: string;
  price: number;
}

interface MenuItem {
  _id: string;
  itemName: string;
  slug: string;
  description: string;
  subcategories: Subcategory[];
  image: string;
  vendorId: string;
  itemCategory: string;
  isAvailable: boolean;
}

interface Vendor {
  _id: string;
  name: string;
  description: string;
  slug: string;
  rating: number;
  location: string;
  image: string;
  deliveryTime: string;
  deliveryFee: number;
  minOrderValue: number;
  isActive: boolean;
}

interface FormattedDish {
  id: string;
  name: string;
  description: string;
  price: number;
  image: string;
  vendor: string;
  vendorId: string;
  rating: number;
  reviewCount: number;
  category: string;
}

export default function Catalog({ searchQuery, categoryFilter }: CatalogProps) {
  const [menuItems, setMenuItems] = useState<MenuItem[]>([]);
  const [vendors, setVendors] = useState<Record<string, Vendor>>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [formattedDishes, setFormattedDishes] = useState<FormattedDish[]>([]);

  // Fetch data from backend
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // Fetch all menu items
        const menuItemsResponse = await menuItemService.getAllMenuItems();

        // Fetch all vendors
        const vendorsResponse = await vendorService.getAllVendors();

        // Check if we got valid data from both endpoints
        if (!menuItemsResponse.success || !vendorsResponse.success ||
            !menuItemsResponse.data || menuItemsResponse.data.length === 0 ||
            !vendorsResponse.data || vendorsResponse.data.length === 0) {
          throw new Error('Failed to fetch data from backend');
        }

        // Create a map of vendor IDs to vendor objects for easy lookup
        const vendorMap: Record<string, Vendor> = {};
        vendorsResponse.data.forEach((vendor: Vendor) => {
          vendorMap[vendor._id] = vendor;
        });

        setMenuItems(menuItemsResponse.data);
        setVendors(vendorMap);

        // Format the menu items for display
        const formatted = menuItemsResponse.data.map((item: MenuItem) => {
          const vendor = vendorMap[item.vendorId];
          return {
            id: item._id,
            name: item.itemName,
            slug: item.slug,
            description: item.description,
            price: item.subcategories[0]?.price || 0,
            image: item.image,
            vendor: vendor ? vendor.name : 'Unknown Vendor',
            vendorId: item.vendorId,
            rating: vendor ? vendor.rating : 4.5,
            reviewCount: Math.floor(Math.random() * 100) + 20,
            category: item.itemCategory,
            subcategories: item.subcategories // Pass subcategories to FoodCard
          };
        });

        setFormattedDishes(formatted);
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Failed to load menu items from the backend. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Filter dishes based on search query and category filter
  useEffect(() => {
    if (menuItems.length > 0 && Object.keys(vendors).length > 0) {
      // Filter dynamic dishes
      const filtered = menuItems
        .filter(item => {
          const vendor = vendors[item.vendorId];
          const vendorName = vendor ? vendor.name : 'Unknown Vendor';

          const matchesCategory = !categoryFilter || item.itemCategory === categoryFilter;
          const matchesSearch = !searchQuery ||
            item.itemName.toLowerCase().includes(searchQuery.toLowerCase()) ||
            item.itemCategory.toLowerCase().includes(searchQuery.toLowerCase()) ||
            vendorName.toLowerCase().includes(searchQuery.toLowerCase());

          return matchesCategory && matchesSearch;
        })
        .map(item => {
          const vendor = vendors[item.vendorId];
          return {
            id: item._id,
            name: item.itemName,
            slug: item.slug,
            description: item.description,
            price: item.subcategories[0]?.price || 0,
            image: item.image,
            vendor: vendor ? vendor.name : 'Unknown Vendor',
            vendorId: item.vendorId,
            rating: vendor ? vendor.rating : 4.5,
            reviewCount: Math.floor(Math.random() * 100) + 20, // Random review count for demo
            category: item.itemCategory,
            subcategories: item.subcategories // Pass subcategories to FoodCard
          };
        });

      setFormattedDishes(filtered);
    }
  }, [searchQuery, categoryFilter, menuItems, vendors]);

  return (
    <div className="section-container">
      {loading && (
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-bandiwala-orange"></div>
        </div>
      )}

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative my-4" role="alert">
          <strong className="font-bold">Error!</strong>
          <span className="block sm:inline"> {error}</span>
          <div className="mt-2">
            <button
              onClick={() => window.location.reload()}
              className="bg-red-700 text-white px-4 py-2 rounded hover:bg-red-800"
            >
              Retry
            </button>
          </div>
        </div>
      )}

      {!loading && !error && formattedDishes.length === 0 && (
        <div className="text-center py-12">
          <div className="text-6xl mb-4">🍽️</div>
          <h3 className="text-xl font-semibold mb-2">No dishes found</h3>
          <p className="text-gray-500 mb-6">
            {categoryFilter
              ? `No dishes available in "${categoryFilter}" category.`
              : searchQuery
                ? `No dishes match your search "${searchQuery}".`
                : "No dishes available at the moment."
            }
          </p>
          <button
            onClick={() => window.location.reload()}
            className="bg-bandiwala-orange text-white px-6 py-3 rounded-full hover:bg-bandiwala-red transition-colors"
          >
            Refresh Menu
          </button>
        </div>
      )}

      {!loading && !error && formattedDishes.length > 0 && (
        <>
          <div className="mb-6">
            <div className="flex justify-between items-center">
              <div>
                <h2 className="text-xl font-semibold">
                  {categoryFilter ? `${categoryFilter} Dishes` : 'All Dishes'}
                </h2>
                <p className="text-gray-600">
                  {formattedDishes.length} dish{formattedDishes.length !== 1 ? 'es' : ''} available
                </p>
              </div>
              {(categoryFilter || searchQuery) && (
                <div className="text-sm text-gray-500">
                  {searchQuery && (
                    <span>Search: "{searchQuery}"</span>
                  )}
                  {categoryFilter && searchQuery && <span> • </span>}
                  {categoryFilter && (
                    <span>Category: {categoryFilter}</span>
                  )}
                </div>
              )}
            </div>
          </div>

          <div className="grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6">
            {formattedDishes.map((dish) => (
              <div key={dish.id}>
                <FoodCard {...dish} />
              </div>
            ))}
          </div>
        </>
      )}
    </div>
  );
}