
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Download, ShoppingBag } from 'lucide-react';
import { useRouter } from 'next/navigation';

const CTA = () => {
  const router = useRouter();
  const handleDownload = () => {
    router.push('/coming-soon');
  };
  return (
    <div className="bg-gradient-to-r from-bandiwala-orange to-bandiwala-red py-16">
      <div className="max-w-5xl mx-auto px-4 sm:px-6 text-center">
        <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
          Ready to Explore Street Food & Home Cooking?
        </h2>
        <p className="text-white text-opacity-90 text-lg mb-8 max-w-2xl mx-auto">
          Download our app to discover the best street food vendors and home chefs near you. Order food delivery, pickup, or book catering services.
        </p>
        
        <div className="flex flex-col sm:flex-row justify-center gap-4">
          <Button className="bg-white hover:bg-gray-100 text-bandiwala-red px-6 py-6 h-auto font-semibold" onClick={handleDownload}>
            <Download className="mr-2 h-5 w-5" />
            Download App
          </Button>
          <Button className="bg-bandiwala-brown hover:bg-bandiwala-brown/90 text-white px-6 py-6 h-auto font-semibold" onClick={handleDownload}>
            <ShoppingBag className="mr-2 h-5 w-5" />
            Order Now
          </Button>
        </div>
        
      </div>
    </div>
  );
};

export default CTA;
