import Image from "next/image";
import map from "@/assets/images/map.png";

const Hero = () => {
  return (
    <div className="relative bg-gradient-to-b from-amber-50 to-white">
      <div className="grid grid-cols-1 md:grid-cols-2 max-w-7xl mx-auto px-4 sm:px-6 py-8 md:py-12">
        {/* Text Content */}
        <div className="flex flex-col justify-center z-10 animate-fade-in">
          <div className="max-w-lg">
            <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold text-gray-900 leading-tight">
              Discover the Taste of <span className="text-bandiwala-orange">Street Food</span> & <span className="text-bandiwala-red">Home Cooking</span>
            </h1>

            <p className="mt-4 sm:mt-6 text-base sm:text-lg text-gray-600 leading-relaxed">
              From authentic street food to homemade delicacies. Order delivery, pickup, or book catering services - all in one place.
            </p>
          </div>
        </div>

        {/* Image Section */}
        <div className="hidden md:flex items-center justify-center relative animate-fade-in">
          <div className="relative w-[550px] h-[300px]">
            {/* Main image */}
            <Image
              src={map}
              fill={true}
              alt="Street food vendor"
              className="absolute rounded-3xl shadow-xl z-10 w-full h-[400px] animate-pulse-subtle z-0"
              priority
            />

            {/* Floating food cards */}
            <div className="absolute -top-10 -left-10 bg-white p-3 rounded-xl shadow-lg rotate-[-5deg] z-10">
              {/* <Image
                src={samosa}
                alt="Delicious dish"
                fill={true}
                className="w-32 h-32 object-cover rounded-lg"
                priority
              /> */}
              <p className="text-sm font-medium mt-1">Bandiwala</p>
              <div className="flex items-center text-sm mt-1">
                <span className="text-yellow-500">★★★★★</span>
                <span className="text-xs text-gray-600 ml-1">(128)</span>
              </div>
            </div>

            <div className="absolute -bottom-5 -right-5 bg-white p-3 rounded-xl shadow-lg rotate-[5deg] z-10">
              {/* <Image
                src={dosa}
                alt="Home-cooked meal"
                fill={true}
                className="w-32 h-32 object-cover rounded-lg"
                priority
              /> */}
              <p className="text-sm font-medium mt-1">Home-style Biryani</p>
              <div className="flex items-center text-sm mt-1">
                <span className="text-yellow-500">★★★★★</span>
                <span className="text-xs text-gray-600 ml-1">(86)</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Decorative elements */}
      <div className="absolute bottom-0 left-0 right-0">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 100" fill="white" className="w-full">
          <path d="M0,64L80,69.3C160,75,320,85,480,80C640,75,800,53,960,42.7C1120,32,1280,32,1360,32L1440,32L1440,100L1360,100C1280,100,1120,100,960,100C800,100,640,100,480,100C320,100,160,100,80,100L0,100Z"></path>
        </svg>
      </div>
    </div>
  );
};

export default Hero;
