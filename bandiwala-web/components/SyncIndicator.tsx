import React from 'react';

interface SyncIndicatorProps {
  isOnline: boolean;
  isSyncing: boolean;
  pendingChanges: boolean;
}

export const SyncIndicator: React.FC<SyncIndicatorProps> = ({ isOnline, isSyncing, pendingChanges }) => {
  if (!isOnline) {
    return (
      <div className="flex items-center text-yellow-600">
        <span className="w-2 h-2 bg-yellow-500 rounded-full mr-2"></span>
        <span className="text-sm">Offline Mode</span>
      </div>
    );
  }

  if (isSyncing) {
    return (
      <div className="flex items-center text-blue-600">
        <span className="w-2 h-2 bg-blue-500 rounded-full mr-2 animate-pulse"></span>
        <span className="text-sm">Syncing...</span>
      </div>
    );
  }

  if (pendingChanges) {
    return (
      <div className="flex items-center text-orange-600">
        <span className="w-2 h-2 bg-orange-500 rounded-full mr-2"></span>
        <span className="text-sm">Changes pending</span>
      </div>
    );
  }

  return (
    <div className="flex items-center text-green-600">
      <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
      <span className="text-sm">Synced</span>
    </div>
  );
};