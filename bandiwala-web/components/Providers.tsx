'use client';

import { CartProvider } from '@/contexts/CartContext';
import { AuthProvider } from '@/contexts/AuthContext';
import { ErrorBoundary } from 'react-error-boundary';
import { Toaster } from 'sonner';

function ErrorFallback() {
  return (
    <div className="flex items-center justify-center min-h-screen">
      <p>Something went wrong</p>
    </div>
  );
}

export function Providers({ children }: { children: React.ReactNode }) {
  return (
    <ErrorBoundary fallback={<ErrorFallback />}>
      <AuthProvider>
        <CartProvider>
          <Toaster
            position="top-center"
            expand={true}
            richColors
            closeButton
            duration={3000}
            style={{ zIndex: 9999 }}
          />
          {children}
        </CartProvider>
      </AuthProvider>
    </ErrorBoundary>
  );
}
