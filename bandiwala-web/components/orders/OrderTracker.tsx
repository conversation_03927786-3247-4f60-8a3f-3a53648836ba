'use client';

import React, { useState, useEffect } from 'react';
import { orderService } from '@/services/api';
import OrderTimeline from './OrderTimeline';
import OrderTimer from './OrderTimer';

interface OrderTrackerProps {
  orderId: string;
  orderNumber?: string;
  initialStatus: string;
  statusTimeline?: Array<{
    status: string;
    timestamp: string;
  }>;
  onStatusChange?: () => void;
}

interface TimerData {
  remainingSeconds: number;
  nextStatus: string;
  orderStatus: string;
  isDelivered: boolean;
  statusTimeline: Array<{
    status: string;
    timestamp: string;
  }>;
}

export default function OrderTracker({
  orderId,
  orderNumber,
  initialStatus,
  statusTimeline = [],
  onStatusChange
}: OrderTrackerProps) {
  const [timerData, setTimerData] = useState<TimerData>({
    remainingSeconds: 0,
    nextStatus: '',
    orderStatus: initialStatus,
    isDelivered: initialStatus === 'delivered',
    statusTimeline: statusTimeline
  });

  // Fetch timer data from API
  const fetchTimerData = async () => {
    try {
      const response = await orderService.getOrderTimer(orderId);
      
      if (response.success && response.data) {
        const newTimerData = {
          remainingSeconds: response.data.remainingSeconds || 0,
          nextStatus: response.data.nextStatus || '',
          orderStatus: response.data.orderStatus,
          isDelivered: response.data.isDelivered,
          statusTimeline: response.data.statusTimeline || statusTimeline
        };
        
        setTimerData(newTimerData);
        
        // Notify parent component of status change
        if (onStatusChange && newTimerData.orderStatus !== initialStatus) {
          onStatusChange();
        }
      }
    } catch (error) {
      console.error('Error fetching timer data:', error);
    }
  };

  // Update timer every 5 seconds
  useEffect(() => {
    // Initial fetch
    fetchTimerData();

    // Set up interval for updates
    const interval = setInterval(() => {
      if (timerData.isDelivered) {
        clearInterval(interval);
        return;
      }
      
      fetchTimerData();
    }, 5000);

    return () => clearInterval(interval);
  }, [orderId, timerData.isDelivered]);

  if (timerData.isDelivered) {
    return (
      <div className="space-y-6">
        <OrderTimeline
          currentStatus={timerData.orderStatus}
          statusTimeline={timerData.statusTimeline}
        />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <OrderTimeline
        currentStatus={timerData.orderStatus}
        statusTimeline={timerData.statusTimeline}
        nextStatus={timerData.nextStatus}
        remainingSeconds={timerData.remainingSeconds}
      />
      
      <OrderTimer
        orderId={orderId}
        orderNumber={orderNumber}
        initialStatus={timerData.orderStatus}
        onStatusChange={onStatusChange}
      />
    </div>
  );
}
