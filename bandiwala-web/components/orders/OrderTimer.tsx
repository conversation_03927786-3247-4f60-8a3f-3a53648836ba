'use client';

import React, { useState, useEffect } from 'react';
import { Clock, Package, CheckCircle } from 'lucide-react';
import { orderService } from '@/services/api';

interface OrderTimerProps {
  orderId: string;
  orderNumber?: string;
  initialStatus: string;
  onStatusChange?: (newStatus: string) => void;
}

interface TimerData {
  remainingSeconds: number;
  totalSeconds: number;
  orderStatus: string;
  nextStatus: string;
  isDelivered: boolean;
  statusTimeline: Array<{
    status: string;
    timestamp: string;
  }>;
}

export default function OrderTimer({ 
  orderId, 
  orderNumber, 
  initialStatus, 
  onStatusChange 
}: OrderTimerProps) {
  const [timerData, setTimerData] = useState<TimerData>({
    remainingSeconds: 0,
    totalSeconds: 0,
    orderStatus: initialStatus,
    nextStatus: '',
    isDelivered: initialStatus === 'delivered',
    statusTimeline: []
  });
  const [loading, setLoading] = useState(false);

  // Fetch timer data from API
  const fetchTimerData = async () => {
    try {
      setLoading(true);
      const response = await orderService.getOrderTimer(orderId);
      
      if (response.success && response.data) {
        const newTimerData = {
          remainingSeconds: response.data.remainingSeconds || 0,
          totalSeconds: response.data.totalSeconds || 0,
          orderStatus: response.data.orderStatus,
          nextStatus: response.data.nextStatus || '',
          isDelivered: response.data.isDelivered,
          statusTimeline: response.data.statusTimeline || []
        };

        setTimerData(newTimerData);

        // Notify parent component of status change
        if (onStatusChange && newTimerData.orderStatus !== initialStatus) {
          onStatusChange(newTimerData.orderStatus);
        }
      }
    } catch (error) {
      console.error('Error fetching timer data:', error);
    } finally {
      setLoading(false);
    }
  };

  // Update timer every second
  useEffect(() => {
    // Initial fetch
    fetchTimerData();

    // Set up interval for updates
    const interval = setInterval(() => {
      if (timerData.isDelivered) {
        clearInterval(interval);
        return;
      }
      
      // Fetch fresh data every 5 seconds
      fetchTimerData();
      
      // Update local countdown every second
      setTimerData(prev => ({
        ...prev,
        remainingSeconds: Math.max(0, prev.remainingSeconds - 1)
      }));
    }, 1000);

    return () => clearInterval(interval);
  }, [orderId, timerData.isDelivered]);

  // Format time display
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  // Get status display info
  const getStatusInfo = () => {
    switch (timerData.orderStatus) {
      case 'placed':
        return {
          icon: <Package className="h-5 w-5 text-blue-600" />,
          text: 'Order placed',
          color: 'bg-blue-50 border-blue-200 text-blue-800',
          showTimer: true
        };
      case 'confirmed':
        return {
          icon: <CheckCircle className="h-5 w-5 text-indigo-600" />,
          text: 'Order confirmed',
          color: 'bg-indigo-50 border-indigo-200 text-indigo-800',
          showTimer: true
        };
      case 'preparing':
        return {
          icon: <Package className="h-5 w-5 text-yellow-600" />,
          text: 'Preparing your order',
          color: 'bg-yellow-50 border-yellow-200 text-yellow-800',
          showTimer: true
        };
      case 'out_for_delivery':
        return {
          icon: <Clock className="h-5 w-5 text-orange-600" />,
          text: 'Out for delivery',
          color: 'bg-orange-50 border-orange-200 text-orange-800',
          showTimer: true
        };
      case 'delivered':
        return {
          icon: <CheckCircle className="h-5 w-5 text-green-600" />,
          text: 'Order delivered!',
          color: 'bg-green-50 border-green-200 text-green-800',
          showTimer: false
        };
      default:
        return {
          icon: <Clock className="h-5 w-5 text-gray-600" />,
          text: 'Processing order',
          color: 'bg-gray-50 border-gray-200 text-gray-800',
          showTimer: false
        };
    }
  };

  const statusInfo = getStatusInfo();

  if (timerData.isDelivered) {
    return (
      <div className={`flex items-center gap-3 p-4 rounded-lg border ${statusInfo.color}`}>
        {statusInfo.icon}
        <div>
          <p className="font-medium">{statusInfo.text}</p>
          <p className="text-sm opacity-75">Your order is ready for review!</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`flex items-center justify-between p-4 rounded-lg border ${statusInfo.color}`}>
      <div className="flex items-center gap-3">
        {statusInfo.icon}
        <div>
          <p className="font-medium">{statusInfo.text}</p>
          <p className="text-sm opacity-75">
            {orderNumber ? `Order ${orderNumber}` : 'Your order'}
          </p>
        </div>
      </div>
      
      {statusInfo.showTimer && (
        <div className="flex items-center gap-4">
          {timerData.remainingSeconds > 0 && timerData.nextStatus && (
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4" />
              <div className="text-right">
                <p className="font-mono text-lg font-bold">
                  {formatTime(timerData.remainingSeconds)}
                </p>
                <p className="text-xs opacity-75">until {timerData.nextStatus.replace('_', ' ')}</p>
              </div>
            </div>
          )}
          {timerData.totalSeconds > 0 && (
            <div className="flex items-center gap-2">
              <Package className="h-4 w-4" />
              <div className="text-right">
                <p className="font-mono text-sm font-medium">
                  {formatTime(timerData.totalSeconds)}
                </p>
                <p className="text-xs opacity-75">total delivery time</p>
              </div>
            </div>
          )}
        </div>
      )}
      
      {loading && (
        <div className="flex items-center gap-2">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current"></div>
          <span className="text-sm">Updating...</span>
        </div>
      )}
    </div>
  );
}
