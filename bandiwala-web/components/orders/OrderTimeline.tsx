'use client';

import React from 'react';
import { 
  Package, 
  CheckCircle, 
  Clock, 
  Truck, 
  ChefHat,
  MapPin 
} from 'lucide-react';
import { formatTime } from '@/lib/utils';

interface TimelineEntry {
  status: string;
  timestamp: string;
}

interface OrderTimelineProps {
  currentStatus: string;
  statusTimeline: TimelineEntry[];
  nextStatus?: string;
  remainingSeconds?: number;
}

const statusConfig = {
  placed: {
    icon: Package,
    label: 'Order Placed',
    description: 'Your order has been received',
    color: 'text-blue-600',
    bgColor: 'bg-blue-100',
    borderColor: 'border-blue-200'
  },
  confirmed: {
    icon: CheckCircle,
    label: 'Order Confirmed',
    description: 'Restaurant confirmed your order',
    color: 'text-indigo-600',
    bgColor: 'bg-indigo-100',
    borderColor: 'border-indigo-200'
  },
  preparing: {
    icon: ChefHat,
    label: 'Preparing',
    description: 'Your food is being prepared',
    color: 'text-yellow-600',
    bgColor: 'bg-yellow-100',
    borderColor: 'border-yellow-200'
  },
  out_for_delivery: {
    icon: Truck,
    label: 'Out for Delivery',
    description: 'Your order is on the way',
    color: 'text-orange-600',
    bgColor: 'bg-orange-100',
    borderColor: 'border-orange-200'
  },
  delivered: {
    icon: MapPin,
    label: 'Delivered',
    description: 'Order delivered successfully',
    color: 'text-green-600',
    bgColor: 'bg-green-100',
    borderColor: 'border-green-200'
  }
};

const statusOrder = ['placed', 'confirmed', 'preparing', 'out_for_delivery', 'delivered'];

export default function OrderTimeline({ 
  currentStatus, 
  statusTimeline, 
  nextStatus, 
  remainingSeconds 
}: OrderTimelineProps) {
  
  // Create a map of status to timestamp
  const timelineMap = statusTimeline.reduce((acc, entry) => {
    acc[entry.status] = entry.timestamp;
    return acc;
  }, {} as Record<string, string>);

  const formatTimeDisplay = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getStatusState = (status: string) => {
    const currentIndex = statusOrder.indexOf(currentStatus);
    const statusIndex = statusOrder.indexOf(status);
    
    if (statusIndex < currentIndex) {
      return 'completed';
    } else if (statusIndex === currentIndex) {
      return 'current';
    } else {
      return 'pending';
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm p-4 sm:p-6">
      <h3 className="text-lg font-semibold mb-4 sm:mb-6">Order Tracking</h3>

      <div className="space-y-4 sm:space-y-6">
        {statusOrder.map((status, index) => {
          const config = statusConfig[status as keyof typeof statusConfig];
          const state = getStatusState(status);
          const timestamp = timelineMap[status];
          const Icon = config.icon;

          const isLast = index === statusOrder.length - 1;
          const isCurrent = state === 'current';
          const isCompleted = state === 'completed';
          const isPending = state === 'pending';

          return (
            <div key={status} className="relative flex items-start">
              {/* Timeline line */}
              {!isLast && (
                <div
                  className={`absolute left-5 sm:left-6 top-10 sm:top-12 w-0.5 h-4 sm:h-6 ${
                    isCompleted ? 'bg-green-400' : 'bg-gray-200'
                  }`}
                />
              )}

              {/* Status icon */}
              <div
                className={`relative z-10 flex items-center justify-center w-10 h-10 sm:w-12 sm:h-12 rounded-full border-2 flex-shrink-0 ${
                  isCompleted
                    ? 'bg-green-100 border-green-400 text-green-600'
                    : isCurrent
                    ? `${config.bgColor} ${config.borderColor} ${config.color}`
                    : 'bg-gray-100 border-gray-200 text-gray-400'
                }`}
              >
                <Icon className="w-4 h-4 sm:w-5 sm:h-5" />
              </div>

              {/* Status content */}
              <div className="ml-3 sm:ml-4 flex-1 min-w-0">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
                  <div className="flex-1">
                    <h4 className={`font-medium text-sm sm:text-base ${
                      isCompleted || isCurrent ? 'text-gray-900' : 'text-gray-500'
                    }`}>
                      {config.label}
                    </h4>
                    <p className={`text-xs sm:text-sm mt-1 leading-relaxed ${
                      isCompleted || isCurrent ? 'text-gray-600' : 'text-gray-400'
                    }`}>
                      {config.description}
                    </p>
                  </div>

                  {/* Timestamp or countdown */}
                  <div className="text-left sm:text-right flex-shrink-0">
                    {timestamp && (
                      <p className="text-xs sm:text-sm text-gray-500">
                        {formatTime(new Date(timestamp))}
                      </p>
                    )}
                    {isCurrent && nextStatus && remainingSeconds && remainingSeconds > 0 && (
                      <div className="text-xs sm:text-sm mt-1">
                        <p className="text-gray-500">Next: {statusConfig[nextStatus as keyof typeof statusConfig]?.label}</p>
                        <p className="font-mono font-bold text-blue-600 text-sm sm:text-base">
                          {formatTimeDisplay(remainingSeconds)}
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Estimated delivery time */}
      {currentStatus !== 'delivered' && (
        <div className="mt-4 sm:mt-6 p-3 sm:p-4 bg-blue-50 rounded-lg border border-blue-200">
          <div className="flex items-center gap-2">
            <Clock className="w-4 h-4 text-blue-600 flex-shrink-0" />
            <p className="text-xs sm:text-sm text-blue-800">
              <span className="font-medium">Estimated delivery:</span> 10 minutes total
            </p>
          </div>
        </div>
      )}
    </div>
  );
}
