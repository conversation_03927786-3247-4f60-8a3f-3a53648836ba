import React, { useState } from 'react';
import { Order, OrderStatus } from '@/types/order';
import { formatDate, formatTime } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { ChevronRight, MapPin, Clock, Package, RotateCcw } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';
import { getImageUrl } from '@/utils/imageUtils';
import { formatOrderIdForDisplay } from '@/lib/orderUtils';
import { useCart } from '@/contexts/CartContext';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';

interface OrderCardProps {
  order: Order;
}

const OrderCard: React.FC<OrderCardProps> = ({ order }) => {
  const { reorderFromOrder, loading: cartLoading } = useCart();
  const router = useRouter();
  const [reordering, setReordering] = useState(false);

  // Format the date and time
  let formattedDate = '';
  let formattedTime = '';

  try {
    if (order.createdAt) {
      const orderDate = new Date(order.createdAt);
      formattedDate = formatDate(orderDate);
      formattedTime = formatTime(orderDate);
    } else {
      console.warn('Order createdAt is missing:', order);
      formattedDate = 'Unknown date';
      formattedTime = 'Unknown time';
    }
  } catch (error) {
    console.error('Error formatting order date:', error);
    formattedDate = 'Invalid date';
    formattedTime = 'Invalid time';
  }

  // Get status badge color
  const getStatusColor = (status: OrderStatus) => {
    switch (status) {
      case 'placed':
        return 'bg-blue-100 text-blue-800';
      case 'confirmed':
        return 'bg-indigo-100 text-indigo-800';
      case 'preparing':
        return 'bg-yellow-100 text-yellow-800';
      case 'out_for_delivery':
        return 'bg-orange-100 text-orange-800';
      case 'delivered':
        return 'bg-green-100 text-green-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Format status text
  const formatStatus = (status: OrderStatus) => {
    switch (status) {
      case 'out_for_delivery':
        return 'Out for Delivery';
      default:
        return status.charAt(0).toUpperCase() + status.slice(1);
    }
  };

  const handleReorder = async (e: React.MouseEvent) => {
    e.preventDefault(); // Prevent navigation to order details
    e.stopPropagation();

    if (!order || !order.items || order.items.length === 0) {
      toast.error('No items to reorder');
      return;
    }

    setReordering(true);
    try {
      await reorderFromOrder(order.items);
      // Navigate to cart after successful reorder
      router.push('/cart');
    } catch (error) {
      console.error('Error reordering:', error);
      toast.error('Failed to reorder items. Please try again.');
    } finally {
      setReordering(false);
    }
  };

  // Safely get order ID - prioritize orderNumber over _id
  const getOrderId = () => {
    try {
      // Use orderNumber if available, otherwise fall back to _id
      const orderId = order.orderNumber || order._id;
      if (orderId) {
        return formatOrderIdForDisplay(orderId);
      }
      return 'Unknown';
    } catch (error) {
      console.error('Error getting order ID:', error);
      return 'Error';
    }
  };

  // Safely get total price
  const getFormattedTotal = () => {
    try {
      if (typeof order.total === 'number') {
        return `₹${order.total.toFixed(2)}`;
      }
    } catch (error) {
      console.error('Error formatting total:', error);
    }
  };

  // Safely get items count
  const getItemsCount = () => {
    try {
      if (Array.isArray(order.items)) {
        return order.items.length;
      }
      return 0;
    } catch (error) {
      console.error('Error getting items count:', error);
      return 0;
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm overflow-hidden border border-gray-100">
      {/* Order header */}
      <div className="p-4 border-b border-gray-100 flex justify-between items-center">
        <div>
          <div className="flex items-center gap-2">
            <h3 className="font-medium">Order #{getOrderId()}</h3>
            <span className={`text-xs px-2 py-1 rounded-full ${getStatusColor(order.orderStatus)}`}>
              {formatStatus(order.orderStatus)}
            </span>
          </div>
          <p className="text-sm text-gray-500">
            {formattedDate} at {formattedTime}
          </p>
        </div>
        <div className="text-right">
          <p className="font-medium">{getFormattedTotal()}</p>
          <p className="text-sm text-gray-500">{getItemsCount()} items</p>
        </div>
      </div>

      {/* Order items preview (show first 2 items) */}
      <div className="p-4 border-b border-gray-100">
        <div className="space-y-3">
          {Array.isArray(order.items) && order.items.length > 0 ? (
            <>
              {order.items.slice(0, 2).map((item, index) => (
                <div key={`${item.menuItemId || 'unknown'}-${index}`} className="flex items-center gap-3">
                  <div className="w-12 h-12 rounded-md overflow-hidden bg-gray-100 flex-shrink-0">
                    {item.image ? (
                      <Image
                        src={getImageUrl(item.image)}
                        alt={item.name || 'Food item'}
                        width={48}
                        height={48}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center bg-gray-200">
                        <Package className="h-6 w-6 text-gray-400" />
                      </div>
                    )}
                  </div>
                  <div className="flex-grow">
                    <p className="font-medium text-sm">{item.name || 'Unknown item'}</p>
                    <p className="text-xs text-gray-500">
                      {item.selectedSubcategory?.title || 'Regular'} • Qty: {item.quantity || 1}
                    </p>
                  </div>
                  <div className="text-right">
                  </div>
                </div>
              ))}

              {order.items.length > 2 && (
                <p className="text-sm text-gray-500 mt-2">
                  +{order.items.length - 2} more items
                </p>
              )}
            </>
          ) : (
            <p className="text-sm text-gray-500">No items found in this order</p>
          )}
        </div>
      </div>

      {/* Order footer with delivery info and action button */}
      <div className="p-4 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="flex items-start gap-2">
          <MapPin className="h-4 w-4 text-gray-500 mt-0.5" />
          <div>
            {order.deliveryAddress && order.deliveryAddress.formatted ? (
              <p className="text-sm text-gray-700 line-clamp-1">{order.deliveryAddress.formatted}</p>
            ) : (
              <p className="text-sm text-gray-700">Address not available</p>
            )}
            {order.estimatedDeliveryTime && (
              <div className="flex items-center gap-1 mt-1">
                <Clock className="h-3 w-3 text-gray-500" />
                <p className="text-xs text-gray-500">{order.estimatedDeliveryTime}</p>
              </div>
            )}
          </div>
        </div>

        <div className="flex gap-2">
          <Button
            onClick={handleReorder}
            disabled={reordering || cartLoading}
            variant="outline"
            size="sm"
            className="whitespace-nowrap"
          >
            {reordering ? (
              <>
                <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-current mr-1"></div>
                Adding...
              </>
            ) : (
              <>
                <RotateCcw className="h-3 w-3 mr-1" />
                Reorder
              </>
            )}
          </Button>

          {(() => {
            const orderId = order.orderNumber || order._id || 'unknown';
            // Don't create links for temporary order IDs
            if (orderId.startsWith('TEMP-')) {
              return (
                <Button
                  variant="outline"
                  size="sm"
                  className="whitespace-nowrap opacity-50 cursor-not-allowed"
                  disabled
                  title="This is a temporary order ID. The actual order may still be processing."
                >
                  Processing...
                  <Clock className="h-4 w-4 ml-1" />
                </Button>
              );
            }

            return (
              <Link href={`/orders/${orderId}`}>
                <Button variant="outline" size="sm" className="whitespace-nowrap">
                  View Details
                  <ChevronRight className="h-4 w-4 ml-1" />
                </Button>
              </Link>
            );
          })()}
        </div>
      </div>
    </div>
  );
};

export default OrderCard;
