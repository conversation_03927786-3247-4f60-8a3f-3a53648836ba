/**
 * Utility functions for order ID generation and formatting
 */

/**
 * Generates a consistent temporary order ID
 * Format: TEMP-YYYYMMDD-HHMMSS-XXX
 */
export const generateTempOrderId = (): string => {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  const seconds = String(now.getSeconds()).padStart(2, '0');
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');

  return `TEMP-${year}${month}${day}-${hours}${minutes}${seconds}-${random}`;
};

/**
 * Formats an order ID for display
 * - Always show the full order ID for consistency between email and orders page
 * - If it's a temporary ID (starts with TEMP-), show the full ID
 * - For MongoDB ObjectIds or other IDs, show the full ID
 */
export const formatOrderIdForDisplay = (orderId: string): string => {
  if (!orderId) return 'Unknown';

  // Always return the full order ID for consistency
  return orderId;
};

/**
 * Checks if an order ID is a temporary ID
 */
export const isTempOrderId = (orderId: string): boolean => {
  return orderId.startsWith('TEMP-');
};

/**
 * Extracts order ID from various response formats
 * Prioritizes orderNumber over _id for better user experience
 */
export const extractOrderId = (orderResponse: any): string => {
  // First try to get the custom order number
  const orderNumber = orderResponse?.data?.orderNumber ||
                     orderResponse?.orderNumber ||
                     orderResponse?.order?.orderNumber;

  if (orderNumber) {
    return orderNumber;
  }

  // Fallback to MongoDB _id
  return orderResponse?.data?._id ||
         orderResponse?.order?._id ||
         orderResponse?._id ||
         generateTempOrderId();
};
