import { NextRequest } from 'next/server';
import jwt from 'jsonwebtoken';

interface User {
  id: string;
  userId: string;
  email: string;
  name: string;
  role: string;
}

interface AuthResult {
  isAuthenticated: boolean;
  user: User | null;
}

export function verifyAuth(request: NextRequest): AuthResult {
  try {
    // Get token from cookies or Authorization header
    let token: string | null = null;

    // Check cookies first
    const cookies = request.headers.get('cookie');
    if (cookies) {
      const tokenMatch = cookies.match(/token=([^;]+)/);
      if (tokenMatch) {
        token = tokenMatch[1];
      }
    }

    // Check Authorization header if no cookie token
    if (!token) {
      const authHeader = request.headers.get('authorization');
      if (authHeader && authHeader.startsWith('Bearer ')) {
        token = authHeader.substring(7);
      }
    }

    if (!token) {
      return { isAuthenticated: false, user: null };
    }

    // Verify the token
    const jwtSecret = process.env.JWT_SECRET || 'your-secret-key';
    const decoded = jwt.verify(token, jwtSecret) as any;

    if (!decoded || !decoded.id) {
      return { isAuthenticated: false, user: null };
    }

    const user: User = {
      id: decoded.id,
      userId: decoded.id,
      email: decoded.email || '',
      name: decoded.name || '',
      role: decoded.role || 'user'
    };

    return { isAuthenticated: true, user };

  } catch (error) {
    console.error('Auth verification error:', error);
    return { isAuthenticated: false, user: null };
  }
}
