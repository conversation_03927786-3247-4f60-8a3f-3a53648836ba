// Define Razorpay types
declare global {
  namespace RazorpayCheckout {
    interface PrefillData {
      name?: string;
      email?: string;
      contact?: string;
    }

    interface ThemeData {
      color?: string;
      backdrop_color?: string;
      hide_topbar?: boolean;
    }

    interface Options {
      key: string;
      amount: number;
      currency: string;
      name: string;
      description?: string;
      image?: string;
      order_id: string;
      handler: (response: RazorpayResponse) => void;
      prefill?: PrefillData;
      notes?: Record<string, string>;
      theme?: ThemeData;
      modal?: {
        ondismiss?: () => void;
        animation?: boolean;
        confirm_close?: boolean;
        escape?: boolean;
        backdropclose?: boolean;
        handleback?: boolean;
      };
    }

    interface RazorpayResponse {
      razorpay_payment_id: string;
      razorpay_order_id: string;
      razorpay_signature: string;
    }

    interface RazorpayInstance {
      on: (event: string, handler: (response: any) => void) => void;
      open: () => void;
      close: () => void;
    }
  }

  interface Window {
    Razorpay: new (options: RazorpayCheckout.Options) => RazorpayCheckout.RazorpayInstance;
  }
}

/**
 * Dynamically loads the Razorpay script
 * @returns Promise that resolves to true if script loaded successfully, false otherwise
 */
export const loadRazorpay = (): Promise<boolean> => {
  return new Promise((resolve) => {
    // If Razorpay is already loaded, resolve immediately
    if (window.Razorpay) {
      resolve(true);
      return;
    }

    // Create script element
    const script = document.createElement('script');
    script.src = 'https://checkout.razorpay.com/v1/checkout.js';
    script.async = true;
    script.onload = () => resolve(true);
    script.onerror = () => {
      console.error('Failed to load Razorpay SDK');
      resolve(false);
    };

    // Add script to document
    document.body.appendChild(script);
  });
};
