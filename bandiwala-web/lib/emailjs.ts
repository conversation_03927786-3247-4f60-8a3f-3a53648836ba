import emailjs from '@emailjs/browser';

// Initialize EmailJS with the user ID
// This is important for client-side usage
const initEmailJS = () => {
  const userId = process.env.NEXT_PUBLIC_EMAILJS_USER_ID;
  if (userId) {
    emailjs.init(userId);
    console.log('<PERSON><PERSON><PERSON><PERSON> initialized with user ID');
  } else {
    console.error('<PERSON>ailJS user ID is missing');
  }
};

// Initialize EmailJS if we're in a browser environment
if (typeof window !== 'undefined') {
  initEmailJS();
}

interface OrderDetails {
  orderId: string;
  amount: number;
  items: Array<{
    name: string;
    quantity: number;
    price: number;
  }>;
  customerName: string;
  customerEmail: string;
  customerPhone?: string;
  deliveryAddress: string;
}

/**
 * Sends an order confirmation email using EmailJS
 *
 * @param orderDetails - Details of the order to include in the email
 * @returns Promise that resolves when the email is sent
 */
export const sendOrderConfirmationEmail = async (orderDetails: OrderDetails): Promise<void> => {
  try {
    const serviceId = process.env.NEXT_PUBLIC_EMAILJS_SERVICE_ID;
    const templateId = process.env.NEXT_PUBLIC_EMAILJS_TEMPLATE_ID;
    const userId = process.env.NEXT_PUBLIC_EMAILJS_USER_ID;
    const toEmail = process.env.NEXT_PUBLIC_EMAILJS_TO_EMAIL;

    console.log('EmailJS Configuration:', {
      serviceId,
      templateId,
      userId,
      toEmail: toEmail || '<EMAIL>' 
    });

    if (!serviceId || !templateId || !userId) {
      console.error('EmailJS configuration is missing');
      return;
    }

    // Format the items for the email template
    const itemsList = orderDetails.items
      .map(item => `${item.name} x${item.quantity} - ₹${item.price.toFixed(2)}`)
      .join('\n');

    // Prepare the template parameters
    const templateParams = {
      // EmailJS standard parameters
      to_name: 'Karthik', // Recipient name
      from_name: 'Bandiwala Order System',
      reply_to: '<EMAIL>', // Reply-to address
      recipient: toEmail || '<EMAIL>', // Try different parameter name
      email: toEmail || '<EMAIL>', // Try another parameter name
      to_email: toEmail || '<EMAIL>', // Original parameter

      // Order details
      order_id: orderDetails.orderId,
      customer_name: orderDetails.customerName,
      customer_email: orderDetails.customerEmail,
      phone_number: orderDetails.customerPhone || 'Not provided',
      order_amount: `₹${orderDetails.amount.toFixed(2)}`,
      order_items: itemsList,
      delivery_address: orderDetails.deliveryAddress,
      order_date: new Date().toLocaleDateString('en-IN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      })
    };

    console.log('Sending email with parameters:', templateParams);

    // Send the email
    const response = await emailjs.send(serviceId, templateId, templateParams, userId);
    console.log('EmailJS Response:', response);
    console.log('Order confirmation email sent successfully to', toEmail || '<EMAIL>');
  } catch (error) {
    console.error('Failed to send order confirmation email:', error);
    // Log more detailed error information
    if (error instanceof Error) {
      console.error('Error details:', {
        name: error.name,
        message: error.message,
        stack: error.stack
      });
    }
  }
};
