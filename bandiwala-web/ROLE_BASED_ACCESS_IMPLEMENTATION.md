# Role-Based Access Control Implementation for Bandiwala Web

## Overview
This document outlines the implementation of role-based access control in the bandiwala-web frontend to ensure only users with the "user" role are handled or shown in the application.

## Backend Schema
The User schema in `server/models/usermodel.js` includes:
```javascript
role: {
  type: String,
  enum: ["user", "vendor", "deliveryPartner", "admin"],
  default: "user",
}
```

## Frontend Implementation

### 1. User Interface Update
**File:** `bandiwala-web/contexts/AuthContext.tsx`

- Added `role?: string` field to the User interface
- Implemented role validation in user data fetching and authentication flows
- Only allows users with "user" role or no role (defaults to "user")

### 2. Authentication Context Changes
**File:** `bandiwala-web/contexts/AuthContext.tsx`

#### Key Changes:
- **fetchUserData()**: Added role validation for backend and fallback API responses
- **login()**: Added role validation for stored user data
- **updateUser()**: Added role validation for updated user data
- **localStorage handling**: Only stores user data if role is "user"
- **clearAuthData()**: Utility function to clear all authentication data
- **Startup validation**: Checks stored user data on app load and clears non-user roles

### 3. Login Page Enhancements
**File:** `bandiwala-web/app/login/page.tsx`

#### Key Changes:
- **Pre-login validation**: Checks user role before calling AuthContext login
- **Dual validation**: Both backend and fallback API responses are validated
- **Early rejection**: Admin users are rejected before token storage

#### Role Validation Logic:
```javascript
// Only allow users with "user" role or no role (defaults to "user")
if (!data.user.role || data.user.role === "user") {
  setUser(data.user);
} else {
  throw new Error('Access denied: This application is only for regular users');
}
```

### 3. API Route Updates
**File:** `bandiwala-web/app/api/me/route.ts`

#### Changes:
- Added role validation in backend response handling
- Added role validation in token payload processing
- Ensures fallback users are always created with "user" role
- Rejects access for users with non-"user" roles

### 4. Admin Access Control
**File:** `bandiwala-web/app/admin/moderation/page.tsx`

#### Changes:
- Updated admin check to use proper role-based access control
- Changed from email-based admin detection to role-based checking:
```javascript
// Before: email-based check
const isAdmin = user?.email?.includes('admin') || ...

// After: role-based check
const isAdmin = user?.role === 'admin';
```

### 5. Service Layer Updates
**File:** `bandiwala-web/services/api.ts`

#### Changes:
- **login()**: Added role validation immediately after backend response
- **getCurrentUser()**: Added role validation before storing user data
- **updateUserProfile()**: Added role validation for updated user data
- Only stores user data in localStorage if role is "user"

## Implementation Details

### Role Filtering Logic
The application implements role filtering at multiple levels:

1. **Authentication Level**: Users with non-"user" roles are rejected during login
2. **Data Storage Level**: Only "user" role data is stored in localStorage
3. **API Level**: API routes validate user roles before processing requests
4. **Admin Access Level**: Admin functions require "admin" role

### Default Behavior
- New users automatically get "user" role (backend default)
- Users without explicit role are treated as "user" role
- Registration process maintains backend defaults

### Error Handling
When a user with non-"user" role attempts access:
- Clear error message: "Access denied: This application is only for regular users"
- Authentication tokens are cleared
- User is redirected appropriately

## Files Modified

1. `bandiwala-web/contexts/AuthContext.tsx` - Added role field, validation, and auth data clearing
2. `bandiwala-web/app/login/page.tsx` - Added pre-login role validation
3. `bandiwala-web/app/admin/moderation/page.tsx` - Updated admin access control
4. `bandiwala-web/app/api/me/route.ts` - Added role validation in API routes
5. `bandiwala-web/services/api.ts` - Added role filtering in login and user services

## Testing Recommendations

1. **User Role Testing**: Verify users with "user" role can access all features
2. **Admin Role Testing**: Verify admin users (like Karthik Gurram) are rejected at login
3. **Non-User Role Testing**: Verify users with vendor/deliveryPartner roles are rejected
4. **Registration Testing**: Verify new users get "user" role by default
5. **Admin Access Testing**: Verify only "admin" role users can access admin features
6. **Fallback Testing**: Verify role validation works in fallback scenarios
7. **Storage Clearing**: Verify auth data is cleared when non-user roles are detected

### Manual Testing Steps
1. Try logging in with admin credentials (Phone: +************, Password: plplplpl)
2. Verify login is rejected with "Access denied" message
3. Check that localStorage is cleared after failed admin login
4. Verify regular user accounts can still login normally

## Notes

- The bandiwala-web frontend doesn't have user management functionality that lists multiple users
- User management features exist in the Flutter app (Bandiwala/bandiwala)
- This implementation focuses on individual user operations and access control
- Backend filtering for admin functions should be implemented server-side
