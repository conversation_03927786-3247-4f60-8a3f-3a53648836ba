# Deployment Instructions for Bandiwala Web

## Issues Fixed

### 1. Image Loading Errors (400 Bad Request)
**Problem**: Images were trying to load from `localhost:4000` in production
**Solution**: 
- Updated `.env` to include `NEXT_PUBLIC_API_URL=https://server-8kpf.onrender.com`
- Created `.env.production` with production environment variables
- Updated `services/api.ts` to use environment variable for API URL
- Added production server domain to Next.js image configuration

### 2. Cart API Authentication Errors (401 Unauthorized)
**Problem**: Cart API calls failing due to CORS and authentication issues
**Solution**:
- Added deployed frontend URL to server CORS configuration
- Updated server `app.js` to include `https://bandiwala-web-git-main-gurramkarthiknethas-projects.vercel.app`

## Deployment Steps

### For Frontend (Vercel)

1. **Environment Variables**: Set these in Vercel dashboard:
   ```
   NEXT_PUBLIC_API_URL=https://server-8kpf.onrender.com
   NEXT_PUBLIC_OPEN_CAGE_KEY=ea96f60412194cfc8db331bd653044af
   NEXT_PUBLIC_GOOGLE_MAPS_KEY=AIzaSyC6zSUlV1nOcJ0EU3_i3VmdTbaVgH3k9gU
   NEXT_PUBLIC_RAZORPAY_KEY_ID=rzp_test_rwHZNW24he5sTF
   NEXT_PUBLIC_EMAILJS_SERVICE_ID=service_8jjmghb
   NEXT_PUBLIC_EMAILJS_TEMPLATE_ID=template_qds7t9j
   NEXT_PUBLIC_EMAILJS_USER_ID=_Ph1nmwuslpURgFKm
   NEXT_PUBLIC_EMAILJS_TO_EMAIL=<EMAIL>
   ```

2. **Deploy**: Push changes to your repository and Vercel will auto-deploy

### For Backend (Render)

1. **Environment Variables**: Ensure these are set in Render dashboard:
   ```
   FRONTEND_URL=https://bandiwala-web-git-main-gurramkarthiknethas-projects.vercel.app
   MONGO_URL=your_mongodb_connection_string
   JWT_SECRET=your_jwt_secret
   JWT_EXPIRE=7d
   COOKIE_EXPIRE=7
   ```

2. **Deploy**: Push changes to trigger redeploy

## Testing After Deployment

1. **Images**: Check that food item images load properly
2. **Authentication**: Test login/logout functionality
3. **Cart**: Test adding items to cart (should not get 401 errors)
4. **API Calls**: Monitor browser console for any remaining errors

## Troubleshooting

If you still see issues:

1. **Clear browser cache** and hard refresh (Ctrl+Shift+R)
2. **Check browser console** for any remaining errors
3. **Verify environment variables** are set correctly in both Vercel and Render
4. **Check server logs** in Render dashboard for any backend errors
