// Script to clean up user data with placeholder values
// This can be run manually to fix existing problematic user data

const cleanupUser = async (phone) => {
  try {
    const response = await fetch('http://localhost:3111/api/auth/cleanup', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ phone })
    });

    const data = await response.json();
    
    if (data.success) {
      console.log(`✅ Cleaned up user with phone ${phone}`);
      console.log('Updated user data:', data.user);
    } else {
      console.log(`❌ Failed to clean up user with phone ${phone}: ${data.message}`);
    }
  } catch (error) {
    console.error(`❌ Error cleaning up user with phone ${phone}:`, error.message);
  }
};

// Example usage:
// cleanupUser('+919974885015');

console.log('User cleanup script loaded. Use cleanupUser(phone) to clean up a specific user.');
console.log('Example: cleanupUser("+919974885015")');
