'use client';

import { useState, useEffect } from 'react';

const SEARCH_HISTORY_KEY = 'search_history';
const MAX_HISTORY_ITEMS = 5;

export function useSearchHistory() {
  const [recentSearches, setRecentSearches] = useState<string[]>([]);

  useEffect(() => {
    const history = localStorage.getItem(SEARCH_HISTORY_KEY);
    if (history) {
      setRecentSearches(JSON.parse(history));
    }
  }, []);

  const addToHistory = (search: string) => {
    const newHistory = [
      search,
      ...recentSearches.filter(item => item !== search)
    ].slice(0, MAX_HISTORY_ITEMS);
    
    setRecentSearches(newHistory);
    localStorage.setItem(SEARCH_HISTORY_KEY, JSON.stringify(newHistory));
  };

  const clearHistory = () => {
    setRecentSearches([]);
    localStorage.removeItem(SEARCH_HISTORY_KEY);
  };

  return { recentSearches, addToHistory, clearHistory };
}
