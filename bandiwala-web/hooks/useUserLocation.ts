'use client';

import { useState, useCallback, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import useReverseGeocode from './useReverseGeocode';
import { isWithinDeliveryArea } from '@/utils/distance';

export interface Coordinates {
  lat: number;
  lng: number;
}

export interface UserLocationState {
  coordinates: Coordinates | null;
  formattedAddress: string | null;
  loading: boolean;
  error: string | null;
  isWithinServiceArea: boolean;
}

export default function useUserLocation() {
  const { user, updateUser } = useAuth();
  const [state, setState] = useState<UserLocationState>({
    coordinates: user?.location?.coordinates || null,
    formattedAddress: user?.location?.formattedAddress || null,
    loading: false,
    error: null,
    isWithinServiceArea: false
  });

  const { reverseGeocode, address, loading: geocodeLoading, error: geocodeError } = useReverseGeocode();

  // Update state when user data changes
  useEffect(() => {
    if (user?.location) {
      const coords = user.location?.coordinates;
      const isWithinArea = coords ? isWithinDeliveryArea(coords) : false;

      setState(prev => ({
        ...prev,
        coordinates: coords || null,
        formattedAddress: user.location?.formattedAddress || null,
        isWithinServiceArea: isWithinArea
      }));
    }
  }, [user]);

  // Get current location from browser
  const getCurrentLocation = useCallback(() => {
    console.log('getCurrentLocation called');
    setState(prev => ({ ...prev, loading: true, error: null }));

    if (!navigator.geolocation) {
      console.error('Geolocation not supported');
      setState(prev => ({
        ...prev,
        loading: false,
        error: 'Geolocation is not supported by your browser'
      }));
      return;
    }

    console.log('Requesting geolocation...');
    navigator.geolocation.getCurrentPosition(
      (position) => {
        console.log('Geolocation success:', position);
        const coords = {
          lat: position.coords.latitude,
          lng: position.coords.longitude
        };

        console.log('Detected coordinates:', coords);

        // Check if location is within delivery area
        const isWithinArea = isWithinDeliveryArea(coords);

        console.log('Is within service area:', isWithinArea);

        setState(prev => ({
          ...prev,
          coordinates: coords,
          loading: false,
          isWithinServiceArea: isWithinArea,
          error: null // Remove error even if outside service area for now
        }));

        // Get address from coordinates (non-blocking)
        reverseGeocode(coords).catch((err) => {
          console.error('Reverse geocoding failed:', err);
        });
      },
      (error) => {
        console.error('Geolocation error:', error);
        let errorMessage = 'Unable to get your location';

        switch (error.code) {
          case error.PERMISSION_DENIED:
            errorMessage = 'Location access denied. Please enable location permissions in your browser and try again.';
            break;
          case error.POSITION_UNAVAILABLE:
            errorMessage = 'Location information is unavailable. Please try again.';
            break;
          case error.TIMEOUT:
            errorMessage = 'Location request timed out. Please try again.';
            break;
          default:
            errorMessage = error.message || 'An unknown error occurred while getting your location.';
        }

        setState(prev => ({
          ...prev,
          loading: false,
          error: errorMessage
        }));
      },
      {
        enableHighAccuracy: true,
        timeout: 20000, // Increased timeout to 20 seconds
        maximumAge: 30000 // Reduced cache time to 30 seconds for more accurate location
      }
    );
  }, [reverseGeocode]);

  // Update state when geocoding completes
  useEffect(() => {
    if (address && !geocodeLoading && !geocodeError) {
      setState(prev => ({
        ...prev,
        formattedAddress: address
      }));
    }
  }, [address, geocodeLoading, geocodeError]);

  // Save location to user profile
  const saveLocation = useCallback(async () => {
    if (!state.coordinates || !state.formattedAddress) {
      return;
    }

    try {
      await updateUser({
        location: {
          coordinates: state.coordinates,
          formattedAddress: state.formattedAddress
        }
      });
      return true;
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to save location'
      }));
      return false;
    }
  }, [state.coordinates, state.formattedAddress, updateUser]);

  // Update location with new coordinates
  const updateLocation = useCallback(async (coords: Coordinates) => {
    console.log('updateLocation called with coords:', coords);

    // Check if location is within delivery area
    const isWithinArea = isWithinDeliveryArea(coords);

    console.log('updateLocation - Is within service area:', isWithinArea);

    setState(prev => ({
      ...prev,
      coordinates: coords,
      loading: true,
      isWithinServiceArea: isWithinArea,
      error: null // Remove error even if outside service area for now
    }));

    // Get address from coordinates (non-blocking)
    reverseGeocode(coords).then(() => {
      setState(prev => ({ ...prev, loading: false }));
    }).catch((err) => {
      console.error('Reverse geocoding failed:', err);
      setState(prev => ({ ...prev, loading: false }));
    });
  }, [reverseGeocode]);

  return {
    ...state,
    getCurrentLocation,
    saveLocation,
    updateLocation,
    geocodeLoading
  };
}
