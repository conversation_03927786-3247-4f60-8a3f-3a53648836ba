import './globals.css'
import { SearchProvider } from "@/context/SearchContext"
import { Metadata } from 'next'
import { QueryProvider } from '@/components/providers/QueryProvider'
import { Providers } from '@/components/Providers'
import { FontLoader } from '@/components/FontLoader'

// Use system fonts as fallback
const fontFamily = 'Inter, system-ui, sans-serif'

export const metadata: Metadata = {
  metadataBase: new URL('https://www.bandiwala.co.in'),
  title: {
    default: 'Bandiwala - Local Food Delivery',
    template: '%s | Bandiwala'
  },
  description: 'Order delicious food from local vendors and get it delivered to your doorstep',
  keywords: ['food delivery', 'local food', 'street food', 'home kitchen', 'delivery service'],
  icons: {
    icon: '/images/logo.png',
  },
  openGraph: {
    title: 'Bandiwala - Local Food Delivery',
    description: 'Order delicious food from local vendors and get it delivered to your doorstep',
    url: 'https://www.bandiwala.co.in',
    siteName: 'Bandiwala',
    type: 'website'
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: process.env.NEXT_PUBLIC_GOOGLE_SITE_VERIFICATION,
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <head>
        <link rel="icon" href="/images/logo.png" type="image/png" />
        {/* Google Analytics GA4 */}
        <script async src={`https://www.googletagmanager.com/gtag/js?id=${process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID}`}></script>
        <script dangerouslySetInnerHTML={{
          __html: `window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', '${process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID}');`
        }} />
        {/* Sitelinks Searchbox structured data */}
        <script type="application/ld+json" dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "WebSite",
            "url": "https://www.bandiwala.co.in/",
            "potentialAction": {
              "@type": "SearchAction",
              "target": "https://www.bandiwala.co.in/search?q={search_term_string}",
              "query-input": "required name=search_term_string"
            }
          })
        }} />
        {/* Breadcrumb structured data */}
        <script type="application/ld+json" dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "BreadcrumbList",
            "itemListElement": [
              {
                "@type": "ListItem",
                "position": 1,
                "name": "Home",
                "item": "https://www.bandiwala.co.in/"
              },
              {
                "@type": "ListItem",
                "position": 2,
                "name": "Dishes",
                "item": "https://www.bandiwala.co.in/dishes"
              },
              {
                "@type": "ListItem",
                "position": 3,
                "name": "Vendors",
                "item": "https://www.bandiwala.co.in/vendors"
              },
              {
                "@type": "ListItem",
                "position": 4,
                "name": "Cart",
                "item": "https://www.bandiwala.co.in/cart"
              },
              {
                "@type": "ListItem",
                "position": 5,
                "name": "About Us",
                "item": "https://www.bandiwala.co.in/about-us"
              },
              {
                "@type": "ListItem",
                "position": 6,
                "name": "Login",
                "item": "https://www.bandiwala.co.in/login"
              }
            ]
          })
        }} />
      </head>
      <body className="font-sans">
        <FontLoader />
        <QueryProvider>
          <Providers>
            <SearchProvider>
              {children}
            </SearchProvider>
          </Providers>
        </QueryProvider>
      </body>
    </html>
  )
}
