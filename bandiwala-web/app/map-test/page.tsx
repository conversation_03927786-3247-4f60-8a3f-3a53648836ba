'use client';

import React from 'react';
import VendorMap from '@/components/maps/VendorMap';
import { ArrowLeft } from 'lucide-react';
import Link from 'next/link';

export default function MapTestPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-4">
              <Link 
                href="/" 
                className="flex items-center gap-2 text-gray-600 hover:text-gray-900 transition-colors"
              >
                <ArrowLeft size={20} />
                <span>Back to Home</span>
              </Link>
              <div className="h-6 w-px bg-gray-300"></div>
              <h1 className="text-xl font-semibold text-gray-900">
                Vendor Map Test
              </h1>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="space-y-6">
          {/* Instructions */}
          <div className="bg-white rounded-lg shadow-sm border p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">
              Interactive Vendor Map
            </h2>
            <div className="space-y-3 text-sm text-gray-600">
              <p>
                <strong>How to use:</strong>
              </p>
              <ul className="list-disc list-inside space-y-1 ml-4">
                <li>Click on any orange vendor marker to see a popup with the shop image</li>
                <li>Click "View Details" in the popup to open the detailed vendor modal</li>
                <li>Use "View Menu" button in the modal to navigate to the vendor's page</li>
                <li>The map shows all 4 vendors within the delivery area</li>
              </ul>
            </div>
          </div>

          {/* Map Container */}
          <div className="bg-white rounded-lg shadow-sm border p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              Vendor Locations
            </h3>
            <div className="rounded-lg overflow-hidden">
              <VendorMap height="600px" zoom={16} />
            </div>
          </div>

          {/* Vendor List */}
          <div className="bg-white rounded-lg shadow-sm border p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              Available Vendors
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="p-4 border rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
                  <h4 className="font-medium">Jai Bhavani Chat Bhandar</h4>
                </div>
                <p className="text-sm text-gray-600">Rating: 4.5 ⭐</p>
                <p className="text-sm text-gray-600">Street Food & Chats</p>
              </div>
              
              <div className="p-4 border rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
                  <h4 className="font-medium">BFC Chicken Pakodi Center</h4>
                </div>
                <p className="text-sm text-gray-600">Rating: 4.3 ⭐</p>
                <p className="text-sm text-gray-600">Chicken & Fish</p>
              </div>
              
              <div className="p-4 border rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
                  <h4 className="font-medium">Rajahmundry vari Special</h4>
                </div>
                <p className="text-sm text-gray-600">Rating: 4.7 ⭐</p>
                <p className="text-sm text-gray-600">Bajjis & Muntha Masala</p>
              </div>
              
              <div className="p-4 border rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
                  <h4 className="font-medium">Sangamesh Bhavani Fast Food</h4>
                </div>
                <p className="text-sm text-gray-600">Rating: 4.4 ⭐</p>
                <p className="text-sm text-gray-600">Chinese & Fast Food</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
