'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';

export default function DebugEmailPage() {
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [testEmail, setTestEmail] = useState('<EMAIL>');

  const sendTestEmailToUser = async () => {
    setLoading(true);
    setError(null);
    setResult(null);

    try {
      // Get EmailJS configuration
      const serviceId = process.env.NEXT_PUBLIC_EMAILJS_SERVICE_ID;
      const templateId = process.env.NEXT_PUBLIC_EMAILJS_TEMPLATE_ID;
      const userId = process.env.NEXT_PUBLIC_EMAILJS_USER_ID;
      const adminEmail = process.env.NEXT_PUBLIC_EMAILJS_ADMIN_EMAIL || '<EMAIL>';

      console.log('EmailJS Config:', { serviceId, templateId, userId, adminEmail, testEmail });

      if (!serviceId || !templateId || !userId) {
        throw new Error('EmailJS configuration is missing');
      }

      // Import emailjs directly
      const emailjs = (await import('@emailjs/browser')).default;
      emailjs.init(userId);

      // Test data
      const testOrderId = 'DEBUG-' + Date.now();
      const coordinates = { lat: 17.3850, lng: 78.4867 };
      const mapUrl = `https://www.google.com/maps?q=${coordinates.lat},${coordinates.lng}`;

      const baseTemplateParams = {
        from_name: 'Bandiwala Order System',
        reply_to: '<EMAIL>',
        order_id: testOrderId,
        items_text: 'Dahi Papdi x2 — ₹90.00 (Jai Bhavani Chat Bhandar)\nPani Puri x1 — ₹25.00 (Jai Bhavani Chat Bhandar)',
        address: 'Test Address, Hyderabad, Telangana',
        coordinates: `${coordinates.lat}, ${coordinates.lng}`,
        phone_number: '+91 98765 43210',
        map_url: mapUrl,
        subtotal: '115.00',
        platform_fee: '5.00',
        delivery_charge: '20.00',
        taxes: '5.75',
        total: '145.75',
        shop_name: 'Jai Bhavani Chat Bhandar',
        shop_phone: '+91 98765 43210',
        customer_name: 'Test Customer',
        customer_email: testEmail,
        order_date: new Date().toLocaleDateString('en-IN', {
          year: 'numeric',
          month: 'long',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit'
        })
      };

      // Send email to admin first
      console.log('Sending admin email...');
      const adminTemplateParams = {
        ...baseTemplateParams,
        to_name: 'Bandiwala Admin',
        recipient: adminEmail,
        email: adminEmail,
        to_email: adminEmail,
      };

      const adminResponse = await emailjs.send(serviceId, templateId, adminTemplateParams);
      console.log('Admin email sent:', adminResponse);

      // Send email to user
      console.log('Sending user email...');
      const userTemplateParams = {
        ...baseTemplateParams,
        to_name: 'Test Customer',
        recipient: testEmail,
        email: testEmail,
        to_email: testEmail,
      };

      const userResponse = await emailjs.send(serviceId, templateId, userTemplateParams);
      console.log('User email sent:', userResponse);

      setResult({
        admin: adminResponse,
        user: userResponse,
        message: 'Both emails sent successfully!'
      });

    } catch (err) {
      console.error('Error sending test emails:', err);
      setError(err instanceof Error ? err.message : String(err));
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-4">Debug Email Sending</h1>

      <div className="mb-6">
        <p className="mb-4">This page helps debug email sending to both admin and user.</p>
        
        <div className="mb-4">
          <label className="block text-sm font-medium mb-2">Test User Email:</label>
          <input
            type="email"
            value={testEmail}
            onChange={(e) => setTestEmail(e.target.value)}
            className="w-full p-2 border border-gray-300 rounded-md"
            placeholder="Enter user email to test"
          />
        </div>

        <Button
          onClick={sendTestEmailToUser}
          disabled={loading || !testEmail}
          className="bg-bandiwala-orange hover:bg-orange-600 text-white"
        >
          {loading ? 'Sending...' : 'Send Test Emails (Admin + User)'}
        </Button>
      </div>

      {error && (
        <div className="p-4 mb-4 bg-red-100 border border-red-400 text-red-700 rounded">
          <h2 className="font-bold mb-2">Error:</h2>
          <p>{error}</p>
        </div>
      )}

      {result && (
        <div className="p-4 mb-4 bg-green-100 border border-green-400 text-green-700 rounded">
          <h2 className="font-bold mb-2">Success!</h2>
          <p>{result.message}</p>
          <div className="mt-2">
            <p><strong>Admin Email Status:</strong> {result.admin.status} - {result.admin.text}</p>
            <p><strong>User Email Status:</strong> {result.user.status} - {result.user.text}</p>
          </div>
        </div>
      )}

      <div className="mt-8 p-4 bg-gray-100 rounded">
        <h2 className="font-bold mb-2">EmailJS Configuration:</h2>
        <ul className="list-disc pl-5">
          <li>Service ID: {process.env.NEXT_PUBLIC_EMAILJS_SERVICE_ID ? '✅ Set' : '❌ Missing'}</li>
          <li>Template ID: {process.env.NEXT_PUBLIC_EMAILJS_TEMPLATE_ID ? '✅ Set' : '❌ Missing'}</li>
          <li>User ID: {process.env.NEXT_PUBLIC_EMAILJS_USER_ID ? '✅ Set' : '❌ Missing'}</li>
          <li>Admin Email: {process.env.NEXT_PUBLIC_EMAILJS_ADMIN_EMAIL || '<EMAIL>'}</li>
        </ul>
      </div>

      <div className="mt-8 p-4 bg-blue-100 rounded">
        <h2 className="font-bold mb-2">Troubleshooting Steps:</h2>
        <ol className="list-decimal pl-5 space-y-2">
          <li>Check if emails are going to spam folder</li>
          <li>Verify the user email address is correct</li>
          <li>Check EmailJS dashboard for delivery status</li>
          <li>Ensure EmailJS template variables are correctly mapped</li>
          <li>Check browser console for any errors</li>
        </ol>
      </div>
    </div>
  );
}
