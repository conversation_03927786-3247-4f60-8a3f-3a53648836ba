"use client";
import React, { useState } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { useAuth } from "@/contexts/AuthContext";
import { authService } from "@/services/api";
import { BackButton } from "@/components/ui/back-button";

export default function LoginPage() {
  const router = useRouter();
  const { login } = useAuth();

  const [loginMethod, setLoginMethod] = useState<'email' | 'phone'>('email');
  const [formData, setFormData] = useState({
    email: "",
    phone: "",
    password: "",
  });

  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [successMessage, setSuccessMessage] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [showRegisterPrompt, setShowRegisterPrompt] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;

    // Handle phone number input with automatic +91 prefix
    if (name === 'phone') {
      // Remove any non-digit characters except +
      let cleanValue = value.replace(/[^\d+]/g, '');

      // If user tries to enter +91, just keep the digits after it
      if (cleanValue.startsWith('+91')) {
        cleanValue = cleanValue.substring(3);
      } else if (cleanValue.startsWith('91')) {
        cleanValue = cleanValue.substring(2);
      } else if (cleanValue.startsWith('+')) {
        cleanValue = cleanValue.substring(1);
      }

      // Limit to 10 digits
      cleanValue = cleanValue.substring(0, 10);

      setFormData((prev) => ({
        ...prev,
        [name]: cleanValue,
      }));
    } else {
      setFormData((prev) => ({
        ...prev,
        [name]: value,
      }));
    }
  };

  // Clear form data when switching login methods
  const handleLoginMethodChange = (method: 'email' | 'phone') => {
    setLoginMethod(method);
    setFormData(prev => ({
      ...prev,
      email: "",
      phone: "",
    }));
    setError("");
    setShowRegisterPrompt(false);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError("");
    setSuccessMessage("");
    setShowRegisterPrompt(false);

    try {
      // Validate form
      const currentField = loginMethod === 'email' ? formData.email : formData.phone;
      if (!currentField || !formData.password) {
        const fieldName = loginMethod === 'email' ? 'Email' : 'Phone number';
        throw new Error(`${fieldName} and password are required`);
      }

      // Validate phone number format if using phone login
      if (loginMethod === 'phone') {
        if (formData.phone.length !== 10 || !/^\d{10}$/.test(formData.phone)) {
          throw new Error("Please enter a valid 10-digit phone number");
        }
      }

      try {
        // First try the backend API
        console.log(`Login page: Trying to login via backend API with ${loginMethod}`);
        const credentials: any = { password: formData.password };
        if (loginMethod === 'email') {
          credentials.email = formData.email;
        } else {
          credentials.phone = `+91${formData.phone}`;
        }

        const response = await authService.login(credentials);

        if (response.success) {
          // Check user role before proceeding with login
          if (response.user && response.user.role && response.user.role !== "user") {
            throw new Error("Access denied: This application is only for regular users");
          }

          setSuccessMessage("Login successful!");

          // Use the token and user data from the response
          if (response.token) {
            await login(response.token, response.user);

            // Redirect to home page after successful login
            setTimeout(() => {
              router.push("/");
            }, 1500);
            return;
          } else {
            throw new Error("No token received from server");
          }
        } else {
          // Check if this is a registration prompt
          if (response.needsRegistration) {
            setShowRegisterPrompt(true);
          }
          throw new Error(response.message || "Login failed");
        }
      } catch (backendError: any) {
        console.error("Login page: Error logging in via backend, trying fallback:", backendError);

        // Check if the backend error indicates need for registration
        if (backendError.response?.status === 404 || backendError.message?.includes("register")) {
          setShowRegisterPrompt(true);
          throw backendError;
        }

        // If backend fails, try our fallback API
        try {
          console.log("Login page: Trying fallback API at /api/auth/login");
          const fallbackCredentials: any = { password: formData.password };
          if (loginMethod === 'email') {
            fallbackCredentials.email = formData.email;
          } else {
            fallbackCredentials.phone = `+91${formData.phone}`;
          }

          const response = await fetch('/api/auth/login', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(fallbackCredentials)
          });

          const data = await response.json();
          console.log("Login page: Login response from fallback API:", data);

          if (!response.ok) {
            // Check if this is a registration prompt from fallback API
            if (response.status === 404 && data.needsRegistration) {
              setShowRegisterPrompt(true);
            }
            throw new Error(data.message || `Fallback API returned status ${response.status}`);
          }

          if (data.success && data.token) {
            // Check user role before proceeding with login
            if (data.user && data.user.role && data.user.role !== "user") {
              throw new Error("Access denied: This application is only for regular users");
            }

            setSuccessMessage("Login successful!");

            // Use the token and user data from the response
            await login(data.token, data.user);

            // Redirect to home page after successful login
            setTimeout(() => {
              router.push("/");
            }, 1500);
            return;
          } else {
            throw new Error(data.message || "Login failed via fallback API");
          }
        } catch (fallbackError) {
          console.error("Login page: Fallback API also failed:", fallbackError);
          throw fallbackError;
        }
      }
    } catch (err: any) {
      console.error("Login error:", err);
      setError(err.message || "Failed to login. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-[80vh] px-4">
      <div className="container mx-auto max-w-md pt-8">
        <div className="mb-6">
          <BackButton text="Back to Home" href="/" />
        </div>
        <div className="w-full bg-white rounded-lg shadow-md p-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-2">Sign In</h1>
          <p className="text-gray-500 mb-6">Welcome to Bandiwala</p>

          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
              {error}
              {showRegisterPrompt && (
                <div className="mt-3 pt-3 border-t border-red-300">
                  <Link
                    href="/register"
                    className="inline-block bg-red-600 text-white px-4 py-2 rounded text-sm hover:bg-red-700 transition-colors"
                  >
                    Register Now
                  </Link>
                </div>
              )}
            </div>
          )}

          {successMessage && (
            <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
              {successMessage}
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-4 text-left">
            {/* Login Method Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Login with
              </label>
              <div className="flex space-x-4">
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="loginMethod"
                    value="email"
                    checked={loginMethod === 'email'}
                    onChange={(e) => handleLoginMethodChange(e.target.value as 'email' | 'phone')}
                    className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300"
                  />
                  <span className="ml-2 text-sm text-gray-700">Email</span>
                </label>
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="loginMethod"
                    value="phone"
                    checked={loginMethod === 'phone'}
                    onChange={(e) => handleLoginMethodChange(e.target.value as 'email' | 'phone')}
                    className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300"
                  />
                  <span className="ml-2 text-sm text-gray-700">Phone Number</span>
                </label>
              </div>
            </div>

            {/* Email or Phone Input */}
            {loginMethod === 'email' ? (
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                  Email
                </label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                  placeholder="Enter your email"
                  required
                />
              </div>
            ) : (
              <div>
                <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">
                  Phone Number
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <span className="text-gray-500 text-sm">+91</span>
                  </div>
                  <input
                    type="tel"
                    id="phone"
                    name="phone"
                    value={formData.phone}
                    onChange={handleChange}
                    className="w-full pl-12 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                    placeholder="9876543210"
                    maxLength={10}
                    required
                  />
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  Enter 10-digit phone number (e.g., 9876543210)
                </p>
              </div>
            )}

            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
                Password
              </label>
              <div className="relative">
                <input
                  type={showPassword ? "text" : "password"}
                  id="password"
                  name="password"
                  value={formData.password}
                  onChange={handleChange}
                  className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                  placeholder="Enter your password"
                  required
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
                >
                  {showPassword ? (
                    <svg
                      className="h-5 w-5"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"
                      />
                    </svg>
                  ) : (
                    <svg
                      className="h-5 w-5"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                      />
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                      />
                    </svg>
                  )}
                </button>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <input
                  id="remember-me"
                  name="remember-me"
                  type="checkbox"
                  className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                />
                <label htmlFor="remember-me" className="ml-2 block text-sm text-gray-700">
                  Remember me
                </label>
              </div>
              <div className="text-sm">
                <Link href="/forgot-password" className="text-green-600 hover:text-green-500">
                  Forgot password?
                </Link>
              </div>
            </div>

            <button
              type="submit"
              disabled={isLoading}
              className="w-full bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? (
                <span className="flex items-center justify-center">
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Signing in...
                </span>
              ) : (
                "Sign In"
              )}
            </button>
          </form>

          <div className="mt-6 text-center">
            <p className="text-sm text-gray-600">
              Don't have an account?{" "}
              <Link href="/register" className="text-green-600 hover:text-green-500">
                Sign up
              </Link>
            </p>
          </div>
        </div>
      </div>
      </div>
    </div>
  );
}