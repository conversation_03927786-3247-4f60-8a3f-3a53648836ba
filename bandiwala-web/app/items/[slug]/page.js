import ItemDetailClient from './item-detail-client';

export default async function ItemDetail({ params }) {
  // Await the params to get the slug
  const { slug } = await params;

  return <ItemDetailClient slug={slug} />;
}

// Generate metadata for SEO
export async function generateMetadata({ params }) {
  const { slug } = await params;

  return {
    title: `${slug.replace(/-/g, ' ')} | Bandiwala`,
    description: `Order delicious ${slug.replace(/-/g, ' ')} from Bandiwala`,
  };
}
