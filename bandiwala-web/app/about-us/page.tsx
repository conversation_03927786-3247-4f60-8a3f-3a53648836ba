import Footer from "@/components/Footer";
import Header from "@/components/Header";
import {CircleCheck} from "lucide-react";

export default function AboutUs() {
    return (
        <div>
            <Header/>
      <div className="bg-white text-gray-800 px-6 py-12 md:px-20 lg:px-32">
        {/* Header Section */}
        <section className="text-center mb-12">
          <h1 className="text-4xl font-bold mb-4 text-bandiwala-orange">About Us</h1>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            Redefining Street Food, One Bandi at a Time
          </p>
        </section>
  
        {/* Who We Are */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-3 text-gray-800">Who We Are</h2>
          <p className="text-gray-700 leading-relaxed">
            At <strong>Bandi Wala</strong>, we’re a passionate team of foodies and techies committed to reviving the street food culture of India. We empower local food vendors and home chefs by giving them a digital platform to grow their business while offering customers affordable, hygienic, and flavorful meals.
          </p>
        </section>
  
        {/* What We Do */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-3 text-gray-800">What We Do</h2>
          <p className="text-gray-700 leading-relaxed mb-4">
            We connect food lovers with authentic street food vendors and home-based cooks through a seamless WhatsApp-based ordering system. Whether you're craving spicy chaat or a wholesome home-cooked meal, Bandi Wala delivers it straight to your door—fast, affordable, and with quality assurance.
          </p>
        </section>
  
        {/* Why Choose Us */}
        <section className="mb-12">
          <h2 className="text-2xl font-semibold mb-5 text-gray-800">Why Bandi Wala?</h2>
          <ul className="grid grid-cols-1 md:grid-cols-2 gap-4 text-gray-700">
            {[
              "Affordable & Accessible – no inflated pricing.",
              "Hygiene-Focused – vendors are verified and quality-checked.",
              "Empowering Local Economy – support for small vendors.",
              "Hyperlocal Discovery – find stalls near you easily.",
              "Sustainable Growth – community-driven impact.",
            ].map((item, index) => (
              <li key={index} className="flex items-start gap-2">
                <CircleCheck className="text-bandiwala-orange" />
                <span>{item}</span>
              </li>
            ))}
          </ul>
        </section>
  
        {/* Vision and Mission */}
        <section className="grid md:grid-cols-2 gap-8 mb-12">
          <div>
            <h2 className="text-2xl font-semibold mb-3 text-gray-800">Our Vision</h2>
            <p className="text-gray-700 leading-relaxed">
              To become India’s most trusted platform for discovering and enjoying local food while empowering the vendors who make it possible.
            </p>
          </div>
          <div>
            <h2 className="text-2xl font-semibold mb-3 text-gray-800">Our Mission</h2>
            <p className="text-gray-700 leading-relaxed">
              To build an inclusive, affordable, and tech-driven food ecosystem that celebrates local communities and brings authentic flavors to every home.
            </p>
          </div>
        </section>
  
        {/* CTA */}
        <section className="text-center mt-16">
          <h3 className="text-xl font-semibold mb-2 text-gray-800">
            Hungry for real flavors?
          </h3>
          <p className="mb-6 text-gray-600">Join us on WhatsApp and explore your local food scene like never before!</p>
          <a
            href="https://wa.me/your-number-here"
            className="inline-block bg-bandiwala-red text-white px-6 py-3 rounded-xl text-lg hover:bg-bandiwala-orange transition"
          >
            Chat with us on WhatsApp
          </a>
        </section>
      </div>
      <Footer/>
      </div>
    );
  }