'use client';

import { useState, useEffect, useCallback } from 'react';
import { vendorService, menuItemService, cartService, promoCodeService } from '@/services/api';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
// import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { CartPopup } from '@/components/ui/cart-popup';
import { Trash2 } from 'lucide-react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { Cart, CartItem } from '@/types/cart';

interface TestResult {
  endpoint: string;
  method: string;
  status: 'success' | 'error';
  data?: Record<string, unknown>;
  error?: string;
}

export default function ApiTestPage() {
  const [results, setResults] = useState<TestResult[]>([]);
  const [loading, setLoading] = useState(false);

  // Define proper types for vendors and menu items
  interface Vendor {
    _id: string;
    name: string;
    slug: string;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    [key: string]: any;
  }

  interface MenuItem {
    _id: string;
    name: string;
    price: number;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    [key: string]: any;
  }

  const [vendors, setVendors] = useState<Vendor[]>([]);
  const [menuItems, setMenuItems] = useState<MenuItem[]>([]);

  // Using Cart and CartItem types from types/cart.ts

  const [cart, setCart] = useState<Cart | null>(null);
  const [promoCode, setPromoCode] = useState('WELCOME20');
  const [subtotal, setSubtotal] = useState(500);
  const [selectedVendor, setSelectedVendor] = useState<string>('');
  const [selectedMenuItem, setSelectedMenuItem] = useState<string>('');
  const [showClearCartPopup, setShowClearCartPopup] = useState(false);

  // Helper function to add a test result - wrapped in useCallback to avoid dependency issues
  const addResult = useCallback((result: TestResult) => {
    setResults(prev => [result, ...prev]);
  }, []);

  // Helper function to run a test
  const runTest = useCallback(async <T extends Record<string, any>>(
    name: string,
    method: string,
    fn: () => Promise<T>
  ) => {
    try {
      const data = await fn();
      addResult({
        endpoint: name,
        method,
        status: 'success',
        data: data as unknown as Record<string, unknown>,
      });
      return data;
    } catch (error) {
      addResult({
        endpoint: name,
        method,
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      return null;
    }
  }, [addResult]);

  // Test all routes
  const testAllRoutes = async () => {
    setLoading(true);
    setResults([]);

    try {
      // Test vendor routes
      await testVendorRoutes();

      // Test menu item routes
      await testMenuItemRoutes();

      // Test cart routes
      await testCartRoutes();

      // Test promo code routes
      await testPromoCodeRoutes();

      toast.success('Tests completed', {
        description: 'All API tests have been completed.',
      });
    } catch (error) {
      console.error('Error running tests:', error);
      toast.error('Test error', {
        description: 'An error occurred while running the tests.',
      });
    } finally {
      setLoading(false);
    }
  };

  // Test vendor routes
  const testVendorRoutes = async () => {
    // Get all vendors
    const vendorsData = await runTest('Get all vendors', 'GET', async () => {
      const response = await vendorService.getAllVendors();
      if (response.success) {
        setVendors(response.data);
        if (response.data.length > 0) {
          setSelectedVendor(response.data[0]._id);
        }
      }
      return response;
    });

    // Get vendor by slug
    if (vendorsData?.success && Array.isArray(vendorsData.data) && vendorsData.data.length > 0) {
      const vendor = vendorsData.data[0];
      await runTest(`Get vendor by slug: ${vendor.slug}`, 'GET', async () => {
        return await vendorService.getVendorBySlug(vendor.slug);
      });
    }
  };

  // Test menu item routes
  const testMenuItemRoutes = async () => {
    // Get all menu items
    const menuItemsData = await runTest('Get all menu items', 'GET', async () => {
      const response = await menuItemService.getAllMenuItems();
      if (response.success) {
        setMenuItems(response.data);
        if (response.data.length > 0) {
          setSelectedMenuItem(response.data[0]._id);
        }
      }
      return response;
    });

    // Get menu item by ID
    if (menuItemsData?.success && Array.isArray(menuItemsData.data) && menuItemsData.data.length > 0) {
      const menuItem = menuItemsData.data[0];
      await runTest(`Get menu item by ID: ${menuItem._id}`, 'GET', async () => {
        return await menuItemService.getMenuItemById(menuItem._id);
      });
    }

    // Get menu items by vendor
    if (selectedVendor) {
      await runTest(`Get menu items by vendor: ${selectedVendor}`, 'GET', async () => {
        return await menuItemService.getMenuItemsByVendor(selectedVendor);
      });
    }
  };

  // Test cart routes
  const testCartRoutes = async () => {
    // Get cart
    await runTest('Get cart', 'GET', async () => {
      const response = await cartService.getCart();
      if (response.success) {
        setCart(response.data);
      }
      return response;
    });

    // Add to cart
    if (selectedMenuItem) {
      await runTest(`Add to cart: ${selectedMenuItem}`, 'POST', async () => {
        return await cartService.addToCart(selectedMenuItem, 2, 'Test note');
      });
    }

    // Update cart item
    if (selectedMenuItem) {
      await runTest(`Update cart item: ${selectedMenuItem}`, 'PUT', async () => {
        return await cartService.updateCartItem(selectedMenuItem, 3, 'Updated test note');
      });
    }

    // Remove from cart
    if (selectedMenuItem) {
      await runTest(`Remove from cart: ${selectedMenuItem}`, 'DELETE', async () => {
        return await cartService.removeFromCart(selectedMenuItem);
      });
    }

    // Clear cart
    await runTest('Clear cart', 'DELETE', async () => {
      toast.success('Clearing cart', {
        description: 'cart cleared',
        duration: 3000
      });
      return await cartService.clearCart();
    });
  };

  // Test promo code routes
  const testPromoCodeRoutes = async () => {
    // Validate promo code
    await runTest(`Validate promo code: ${promoCode}`, 'POST', async () => {
      return await promoCodeService.validatePromoCode(promoCode, subtotal);
    });

    // Invalid promo code
    await runTest('Validate invalid promo code', 'POST', async () => {
      return await promoCodeService.validatePromoCode('INVALID', subtotal);
    });
  };

  // Individual test functions
  const testGetVendors = useCallback(async () => {
    await runTest('Get all vendors', 'GET', async () => {
      const response = await vendorService.getAllVendors();
      if (response.success) {
        setVendors(response.data);
        if (response.data.length > 0) {
          setSelectedVendor(response.data[0]._id);
        }
      }
      return response;
    });
  }, [runTest, setVendors, setSelectedVendor]);

  const testGetMenuItems = useCallback(async () => {
    await runTest('Get all menu items', 'GET', async () => {
      const response = await menuItemService.getAllMenuItems();
      if (response.success) {
        setMenuItems(response.data);
        if (response.data.length > 0) {
          setSelectedMenuItem(response.data[0]._id);
        }
      }
      return response;
    });
  }, [runTest, setMenuItems, setSelectedMenuItem]);

  const testAddToCart = async () => {
    if (selectedMenuItem) {
      await runTest(`Add to cart: ${selectedMenuItem}`, 'POST', async () => {
        return await cartService.addToCart(selectedMenuItem, 1);
      });
    } else {
      toast.error('Error', {
        description: 'No menu item selected',
      });
    }
  };

  const testGetCart = useCallback(async () => {
    await runTest('Get cart', 'GET', async () => {
      const response = await cartService.getCart();
      if (response.success) {
        setCart(response.data);
      }
      return response;
    });
  }, [runTest, setCart]);

  const testClearCart = async () => {
    console.log('testClearCart called');
    await runTest('Clear cart', 'DELETE', async () => {
      console.log('Setting popup to true');
      // Show the custom popup
      setShowClearCartPopup(true);

      // Auto-close the popup after 3 seconds
      setTimeout(() => {
        console.log('Auto-closing popup');
        setShowClearCartPopup(false);
      }, 3000);

      return await cartService.clearCart();
    });
  };

  const testValidatePromoCode = async () => {
    await runTest(`Validate promo code: ${promoCode}`, 'POST', async () => {
      return await promoCodeService.validatePromoCode(promoCode, subtotal);
    });
  };

  // Load initial data
  useEffect(() => {
    const loadInitialData = async () => {
      await testGetVendors();
      await testGetMenuItems();
      await testGetCart();
    };

    loadInitialData();
  }, [testGetVendors, testGetMenuItems, testGetCart]);

  return (
    <div className="min-h-screen flex flex-col">
      <Header />

      <main className="flex-1 container mx-auto py-8 px-4">
        <h1 className="text-3xl font-bold mb-6">API Testing Dashboard</h1>
        <p className="text-gray-600 mb-8">
          Use this page to test all API routes in the Bandiwala application.
        </p>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <Card>
            <CardHeader>
              <CardTitle>Vendors</CardTitle>
              <CardDescription>Test vendor-related API routes</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <Label>Selected Vendor</Label>
                  <select
                    className="w-full p-2 border rounded mt-1"
                    value={selectedVendor}
                    onChange={(e) => setSelectedVendor(e.target.value)}
                  >
                    {vendors.map(vendor => (
                      <option key={vendor._id} value={vendor._id}>
                        {vendor.name}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button onClick={testGetVendors} disabled={loading}>
                Get All Vendors
              </Button>
              <Button
                onClick={async () => {
                  if (selectedVendor) {
                    const vendor = vendors.find(v => v._id === selectedVendor);
                    if (vendor) {
                      await runTest(`Get vendor by slug: ${vendor.slug}`, 'GET', async () => {
                        return await vendorService.getVendorBySlug(vendor.slug);
                      });
                    }
                  }
                }}
                disabled={loading || !selectedVendor}
              >
                Get By Slug
              </Button>
            </CardFooter>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Menu Items</CardTitle>
              <CardDescription>Test menu item API routes</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <Label>Selected Menu Item</Label>
                  <select
                    className="w-full p-2 border rounded mt-1"
                    value={selectedMenuItem}
                    onChange={(e) => setSelectedMenuItem(e.target.value)}
                  >
                    {menuItems.map(item => (
                      <option key={item._id} value={item._id}>
                        {item.name} (₹{item.price})
                      </option>
                    ))}
                  </select>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button onClick={testGetMenuItems} disabled={loading}>
                Get All Items
              </Button>
              <Button
                onClick={async () => {
                  if (selectedVendor) {
                    await runTest(`Get items by vendor: ${selectedVendor}`, 'GET', async () => {
                      return await menuItemService.getMenuItemsByVendor(selectedVendor);
                    });
                  }
                }}
                disabled={loading || !selectedVendor}
              >
                By Vendor
              </Button>
            </CardFooter>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Cart</CardTitle>
              <CardDescription>Test cart API routes</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <Label>Cart Items: {cart?.items?.length || 0}</Label>
                  <div className="mt-2 text-sm">
                    {cart?.items?.map((item: CartItem, index: number) => (
                      <div key={index} className="flex justify-between mb-1">
                        <span>{item.name}</span>
                        <span>x{item.quantity}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button onClick={testAddToCart} disabled={loading || !selectedMenuItem}>
                Add Item
              </Button>
              <Button onClick={testGetCart} disabled={loading}>
                Get Cart
              </Button>
              <Button onClick={testClearCart} disabled={loading}>
                Clear
              </Button>
              <Button
                onClick={() => {
                  console.log('Test popup button clicked');
                  setShowClearCartPopup(true);
                  setTimeout(() => setShowClearCartPopup(false), 3000);
                }}
                variant="outline"
                size="sm"
              >
                Test Popup
              </Button>
            </CardFooter>
          </Card>
        </div>

        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Promo Codes</CardTitle>
            <CardDescription>Test promo code API routes</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="promoCode">Promo Code</Label>
                <Input
                  id="promoCode"
                  value={promoCode}
                  onChange={(e) => setPromoCode(e.target.value)}
                  className="mt-1"
                />
              </div>
              <div>
                <Label htmlFor="subtotal">Subtotal</Label>
                <Input
                  id="subtotal"
                  type="number"
                  value={subtotal}
                  onChange={(e) => setSubtotal(Number(e.target.value))}
                  className="mt-1"
                />
              </div>
            </div>
          </CardContent>
          <CardFooter>
            <Button onClick={testValidatePromoCode} disabled={loading}>
              Validate Promo Code
            </Button>
          </CardFooter>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Run All Tests</CardTitle>
            <CardDescription>Test all API routes at once</CardDescription>
          </CardHeader>
          <CardContent>
            <Button
              onClick={testAllRoutes}
              disabled={loading}
              className="w-full"
            >
              {loading ? 'Running Tests...' : 'Run All Tests'}
            </Button>
          </CardContent>
        </Card>

        <div className="mt-8">
          <h2 className="text-2xl font-bold mb-4">Test Results</h2>
          <ScrollArea className="h-[400px] border rounded-md p-4">
            {results.length === 0 ? (
              <p className="text-gray-500 text-center py-8">No test results yet. Run some tests to see results here.</p>
            ) : (
              <div className="space-y-4">
                {results.map((result, index) => (
                  <div key={index} className="border rounded-md p-4">
                    <div className="flex justify-between items-center mb-2">
                      <div>
                        <span className="font-semibold">{result.method}</span>
                        <span className="ml-2 text-gray-600">{result.endpoint}</span>
                      </div>
                      <Badge variant={result.status === 'success' ? 'default' : 'destructive'}>
                        {result.status}
                      </Badge>
                    </div>
                    {result.error && (
                      <div className="text-red-500 text-sm mt-2">
                        Error: {result.error}
                      </div>
                    )}
                    {result.data && (
                      <div className="mt-2">
                        <Separator className="my-2" />
                        <details>
                          <summary className="cursor-pointer text-sm text-gray-600">
                            View Response Data
                          </summary>
                          <pre className="text-xs mt-2 bg-gray-100 p-2 rounded overflow-auto max-h-[200px]">
                            {JSON.stringify(result.data, null, 2)}
                          </pre>
                        </details>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </ScrollArea>
        </div>
      </main>

      <Footer />

      {/* Cart Clear Popup */}
      <CartPopup
        isOpen={showClearCartPopup}
        onClose={() => setShowClearCartPopup(false)}
        title="Cart cleared"
        description="Your cart has been successfully cleared"
        icon={<Trash2 size={20} className="text-red-500" />}
      />
    </div>
  );
}
