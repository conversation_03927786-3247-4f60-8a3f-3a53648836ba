"use client";
import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { useAuth } from "@/contexts/AuthContext";
import { authService } from "@/services/api";

export default function VerifyOTPPage() {
  const router = useRouter();
  const { login } = useAuth();

  const [formData, setFormData] = useState({
    email: "",
    phone: "",
    otp: "",
  });

  const [otpDigits, setOtpDigits] = useState(["", "", "", "", "",""]);

  const [isLoading, setIsLoading] = useState(false);
const [error, setError] = useState("");
  const [successMessage, setSuccessMessage] = useState("");
  const [timeLeft, setTimeLeft] = useState(600); // 10 minutes in seconds
  const [resendDisabled, setResendDisabled] = useState(true);

  useEffect(() => {
    // Get email and phone from session storage
    if (typeof window !== 'undefined') {
      const email = sessionStorage.getItem("verificationEmail") || "";
      const phone = sessionStorage.getItem("verificationPhone") || "";

      if (!email || !phone) {
        setError("Missing verification details. Please register again.");
        return;
      }

      setFormData(prev => ({
        ...prev,
        email,
        phone
      }));
    }

    // Start countdown timer
    const timer = setInterval(() => {
      setTimeLeft(prev => {
        if (prev <= 1) {
          clearInterval(timer);
          setResendDisabled(false);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    // Enable resend after 2 minutes
    const resendTimer = setTimeout(() => {
      setResendDisabled(false);
    }, 120000); // 2 minutes

    return () => {
      clearInterval(timer);
      clearTimeout(resendTimer);
    };
  }, []);

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs < 10 ? '0' : ''}${secs}`;
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleOtpDigitChange = (index, value) => {
    // Only allow single digit
    if (value.length > 1) return;

    // Only allow numbers
    if (value && !/^\d$/.test(value)) return;

    const newOtpDigits = [...otpDigits];
    newOtpDigits[index] = value;
    setOtpDigits(newOtpDigits);

    // Update the main OTP field
    const otpString = newOtpDigits.join("");
    setFormData(prev => ({
      ...prev,
      otp: otpString
    }));

    // Auto-focus next input
    if (value && index < 5) {
      const nextInput = document.getElementById(`otp-${index + 1}`);
      if (nextInput) nextInput.focus();
    }
  };

  const handleOtpKeyDown = (index, e) => {
    // Handle backspace to move to previous input
    if (e.key === 'Backspace' && !otpDigits[index] && index > 0) {
      const prevInput = document.getElementById(`otp-${index - 1}`);
      if (prevInput) prevInput.focus();
    }
  };

  const handleOtpPaste = (e) => {
    e.preventDefault();
    const pastedData = e.clipboardData.getData('text');
    const digits = pastedData.replace(/\D/g, '').slice(0, 6).split('');

    const newOtpDigits = [...otpDigits];
    digits.forEach((digit, index) => {
      if (index < 5) newOtpDigits[index] = digit;
    });

    // Fill remaining with empty strings
    for (let i = digits.length; i < 6; i++) {
      newOtpDigits[i] = '';
    }

    setOtpDigits(newOtpDigits);
    setFormData(prev => ({
      ...prev,
      otp: newOtpDigits.join("")
    }));

    // Focus the next empty input or the last input
    const nextEmptyIndex = newOtpDigits.findIndex(digit => digit === '');
    const focusIndex = nextEmptyIndex !== -1 ? nextEmptyIndex : 4;
    const inputToFocus = document.getElementById(`otp-${focusIndex}`);
    if (inputToFocus) inputToFocus.focus();
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    setError("");
    setSuccessMessage("");

    try {
      // Validate form
      if (!formData.otp) {
        throw new Error("OTP is required");
      }

      // Call verify OTP API
      const response = await authService.verifyOTP({
        email: formData.email,
        phone: formData.phone,
        otp: formData.otp,
      });

      if (response.success) {
        setSuccessMessage("OTP verified successfully!");

        // Use the token from the response
        if (response.token) {
          await login(response.token);

          // Clear verification data from session storage
          if (typeof window !== 'undefined') {
            sessionStorage.removeItem("verificationEmail");
            sessionStorage.removeItem("verificationPhone");
          }

          // Redirect to home page after successful verification
          setTimeout(() => {
            router.push("/");
          }, 1500);
        } else {
          throw new Error("No token received from server");
        }
      } else {
        throw new Error(response.message || "OTP verification failed");
      }
    } catch (err) {
      console.error("OTP verification error:", err);
      setError(err.message || "Failed to verify OTP. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendOTP = async () => {
    setIsLoading(true);
    setError("");
    setSuccessMessage("");

    try {
      // Use the dedicated resend OTP endpoint
      const response = await authService.resendOTP({
        email: formData.email,
        phone: formData.phone,
        verificationMethod: "phone", // Default to phone, could be made dynamic
      });

      if (response.success) {
        setSuccessMessage(response.message || "OTP resent successfully!");

        // Clear OTP inputs
        setOtpDigits(["", "", "", "", "",""]);
        setFormData(prev => ({ ...prev, otp: "" }));

        // Reset timer
        setTimeLeft(600);
        setResendDisabled(true);

        // Enable resend after 2 minutes
        setTimeout(() => {
          setResendDisabled(false);
        }, 120000);

        // Focus first OTP input
        setTimeout(() => {
          const firstInput = document.getElementById('otp-0');
          if (firstInput) firstInput.focus();
        }, 100);
      } else {
        throw new Error(response.message || "Failed to resend OTP");
      }
    } catch (err) {
      console.error("Resend OTP error:", err);
      setError(err.message || "Failed to resend OTP. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex items-center justify-center min-h-[80vh] px-4">
      <div className="w-full max-w-md bg-white rounded-lg shadow-md p-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-2">Verify OTP</h1>
          <p className="text-gray-500 mb-6">
            Enter the verification code sent to your {formData.email ? `email (${formData.email})` : 'phone'}
          </p>

          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
              {error}
            </div>
          )}

          {successMessage && (
            <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
              {successMessage}
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-4 text-left">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3 text-center">
                Verification Code
              </label>
              <div className="flex justify-center gap-3 mb-3">
                {otpDigits.map((digit, index) => (
                  <input
                    key={index}
                    id={`otp-${index}`}
                    type="text"
                    value={digit}
                    onChange={(e) => handleOtpDigitChange(index, e.target.value)}
                    onKeyDown={(e) => handleOtpKeyDown(index, e)}
                    onPaste={index === 0 ? handleOtpPaste : undefined}
                    className="w-12 h-12 text-center text-lg font-semibold border-2 border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
                    maxLength={1}
                    required
                  />
                ))}
              </div>
              <p className="text-xs text-gray-500 mt-1 text-center">
                Time remaining: {formatTime(timeLeft)}
              </p>
            </div>

            <button
              type="submit"
              disabled={isLoading}
              className="w-full bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? (
                <span className="flex items-center justify-center">
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Verifying...
                </span>
              ) : (
                "Verify OTP"
              )}
            </button>
          </form>

          <div className="mt-6 text-center">
            <button
              onClick={handleResendOTP}
              disabled={isLoading || resendDisabled}
              className="text-green-600 hover:text-green-500 disabled:text-gray-400 disabled:cursor-not-allowed"
            >
              Resend OTP
            </button>
          </div>

          <div className="mt-4 text-center">
            <p className="text-sm text-gray-600">
              <Link href="/login" className="text-green-600 hover:text-green-500">
                Back to Login
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
