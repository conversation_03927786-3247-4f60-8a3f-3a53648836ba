'use client';

import { useState } from 'react';
import { isWithinDeliveryArea, getDistanceFromServiceCenter, SERVICE_AREA_CENTER, SERVICE_AREA_RADIUS_KM } from '@/utils/distance';
import DeliveryAreaMap from '../../components/maps/DeliveryAreaMap';

export default function TestDeliveryAreaPage() {
  const [testCoords, setTestCoords] = useState({ lat: 17.49328, lng: 78.39433 });
  const [result, setResult] = useState<{ isWithin: boolean; distance: number } | null>(null);

  const testLocations = [
    { name: 'Service Center (Should be within)', coords: { lat: 17.49328, lng: 78.39433 } },
    { name: 'Very close (0.5km away)', coords: { lat: 17.49828, lng: 78.39433 } },
    { name: 'Edge of area (1km away)', coords: { lat: 17.50228, lng: 78.39433 } },
    { name: 'Just outside (1.1km away)', coords: { lat: 17.50328, lng: 78.39433 } },
    { name: 'Far outside (5km away)', coords: { lat: 17.54328, lng: 78.39433 } },
    { name: 'VNR Hostel', coords: { lat: 17.538779, lng: 78.395689 } },
    { name: 'Kukatpally', coords: { lat: 17.4933, lng: 78.3915 } },
    { name: 'Hyderabad City Center', coords: { lat: 17.385044, lng: 78.486671 } },
  ];

  const handleTest = () => {
    const isWithin = isWithinDeliveryArea(testCoords);
    const distance = getDistanceFromServiceCenter(testCoords);
    setResult({ isWithin, distance });
  };

  const handleTestLocation = (coords: { lat: number; lng: number }) => {
    setTestCoords(coords);
    const isWithin = isWithinDeliveryArea(coords);
    const distance = getDistanceFromServiceCenter(coords);
    setResult({ isWithin, distance });
  };

  const handleMapCoordinateChange = (coords: { lat: number; lng: number }) => {
    setTestCoords(coords);
    const isWithin = isWithinDeliveryArea(coords);
    const distance = getDistanceFromServiceCenter(coords);
    setResult({ isWithin, distance });
  };

  return (
    <div className="max-w-7xl mx-auto p-6">
      <h1 className="text-3xl font-bold mb-6">Delivery Area Test</h1>

      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
        <h2 className="text-lg font-semibold mb-2">Service Area Configuration</h2>
        <p><strong>Center:</strong> {SERVICE_AREA_CENTER.lat}, {SERVICE_AREA_CENTER.lng}</p>
        <p><strong>Radius:</strong> {SERVICE_AREA_RADIUS_KM} km ({(SERVICE_AREA_RADIUS_KM * 1000).toFixed(0)} meters)</p>
        <p><strong>Area:</strong> ~{(Math.PI * SERVICE_AREA_RADIUS_KM * SERVICE_AREA_RADIUS_KM).toFixed(2)} km²</p>
      </div>

      {/* Interactive Map */}
      <div className="bg-white border border-gray-200 rounded-lg p-4 mb-6">
        <h2 className="text-xl font-semibold mb-4">Interactive Delivery Area Map</h2>
        <p className="text-gray-600 mb-4">
          Click on the map or drag the marker to test different locations.
          The red circle shows the delivery area boundary.
        </p>

        <div className="h-96 mb-4">
          <DeliveryAreaMap
            selectedCoordinates={testCoords}
            onCoordinatesChange={handleMapCoordinateChange}
            height="100%"
            showTestMarkers={true}
            testLocations={testLocations}
          />
        </div>

        {result && (
          <div className={`p-4 rounded-md ${result.isWithin ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'}`}>
            <p className={`font-semibold text-lg ${result.isWithin ? 'text-green-800' : 'text-red-800'}`}>
              {result.isWithin ? '✅ Within delivery area' : '❌ Outside delivery area'}
            </p>
            <p className="text-sm text-gray-600 mt-1">
              Selected coordinates: {testCoords.lat.toFixed(6)}, {testCoords.lng.toFixed(6)}
            </p>
            <p className="text-sm text-gray-600">
              Distance from center: {result.distance.toFixed(3)} km ({(result.distance * 1000).toFixed(0)} meters)
            </p>
          </div>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <h2 className="text-xl font-semibold mb-4">Manual Test</h2>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-1">Latitude</label>
              <input
                type="number"
                step="0.000001"
                value={testCoords.lat}
                onChange={(e) => setTestCoords(prev => ({ ...prev, lat: parseFloat(e.target.value) || 0 }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-1">Longitude</label>
              <input
                type="number"
                step="0.000001"
                value={testCoords.lng}
                onChange={(e) => setTestCoords(prev => ({ ...prev, lng: parseFloat(e.target.value) || 0 }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
              />
            </div>
            
            <button
              onClick={handleTest}
              className="w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600"
            >
              Test Location
            </button>
            
            {result && (
              <div className={`p-3 rounded-md ${result.isWithin ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'}`}>
                <p className={`font-semibold ${result.isWithin ? 'text-green-800' : 'text-red-800'}`}>
                  {result.isWithin ? '✅ Within delivery area' : '❌ Outside delivery area'}
                </p>
                <p className="text-sm text-gray-600">
                  Distance from center: {result.distance.toFixed(3)} km
                </p>
                <p className="text-sm text-gray-600">
                  Distance in meters: {(result.distance * 1000).toFixed(0)} m
                </p>
              </div>
            )}
          </div>
        </div>

        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <h2 className="text-xl font-semibold mb-4">Predefined Test Locations</h2>
          <p className="text-gray-600 mb-4 text-sm">
            Click on any location to test it on the map. Numbered markers correspond to the locations below.
          </p>

          <div className="space-y-2 max-h-96 overflow-y-auto">
            {testLocations.map((location, index) => {
              const isWithin = isWithinDeliveryArea(location.coords);
              const distance = getDistanceFromServiceCenter(location.coords);

              return (
                <button
                  key={index}
                  onClick={() => handleTestLocation(location.coords)}
                  className={`w-full text-left p-3 rounded-md border transition-colors ${
                    isWithin
                      ? 'bg-green-50 border-green-200 hover:bg-green-100'
                      : 'bg-red-50 border-red-200 hover:bg-red-100'
                  }`}
                >
                  <div className="flex justify-between items-start">
                    <div className="flex items-start">
                      <span className={`inline-flex items-center justify-center w-6 h-6 rounded-full text-xs font-bold mr-3 mt-0.5 ${
                        isWithin ? 'bg-green-500 text-white' : 'bg-red-500 text-white'
                      }`}>
                        {index + 1}
                      </span>
                      <div>
                        <p className="font-medium">{location.name}</p>
                        <p className="text-xs text-gray-600">
                          {location.coords.lat.toFixed(6)}, {location.coords.lng.toFixed(6)}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className={`text-sm font-medium ${isWithin ? 'text-green-600' : 'text-red-600'}`}>
                        {isWithin ? '✅ Within' : '❌ Outside'}
                      </p>
                      <p className="text-xs text-gray-600">
                        {distance.toFixed(2)} km
                      </p>
                    </div>
                  </div>
                </button>
              );
            })}
          </div>
        </div>
      </div>

      <div className="mt-6 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <h3 className="text-lg font-semibold mb-2">Implementation Notes</h3>
        <ul className="text-sm space-y-1">
          <li>• The delivery area is a circle with radius {SERVICE_AREA_RADIUS_KM} km centered at {SERVICE_AREA_CENTER.lat}, {SERVICE_AREA_CENTER.lng}</li>
          <li>• The red circle on the map shows the exact delivery boundary</li>
          <li>• Green markers indicate locations within the delivery area, red markers are outside</li>
          <li>• Distance calculation uses the Haversine formula for accuracy</li>
          <li>• Users outside this area will see "Out of stock for your area" message</li>
          <li>• Location validation happens both on frontend (immediate feedback) and backend (security)</li>
          <li>• Cart and checkout are blocked for users outside the delivery area</li>
          <li>• Click anywhere on the map or drag the marker to test different locations</li>
        </ul>
      </div>
    </div>
  );
}
