'use client';

import React, { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { useCart } from '@/contexts/CartContext';
import { orderService } from '@/services/api';
import { Order, OrderStatus } from '@/types/order';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import {
  ArrowLeft,
  MapPin,
  Clock,
  Package,
  CreditCard,
  CheckCircle,
  XCircle,
  ExternalLink,
  RotateCcw,
  ShoppingCart
} from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';
import { formatDate, formatTime, getRelativeTime } from '@/lib/utils';
import { getImageUrl } from '@/utils/imageUtils';
import { formatOrderIdForDisplay } from '@/lib/orderUtils';
import OrderReviewSection from '@/components/reviews/OrderReviewSection';
import OrderTracker from '@/components/orders/OrderTracker';

export default function OrderDetailPage() {
  const params = useParams();
  const router = useRouter();
  const { isAuthenticated, isLoading: authLoading } = useAuth();
  const { reorderFromOrder, loading: cartLoading } = useCart();
  const [order, setOrder] = useState<Order | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [reordering, setReordering] = useState(false);

  const orderId = params?.id as string;

  useEffect(() => {
    // Redirect to login if not authenticated
    if (!authLoading && !isAuthenticated) {
      toast.error('Please log in to view order details');
      router.push('/login');
      return;
    }

    // Fetch order if authenticated
    if (isAuthenticated && orderId) {
      fetchOrder();
    }
  }, [isAuthenticated, authLoading, orderId, router]);

  const fetchOrder = async () => {
    try {
      setLoading(true);
      const response = await orderService.getOrderById(orderId);

      if (response.success && response.data) {
        setOrder(response.data);
      } else {
        setError(response.message || 'Failed to fetch order details');
      }
    } catch (err: any) {
      console.error('Error fetching order details:', err);

      // Check if it's a temporary order ID error
      if (err.message && err.message.includes('temporary order ID')) {
        setError(err.message);
      } else {
        setError('Failed to fetch order details. Please try again later.');
      }
    } finally {
      setLoading(false);
    }
  };

  // Cancel functionality removed as per requirements

  // Get status badge color
  const getStatusColor = (status: OrderStatus) => {
    switch (status) {
      case 'placed':
        return 'bg-blue-100 text-blue-800';
      case 'confirmed':
        return 'bg-indigo-100 text-indigo-800';
      case 'preparing':
        return 'bg-yellow-100 text-yellow-800';
      case 'out_for_delivery':
        return 'bg-orange-100 text-orange-800';
      case 'delivered':
        return 'bg-green-100 text-green-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Format status text
  const formatStatus = (status: OrderStatus) => {
    switch (status) {
      case 'out_for_delivery':
        return 'Out for Delivery';
      default:
        return status.charAt(0).toUpperCase() + status.slice(1);
    }
  };

  const handleReorder = async () => {
    if (!order || !order.items || order.items.length === 0) {
      toast.error('No items to reorder');
      return;
    }

    setReordering(true);
    try {
      await reorderFromOrder(order.items);
      // Navigate to cart after successful reorder
      router.push('/cart');
    } catch (error) {
      console.error('Error reordering:', error);
      toast.error('Failed to reorder items. Please try again.');
    } finally {
      setReordering(false);
    }
  };

  if (authLoading || loading) {
    return (
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-bandiwala-orange"></div>
        </div>
      </div>
    );
  }

  if (error) {
    const isTempOrderError = error.includes('temporary order ID');

    return (
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        <div className="flex items-center mb-6">
          <Link href="/orders" className="mr-4">
            <Button variant="ghost" size="icon">
              <ArrowLeft className="h-5 w-5" />
            </Button>
          </Link>
          <h1 className="text-2xl font-bold">Order Details</h1>
        </div>

        <div className={`border rounded-lg p-6 mb-6 ${
          isTempOrderError
            ? 'bg-yellow-50 border-yellow-200'
            : 'bg-red-50 border-red-200'
        }`}>
          <div className="flex items-start gap-3">
            {isTempOrderError ? (
              <div className="w-6 h-6 rounded-full bg-yellow-100 flex items-center justify-center flex-shrink-0 mt-0.5">
                <Clock className="h-4 w-4 text-yellow-600" />
              </div>
            ) : (
              <div className="w-6 h-6 rounded-full bg-red-100 flex items-center justify-center flex-shrink-0 mt-0.5">
                <XCircle className="h-4 w-4 text-red-600" />
              </div>
            )}
            <div className="flex-grow">
              <h3 className={`font-medium mb-2 ${
                isTempOrderError ? 'text-yellow-800' : 'text-red-800'
              }`}>
                {isTempOrderError ? 'Temporary Order ID' : 'Error Loading Order'}
              </h3>
              <p className={`text-sm mb-4 ${
                isTempOrderError ? 'text-yellow-700' : 'text-red-600'
              }`}>
                {error}
              </p>
              <div className="flex gap-2">
                <Link href="/orders">
                  <Button variant="outline" size="sm">
                    View All Orders
                  </Button>
                </Link>
                {!isTempOrderError && (
                  <Button
                    onClick={fetchOrder}
                    variant="outline"
                    size="sm"
                  >
                    Try Again
                  </Button>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!order) {
    return (
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        <div className="flex items-center mb-6">
          <Link href="/orders" className="mr-4">
            <Button variant="ghost" size="icon">
              <ArrowLeft className="h-5 w-5" />
            </Button>
          </Link>
          <h1 className="text-2xl font-bold">Order Details</h1>
        </div>

        <div className="bg-gray-50 border border-gray-200 rounded-lg p-8 text-center">
          <p className="text-gray-600">Order not found</p>
          <Link href="/orders">
            <Button className="mt-4">Back to Orders</Button>
          </Link>
        </div>
      </div>
    );
  }

  // Format the date and time
  const orderDate = new Date(order.createdAt);
  const formattedDate = formatDate(orderDate);
  const formattedTime = formatTime(orderDate);

  return (
    <div className="container mx-auto px-3 sm:px-4 py-4 sm:py-8 max-w-4xl">
      <div className="flex items-center mb-4 sm:mb-6">
        <Link href="/orders" className="mr-3 sm:mr-4">
          <Button variant="ghost" size="icon" className="h-8 w-8 sm:h-10 sm:w-10">
            <ArrowLeft className="h-4 w-4 sm:h-5 sm:w-5" />
          </Button>
        </Link>
        <h1 className="text-xl sm:text-2xl font-bold">Order Details</h1>
      </div>

      {/* Order header */}
      <div className="bg-white rounded-lg shadow-sm p-4 sm:p-6 mb-4 sm:mb-6">
        <div className="flex flex-col space-y-3 sm:space-y-0 sm:flex-row sm:justify-between sm:items-center mb-4">
          <div className="flex-1">
            <div className="flex flex-col sm:flex-row sm:items-center gap-2 mb-2">
              <h2 className="text-base sm:text-lg font-semibold break-all">
                Order #{formatOrderIdForDisplay(order.orderNumber || order._id)}
              </h2>
              <span className={`text-xs px-2 py-1 rounded-full self-start ${getStatusColor(order.orderStatus)}`}>
                {formatStatus(order.orderStatus)}
              </span>
            </div>
            <div className="text-sm text-gray-500 space-y-1">
              <p>{formattedDate} at {formattedTime}</p>
              <p className="text-xs text-gray-400">
                {getRelativeTime(new Date(order.createdAt))}
              </p>
              <div className="mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded-md">
                <p className="text-xs text-yellow-700 flex items-center">
                  <Clock className="h-3 w-3 mr-1" />
                  Warning:  Delivery times are estimated and may vary. If you see "Delivered" early, don’t worry — we’re in testing mode. Thanks for your patience!
                </p>
              </div>
            </div>
          </div>

          {/* Reorder button */}
          <div className="flex gap-2 w-full sm:w-auto">
            <Button
              onClick={handleReorder}
              disabled={reordering || cartLoading}
              className="bg-bandiwala-orange hover:bg-bandiwala-red text-white w-full sm:w-auto"
              size="sm"
            >
              {reordering ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  <span className="hidden sm:inline">Adding to Cart...</span>
                  <span className="sm:hidden">Adding...</span>
                </>
              ) : (
                <>
                  <RotateCcw className="h-4 w-4 mr-2" />
                  Reorder
                </>
              )}
            </Button>
          </div>
        </div>

        <div className="border-t border-gray-100 pt-4 mt-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
            <div>
              <h3 className="text-sm font-medium text-gray-500 mb-3">Delivery Address</h3>
              <div className="flex items-start gap-3">
                <MapPin className="h-4 w-4 text-gray-500 mt-1 flex-shrink-0" />
                <div className="flex-1 min-w-0">
                  <p className="text-sm text-gray-700 break-words leading-relaxed">{order.deliveryAddress.formatted}</p>
                  {order.deliveryAddress.mapUrl && (
                    <a
                      href={order.deliveryAddress.mapUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-xs text-blue-600 hover:underline flex items-center mt-2 gap-1"
                    >
                      View on Map <ExternalLink className="h-3 w-3" />
                    </a>
                  )}
                  <p className="text-xs text-gray-500 mt-2 leading-relaxed">
                    For more details about order contact us at{' '}
                    <a href="tel:+917416467890" className="text-blue-600 hover:underline">
                      +91 74164 67890
                    </a>
                  </p>
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-sm font-medium text-gray-500 mb-3">Payment Information</h3>
              <div className="flex items-start gap-3">
                <CreditCard className="h-4 w-4 text-gray-500 mt-1 flex-shrink-0" />
                <div className="flex-1">
                  <p className="text-sm text-gray-700 break-words">
                    {order.paymentMethod === 'card' ? 'Online Payment (Card/UPI)' :
                     order.paymentMethod === 'upi' ? 'UPI Payment' : 'Cash on Delivery'}
                  </p>
                  <div className="flex items-center mt-2">
                    {order.paymentStatus === 'paid' ? (
                      <>
                        <CheckCircle className="h-3 w-3 text-green-500 mr-1 flex-shrink-0" />
                        <p className="text-xs text-green-600">Payment Completed</p>
                      </>
                    ) : order.paymentStatus === 'failed' ? (
                      <>
                        <XCircle className="h-3 w-3 text-red-500 mr-1 flex-shrink-0" />
                        <p className="text-xs text-red-600">Payment Failed</p>
                      </>
                    ) : (
                      <>
                        <Clock className="h-3 w-3 text-yellow-500 mr-1 flex-shrink-0" />
                        <p className="text-xs text-yellow-600">Payment Pending</p>
                      </>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Order Tracker - Show for all active orders */}
      {order.orderStatus !== 'cancelled' && (
        <div className="mb-4 sm:mb-6">
          <OrderTracker
            orderId={order._id}
            orderNumber={order.orderNumber}
            initialStatus={order.orderStatus}
            statusTimeline={order.statusTimeline || []}
            onStatusChange={() => {
              // Refresh the page when status changes
              window.location.reload();
            }}
          />
        </div>
      )}

      {/* Order items */}
      <div className="bg-white rounded-lg shadow-sm p-4 sm:p-6 mb-4 sm:mb-6">
        <h2 className="text-lg font-semibold mb-4">Order Items</h2>

        <div className="divide-y divide-gray-100">
          {order.items.map((item, index) => (
            <div key={index} className="py-3 sm:py-4 flex gap-3 sm:gap-4">
              <div className="w-12 h-12 sm:w-16 sm:h-16 rounded-md overflow-hidden bg-gray-100 flex-shrink-0">
                {item.image ? (
                  <Image
                    src={getImageUrl(item.image)}
                    alt={item.name}
                    width={64}
                    height={64}
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center bg-gray-200">
                    <Package className="h-4 w-4 sm:h-6 sm:w-6 text-gray-400" />
                  </div>
                )}
              </div>

              <div className="flex-grow min-w-0">
                <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-1 sm:gap-2">
                  <h3 className="font-medium text-sm sm:text-base break-words">{item.name}</h3>
                  <p className="font-medium text-sm sm:text-base text-right flex-shrink-0">
                    ₹{((item.selectedSubcategory?.price || item.price || 0) * item.quantity).toFixed(2)}
                  </p>
                </div>
                <p className="text-xs sm:text-sm text-gray-600 mt-1">
                  {item.selectedSubcategory?.title || 'Regular'} • Qty: {item.quantity}
                </p>
                {item.notes && (
                  <p className="text-xs sm:text-sm text-gray-500 mt-1 break-words">Note: {item.notes}</p>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Order summary */}
      <div className="bg-white rounded-lg shadow-sm p-4 sm:p-6">
        <h2 className="text-lg font-semibold mb-4">Order Summary</h2>

        <div className="space-y-3">
          <div className="flex justify-between items-center">
            <p className="text-gray-600 text-sm sm:text-base">Subtotal</p>
            <p className="text-sm sm:text-base font-medium">₹{order.subtotal.toFixed(2)}</p>
          </div>

          <div className="flex justify-between items-center">
            <p className="text-gray-600 text-sm sm:text-base">Platform Fee</p>
            <p className="text-sm sm:text-base font-medium">₹{order.platformFee.toFixed(2)}</p>
          </div>

          <div className="flex justify-between items-center">
            <p className="text-gray-600 text-sm sm:text-base">Delivery Charge</p>
            <p className="text-sm sm:text-base font-medium">₹{order.deliveryCharge.toFixed(2)}</p>
          </div>

          <div className="flex justify-between items-center">
            <p className="text-gray-600 text-sm sm:text-base">Taxes</p>
            <p className="text-sm sm:text-base font-medium">₹{order.tax.toFixed(2)}</p>
          </div>

          {order.discount > 0 && (
            <div className="flex justify-between items-center text-green-600">
              <p className="text-sm sm:text-base">Discount</p>
              <p className="text-sm sm:text-base font-medium">-₹{order.discount.toFixed(2)}</p>
            </div>
          )}

          <div className="border-t border-gray-100 pt-3 mt-3">
            <div className="flex justify-between items-center font-semibold">
              <p className="text-base sm:text-lg">Total</p>
              <p className="text-base sm:text-lg">₹{order.total.toFixed(2)}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Review Section - Only show for delivered orders */}
      {order.orderStatus === 'delivered' && (
        <div className="mt-4 sm:mt-6">
          <OrderReviewSection
            orderId={order._id}
            orderNumber={order.orderNumber}
          />
        </div>
      )}
    </div>
  );
}
