import { MetadataRoute } from 'next';

export default function sitemap(): MetadataRoute.Sitemap {
  const baseUrl = 'https://bandiwala.co.in';

  // Static routes
  const staticRoutes = [
    {
      url: baseUrl,
      lastModified: new Date(),
      priority: 1.0,
      changeFrequency: 'daily' as const
    },
    {
      url: `${baseUrl}/about-us`,
      lastModified: new Date(),
      priority: 0.8,
      changeFrequency: 'monthly' as const
    },
    {
      url: `${baseUrl}/terms`,
      lastModified: new Date(),
      priority: 0.5,
      changeFrequency: 'monthly' as const
    },
    {
      url: `${baseUrl}/vendors`,
      lastModified: new Date(),
      priority: 0.9,
      changeFrequency: 'daily' as const
    }
  ];

  return [...staticRoutes];
}