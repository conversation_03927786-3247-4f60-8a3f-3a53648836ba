"use client";
import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useParams } from 'next/navigation';

export default function PasswordResetRedirectCompat() {
  const router = useRouter();
  const params = useParams();
  const { token } = params;
  const [isRedirecting, setIsRedirecting] = useState(false);

  useEffect(() => {
    if (token && !isRedirecting) {
      setIsRedirecting(true);
      // Add a small delay to ensure the page is ready
      setTimeout(() => {
        // Redirect to the correct reset-password page with the token as a query parameter
        router.replace(`/reset-password?token=${token}`);
      }, 100);
    }
  }, [token, router, isRedirecting]);

  return (
    <div className="flex items-center justify-center min-h-[80vh] px-4">
      <div className="w-full max-w-md bg-white rounded-lg shadow-md p-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-2">Redirecting...</h1>
          <p className="text-gray-500 mb-6">Please wait while we redirect you to the password reset page.</p>
          <div className="flex justify-center">
            <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-green-600"></div>
          </div>
        </div>
      </div>
    </div>
  );
}
