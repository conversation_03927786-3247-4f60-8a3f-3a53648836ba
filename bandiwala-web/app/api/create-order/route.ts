import { NextResponse } from "next/server";

export async function POST(request: Request) {
  try {
    console.log('API route /api/create-order: Request received');

    // Parse the request body
    const body = await request.json();
    const { amount, currency = "INR", promoCode } = body;

    console.log('API route /api/create-order: Request body:', { amount, currency, promoCode });

    if (!amount || amount <= 0) {
      console.error('API route /api/create-order: Valid amount is required');
      return NextResponse.json(
        { success: false, message: 'Valid amount is required' },
        { status: 400 }
      );
    }

    // Get auth token from cookies or headers
    const cookies = request.headers.get('cookie');
    let token = null;

    if (cookies) {
      const tokenMatch = cookies.match(/token=([^;]+)/);
      if (tokenMatch) {
        token = tokenMatch[1];
      }
    }

    if (!token) {
      const authHeader = request.headers.get('authorization');
      if (authHeader && authHeader.startsWith('Bearer ')) {
        token = authHeader.split(' ')[1];
      }
    }

    if (!token) {
      console.error('API route /api/create-order: No authentication token found');
      console.error('Cookies:', cookies);
      console.error('Auth header:', request.headers.get('authorization'));
      return NextResponse.json(
        { success: false, message: 'Authentication required' },
        { status: 401 }
      );
    }

    console.log('API route /api/create-order: Token found:', token ? `${token.substring(0, 10)}...` : 'None');

    // Call backend API to create payment order
    const backendUrl = `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:6111'}/api/payments/create-order`;
    console.log('API route /api/create-order: Calling backend at:', backendUrl);

    const response = await fetch(backendUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({ amount, currency, promoCode })
    });

    console.log('API route /api/create-order: Backend response status:', response.status);

    const data = await response.json();
    console.log('API route /api/create-order: Backend response data:', data);

    if (!response.ok) {
      return NextResponse.json(
        { success: false, message: data.message || 'Failed to create payment order' },
        { status: response.status }
      );
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in /api/create-order:', error);
    return NextResponse.json(
      { success: false, message: 'Server error' },
      { status: 500 }
    );
  }
}