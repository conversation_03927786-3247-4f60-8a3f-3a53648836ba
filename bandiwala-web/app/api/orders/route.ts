import { NextResponse } from 'next/server';
import { Order } from '@/types/order';

// Mock orders data
const mockOrders: Order[] = [
  {
    _id: 'deed410f', // Matching the URL ID from the screenshot
    orderNumber: 'BW-20241220-143022-001', // Custom order number
    user: 'user123',
    items: [
      {
        menuItemId: 'item1',
        quantity: 1,
        selectedSubcategory: {
          title: 'Full Plate',
          quantity: '1',
          price: 60
        },
        name: '<PERSON>v Bhaji',
        price: 60,
        image: '/images/pav-bhaji.jpg',
        vendorId: 'vendor1',
        vendorName: 'Test Vendor'
      },
      {
        menuItemId: 'item2',
        quantity: 1,
        selectedSubcategory: {
          title: 'Full Plate',
          quantity: '1',
          price: 20
        },
        name: 'Extra Pav',
        price: 20,
        image: '/images/extra-pav.jpg',
        vendorId: 'vendor1',
        vendorName: 'Test Vendor'
      },
      {
        menuItemId: 'item3',
        quantity: 1,
        selectedSubcategory: {
          title: 'Single',
          quantity: '1',
          price: 30
        },
        name: 'Vada Pav',
        price: 30,
        image: '/images/vada-pav.jpg',
        vendorId: 'vendor1',
        vendorName: 'Test Vendor'
      }
    ],
    deliveryAddress: {
      formatted: '9FPP+2M5 Save Nation, Hashmath Gunj, Subhash Nagar, Badi Chowdi, Kachiguda, Hyderabad, Telangana 500095, India',
      coordinates: {
        lat: 17.385000,
        lng: 78.486700
      },
      mapUrl: 'https://www.google.com/maps?q=17.385,78.4867'
    },
    subtotal: 110.00, // 60 + 20 + 30
    platformFee: 5.00,
    deliveryCharge: 20.00,
    tax: 6.00, // 5% of 110
    discount: 0,
    total: 141.00, // 110 + 5 + 20 + 6
    paymentMethod: 'card',
    paymentStatus: 'paid',
    orderStatus: 'placed',
    estimatedDeliveryTime: '30-45 min',
    createdAt: new Date('2025-05-30T12:52:00Z').toISOString(),
    updatedAt: new Date('2025-05-30T12:52:00Z').toISOString()
  },
  {
    _id: 'order123456789',
    user: 'user123',
    items: [
      {
        menuItemId: 'item1',
        quantity: 2,
        selectedSubcategory: {
          title: 'Regular',
          quantity: 'Full Plate',
          price: 199
        },
        name: 'Chicken Biryani',
        price: 199,
        image: '/images/biryani.jpg',
        vendorId: 'vendor1',
        vendorName: 'Biryani House',
        notes: 'Extra spicy'
      },
      {
        menuItemId: 'item2',
        quantity: 1,
        selectedSubcategory: {
          title: 'Large',
          quantity: '2 pcs',
          price: 49
        },
        name: 'Butter Naan',
        price: 49,
        image: '/images/naan.jpg',
        vendorId: 'vendor1',
        vendorName: 'Biryani House'
      }
    ],
    deliveryAddress: {
      formatted: '123 Main St, Hyderabad, Telangana 500001',
      coordinates: {
        lat: 17.4485835,
        lng: 78.3908034
      },
      mapUrl: 'https://maps.google.com/?q=17.4485835,78.3908034'
    },
    subtotal: 447,
    platformFee: 5,
    deliveryCharge: 20,
    tax: 22, // 5% of 447
    discount: 0,
    total: 494, // 447 + 5 + 20 + 22
    paymentMethod: 'card',
    paymentStatus: 'paid',
    orderStatus: 'delivered',
    estimatedDeliveryTime: '30-40 min',
    createdAt: new Date(Date.now() - 86400000).toISOString(), // 1 day ago
    updatedAt: new Date(Date.now() - 86400000 + 3600000).toISOString() // 1 day ago + 1 hour
  },
  {
    _id: 'order987654321',
    user: 'user123',
    items: [
      {
        menuItemId: 'item3',
        quantity: 1,
        selectedSubcategory: {
          title: 'Regular',
          quantity: 'Full Plate',
          price: 249
        },
        name: 'Paneer Tikka',
        price: 249,
        image: '/images/paneer.jpg',
        vendorId: 'vendor2',
        vendorName: 'Punjabi Dhaba'
      },
      {
        menuItemId: 'item4',
        quantity: 2,
        selectedSubcategory: {
          title: 'Regular',
          quantity: '2 pcs',
          price: 59
        },
        name: 'Garlic Naan',
        price: 59,
        image: '/images/garlic-naan.jpg',
        vendorId: 'vendor2',
        vendorName: 'Punjabi Dhaba'
      }
    ],
    deliveryAddress: {
      formatted: '456 Park Ave, Hyderabad, Telangana 500016',
      coordinates: {
        lat: 17.4522,
        lng: 78.3801
      },
      mapUrl: 'https://maps.google.com/?q=17.4522,78.3801'
    },
    subtotal: 367,
    platformFee: 5,
    deliveryCharge: 20,
    tax: 18, // 5% of 367
    discount: 50,
    total: 360, // 367 + 5 + 20 + 18 - 50
    paymentMethod: 'upi',
    paymentStatus: 'paid',
    orderStatus: 'placed',
    estimatedDeliveryTime: '40-50 min',
    createdAt: new Date().toISOString(), // Now
    updatedAt: new Date().toISOString() // Now
  }
];

export async function GET() {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 500));

  return NextResponse.json({
    success: true,
    message: 'Orders fetched successfully',
    data: mockOrders
  });
}
