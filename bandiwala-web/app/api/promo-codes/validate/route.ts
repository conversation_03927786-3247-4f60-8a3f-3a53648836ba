import { NextResponse } from "next/server";

export async function POST(request: Request) {
  try {
    console.log('API route /api/promo-codes/validate: Request received');

    // Parse the request body
    const body = await request.json();
    const { code, subtotal } = body;

    console.log('Promo code validation request:', { code, subtotal });

    if (!code || !subtotal) {
      return NextResponse.json(
        { success: false, message: 'Promo code and subtotal are required' },
        { status: 400 }
      );
    }

    // Get auth token from cookies or headers
    const cookies = request.headers.get('cookie');
    let token = null;

    if (cookies) {
      const tokenMatch = cookies.match(/token=([^;]+)/);
      if (tokenMatch) {
        token = tokenMatch[1];
      }
    }

    // Also check Authorization header
    if (!token) {
      const authHeader = request.headers.get('authorization');
      if (authHeader && authHeader.startsWith('Bearer ')) {
        token = authHeader.substring(7);
      }
    }

    console.log('API route /api/promo-codes/validate: Token found:', token ? `${token.substring(0, 10)}...` : 'None');

    if (!token) {
      return NextResponse.json(
        { success: false, message: 'Authentication required' },
        { status: 401 }
      );
    }

    // Call backend API to validate promo code
    const backendUrl = `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:6111'}/api/promo-codes/validate`;
    console.log('API route /api/promo-codes/validate: Calling backend at:', backendUrl);

    const response = await fetch(backendUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({ code, subtotal })
    });

    console.log('API route /api/promo-codes/validate: Backend response status:', response.status);

    const data = await response.json();
    console.log('API route /api/promo-codes/validate: Backend response data:', data);

    // Return the response from backend
    if (response.ok) {
      return NextResponse.json(data);
    } else {
      return NextResponse.json(data, { status: response.status });
    }

  } catch (error) {
    console.error('API route /api/promo-codes/validate: Error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}
