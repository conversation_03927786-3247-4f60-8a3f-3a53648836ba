import { NextRequest, NextResponse } from 'next/server';
import { verifyAuth } from '@/lib/auth';

// Static menu items data - this should match the data structure from the backend
const staticMenuItems = [
  {
    _id: "6756b8e7b4c5d2e8f9a0b1c2",
    itemName: "Sevi Puri",
    slug: "sevi-puri",
    description: "Crispy puri loaded with sev, chutneys and vegetables",
    itemCategory: "Street Food",
    subcategories: [
      { title: "Small", quantity: "6 pieces", price: 45 },
      { title: "Large", quantity: "10 pieces", price: 75 }
    ],
    image: "/images/sevi-puri.jpg",
    vendorId: "6756b8e7b4c5d2e8f9a0b1c1",
    isAvailable: true
  },
  {
    _id: "6756b8e7b4c5d2e8f9a0b1c3",
    itemName: "Pani Puri",
    slug: "pani-puri",
    description: "Traditional pani puri with spicy and tangy water",
    itemCategory: "Street Food",
    subcategories: [
      { title: "Small", quantity: "6 pieces", price: 40 },
      { title: "Large", quantity: "10 pieces", price: 65 }
    ],
    image: "/images/pani-puri.jpg",
    vendorId: "6756b8e7b4c5d2e8f9a0b1c1",
    isAvailable: true
  },
  {
    _id: "6756b8e7b4c5d2e8f9a0b1c4",
    itemName: "Bhel Puri",
    slug: "bhel-puri",
    description: "Mumbai style bhel puri with crispy sev and chutneys",
    itemCategory: "Street Food",
    subcategories: [
      { title: "Regular", quantity: "1 plate", price: 50 },
      { title: "Extra", quantity: "1 large plate", price: 80 }
    ],
    image: "/images/bhel-puri.jpg",
    vendorId: "6756b8e7b4c5d2e8f9a0b1c1",
    isAvailable: true
  },
  {
    _id: "6756b8e7b4c5d2e8f9a0b1c5",
    itemName: "Dahi Puri",
    slug: "dahi-puri",
    description: "Sweet and tangy dahi puri with yogurt and chutneys",
    itemCategory: "Street Food",
    subcategories: [
      { title: "Small", quantity: "6 pieces", price: 55 },
      { title: "Large", quantity: "10 pieces", price: 85 }
    ],
    image: "/images/dahi-puri.jpg",
    vendorId: "6756b8e7b4c5d2e8f9a0b1c1",
    isAvailable: true
  },
  {
    _id: "6756b8e7b4c5d2e8f9a0b1c6",
    itemName: "Vada Pav",
    slug: "vada-pav",
    description: "Mumbai's favorite vada pav with spicy chutneys",
    itemCategory: "Street Food",
    subcategories: [
      { title: "Single", quantity: "1 piece", price: 25 },
      { title: "Double", quantity: "2 pieces", price: 45 }
    ],
    image: "/images/vada-pav.jpg",
    vendorId: "6756b8e7b4c5d2e8f9a0b1c1",
    isAvailable: true
  },
  {
    _id: "6756b8e7b4c5d2e8f9a0b1c7",
    itemName: "Chicken Biryani",
    slug: "chicken-biryani",
    description: "Aromatic basmati rice with tender chicken pieces",
    itemCategory: "Biryani",
    subcategories: [
      { title: "Half", quantity: "300g", price: 180 },
      { title: "Full", quantity: "500g", price: 320 }
    ],
    image: "/images/chicken-biryani.jpg",
    vendorId: "6756b8e7b4c5d2e8f9a0b1c8",
    isAvailable: true
  },
  {
    _id: "6756b8e7b4c5d2e8f9a0b1c8",
    itemName: "Mutton Biryani",
    slug: "mutton-biryani",
    description: "Rich and flavorful mutton biryani with aromatic spices",
    itemCategory: "Biryani",
    subcategories: [
      { title: "Half", quantity: "300g", price: 250 },
      { title: "Full", quantity: "500g", price: 450 }
    ],
    image: "/images/mutton-biryani.jpg",
    vendorId: "6756b8e7b4c5d2e8f9a0b1c8",
    isAvailable: true
  },
  {
    _id: "6756b8e7b4c5d2e8f9a0b1c9",
    itemName: "Vegetable Biryani",
    slug: "vegetable-biryani",
    description: "Fragrant vegetable biryani with mixed vegetables",
    itemCategory: "Biryani",
    subcategories: [
      { title: "Half", quantity: "300g", price: 150 },
      { title: "Full", quantity: "500g", price: 280 }
    ],
    image: "/images/vegetable-biryani.jpg",
    vendorId: "6756b8e7b4c5d2e8f9a0b1c8",
    isAvailable: true
  }
];

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    console.log('Static menu item by slug API called');
    
    // Await the params to get the slug
    const { slug } = await params;
    console.log('Looking for menu item with slug:', slug);

    // Check authentication
    const { isAuthenticated, user } = verifyAuth(request);

    if (!isAuthenticated) {
      console.log('Unauthorized access to menu item by slug API');
      return NextResponse.json({
        success: false,
        message: 'Authentication required to access menu item data'
      }, { status: 401 });
    }

    console.log('Authenticated user accessing menu item by slug:', user?.userId || user?.id);

    // Find the menu item by slug
    const menuItem = staticMenuItems.find(item => item.slug === slug);

    if (!menuItem) {
      console.log(`Menu item with slug ${slug} not found`);
      return NextResponse.json({
        success: false,
        message: `Menu item with slug ${slug} not found`
      }, { status: 404 });
    }

    console.log(`Found menu item with slug ${slug}:`, menuItem.itemName);

    return NextResponse.json({
      success: true,
      data: menuItem
    });
  } catch (error) {
    console.error('Error in static menu item by slug API:', error);
    return NextResponse.json({
      success: false,
      message: 'Failed to fetch menu item',
      data: null
    }, { status: 500 });
  }
}
