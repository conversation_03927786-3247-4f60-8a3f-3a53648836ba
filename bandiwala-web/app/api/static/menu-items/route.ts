import { NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';

// Helper function to verify authentication
function verifyAuth(request: Request): { isAuthenticated: boolean; user?: any } {
  try {
    // Check for token in cookies first
    const cookies = request.headers.get('cookie');
    let token = null;

    if (cookies) {
      const tokenMatch = cookies.match(/token=([^;]+)/);
      if (tokenMatch) {
        token = tokenMatch[1];
      }
    }

    // If no token in cookies, check Authorization header
    if (!token) {
      const authHeader = request.headers.get('authorization');
      if (authHeader && authHeader.startsWith('Bearer ')) {
        token = authHeader.split(' ')[1];
      }
    }

    if (!token) {
      return { isAuthenticated: false };
    }

    // Verify the token
    const jwtSecret = process.env.JWT_SECRET || 'bandiwala123dfghadfsjtktfgdsaDGFHGJHKJJDSAFGHJ';
    const decoded = jwt.verify(token, jwtSecret) as any;

    return { isAuthenticated: true, user: decoded };
  } catch (error) {
    console.error('Auth verification error:', error);
    return { isAuthenticated: false };
  }
}

// Static menu items data as fallback - representative sample from each vendor
const staticMenuItems = [
  // Jai Bhavani Chat Bhandar items
  {
    _id: "item1",
    itemName: "Dahi Papdi",
    description: "Crispy papdi topped with yogurt, chutneys and spices",
    itemCategory: "Papidi",
    slug: "dahi-papdi",
    subcategories: [
      { title: "unit", quantity: "unit", price: 45 }
    ],
    image: "/bandiwala-items-pics/items/Jai Bhavani Chat/dahi papdi.jpg",
    vendorId: "vendor1",
    isAvailable: true
  },
  {
    _id: "item2",
    itemName: "Pani Puri (6 pcs)",
    description: "Crispy puris filled with spicy water and chutneys",
    itemCategory: "Pani Puri",
    slug: "pani-puri-6-pcs",
    subcategories: [
      { title: "6 pcs", quantity: "6 Pcs", price: 25 }
    ],
    image: "/bandiwala-items-pics/items/Jai Bhavani Chat/pani puri.jpeg",
    vendorId: "vendor1",
    isAvailable: true
  },
  {
    _id: "item3",
    itemName: "Bhel Puri",
    description: "Popular Mumbai street food with puffed rice and chutneys",
    itemCategory: "Pani Puri",
    slug: "bhel-puri",
    subcategories: [
      { title: "7 Pcs", quantity: "7 Pcs", price: 40 }
    ],
    image: "/bandiwala-items-pics/items/Jai Bhavani Chat/bhel puri.jpeg",
    vendorId: "vendor1",
    isAvailable: true
  },

  // BFC Chicken Pakodi Center items
  {
    _id: "item4",
    itemName: "CHICKEN PAKODI (Bone)",
    description: "Crispy chicken pakodi with bone, perfectly spiced",
    itemCategory: "Chicken Pakodi",
    slug: "chicken-pakodi-bone",
    subcategories: [
      { title: "250 gr", quantity: "Chicken Pakodi 250 gr", price: 155 },
      { title: "500 gr", quantity: "Chicken Pakodi 500 gr", price: 305 },
      { title: "750 gr", quantity: "Chicken Pakodi 750 gr", price: 455 },
      { title: "1 Kg", quantity: "Chicken Pakodi 1 Kg", price: 605 }
    ],
    image: "/bandiwala-items-pics/items/BFC Chicken pakodi/Chicken pakodi_chicken bone.jpg",
    vendorId: "vendor2",
    isAvailable: true
  },
  {
    _id: "item5",
    itemName: "CHICKEN WINGS",
    description: "Crispy fried chicken wings with special masala",
    itemCategory: "Chicken Fried",
    slug: "chicken-wings",
    subcategories: [
      { title: "250 gr", quantity: "Chicken Wings 250 gr", price: 155 },
      { title: "500 gr", quantity: "Chicken Wings 500 gr", price: 305 }
    ],
    image: "/bandiwala-items-pics/items/BFC Chicken pakodi/Chicken wings.jpg",
    vendorId: "vendor2",
    isAvailable: true
  },
  {
    _id: "item6",
    itemName: "Prawns Fry",
    description: "Fresh prawns fried to perfection with spices",
    itemCategory: "Fish items / Sea foods",
    slug: "prawns-fry",
    subcategories: [
      { title: "250 gr", quantity: "250 gr", price: 255 },
      { title: "500G", quantity: "500G", price: 505 }
    ],
    image: "/bandiwala-items-pics/items/BFC Chicken pakodi/Prawns fry.png",
    vendorId: "vendor2",
    isAvailable: true
  },

  // Rajahmundry vari Special Muntha Masala items
  {
    _id: "item7",
    itemName: "Mirchi Bajji (With onion stuffing)",
    description: "Spicy green chili bajji stuffed with onions",
    itemCategory: "Bajjis",
    slug: "mirchi-bajji-with-onion-stuffing",
    subcategories: [
      { title: "4pcs", quantity: "4pcs", price: 45 }
    ],
    image: "/bandiwala-items-pics/items/Rajabundry Vaari special muntha masala/BAJJI/Mirchi bajji.png",
    vendorId: "vendor3",
    isAvailable: true
  },
  {
    _id: "item8",
    itemName: "Masala Vada",
    description: "Crispy lentil vada with aromatic spices",
    itemCategory: "Vada",
    slug: "masala-vada",
    subcategories: [
      { title: "4pcs", quantity: "4pcs", price: 45 }
    ],
    image: "/bandiwala-items-pics/items/Rajabundry Vaari special muntha masala/Masala wada.jpeg",
    vendorId: "vendor3",
    isAvailable: true
  },
  {
    _id: "item9",
    itemName: "Kaju Mixture",
    description: "Premium cashew mixture, rich and crunchy",
    itemCategory: "Mixtures",
    slug: "kaju-mixture",
    subcategories: [
      { title: "1 Unit", quantity: "1 Unit", price: 85 }
    ],
    image: "/bandiwala-items-pics/items/Rajabundry Vaari special muntha masala/Mixture/Kaju Mixture.avif",
    vendorId: "vendor3",
    isAvailable: true
  },

  // Sangamesh Bhavani Fast Food items
  {
    _id: "item10",
    itemName: "VEG FRIED RICE",
    description: "Aromatic vegetable fried rice with mixed vegetables",
    itemCategory: "Fried rice",
    slug: "veg-fried-rice",
    subcategories: [
      { title: "Half plate", quantity: "Half plate", price: 65 },
      { title: "Full Plate", quantity: "Full Plate", price: 105 }
    ],
    image: "/bandiwala-items-pics/items/Snagamesh Lkashmi bahavani fast food/veg fried rice.jpg",
    vendorId: "vendor4",
    isAvailable: true
  },
  {
    _id: "item11",
    itemName: "CHICKEN NOODLES",
    description: "Spicy chicken noodles with tender chicken pieces",
    itemCategory: "Noodles",
    slug: "chicken-noodles",
    subcategories: [
      { title: "Half plate", quantity: "Half plate", price: 85 },
      { title: "Full Plate", quantity: "Full Plate", price: 145 }
    ],
    image: "/bandiwala-items-pics/items/Snagamesh Lkashmi bahavani fast food/chicken noodles.jpg",
    vendorId: "vendor4",
    isAvailable: true
  },
  {
    _id: "item12",
    itemName: "CHICKEN 65",
    description: "Famous South Indian chicken 65, spicy and crispy",
    itemCategory: "Manchurian",
    slug: "chicken-65",
    subcategories: [
      { title: "Half plate", quantity: "Half plate", price: 135 },
      { title: "Full Plate", quantity: "Full Plate", price: 225 }
    ],
    image: "/bandiwala-items-pics/items/Snagamesh Lkashmi bahavani fast food/chicken 65.jpg",
    vendorId: "vendor4",
    isAvailable: true
  }
];

export async function GET(request: Request) {
  try {
    console.log('Static menu items API called');

    // Check authentication
    const { isAuthenticated, user } = verifyAuth(request);

    if (!isAuthenticated) {
      console.log('Unauthorized access to menu items API');
      return NextResponse.json({
        success: false,
        message: 'Authentication required to access menu items data'
      }, { status: 401 });
    }

    console.log('Authenticated user accessing menu items:', user?.userId || user?.id);

    return NextResponse.json({
      success: true,
      count: staticMenuItems.length,
      data: staticMenuItems
    });
  } catch (error) {
    console.error('Error in static menu items API:', error);
    return NextResponse.json({
      success: false,
      message: 'Failed to fetch menu items',
      data: []
    }, { status: 500 });
  }
}
