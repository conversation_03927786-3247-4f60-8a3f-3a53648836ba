import { NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';

// Helper function to verify authentication
function verifyAuth(request: Request): { isAuthenticated: boolean; user?: any } {
  try {
    // Check for token in cookies first
    const cookies = request.headers.get('cookie');
    let token = null;

    if (cookies) {
      const tokenMatch = cookies.match(/token=([^;]+)/);
      if (tokenMatch) {
        token = tokenMatch[1];
      }
    }

    // If no token in cookies, check Authorization header
    if (!token) {
      const authHeader = request.headers.get('authorization');
      if (authHeader && authHeader.startsWith('Bearer ')) {
        token = authHeader.split(' ')[1];
      }
    }

    if (!token) {
      return { isAuthenticated: false };
    }

    // Verify the token
    const jwtSecret = process.env.JWT_SECRET || 'bandiwala123dfghadfsjtktfgdsaDGFHGJHKJJDSAFGHJ';
    const decoded = jwt.verify(token, jwtSecret) as any;

    return { isAuthenticated: true, user: decoded };
  } catch (error) {
    console.error('Auth verification error:', error);
    return { isAuthenticated: false };
  }
}

// Static vendor data as fallback
const staticVendors = [
  {
    _id: "vendor1",
    name: "Jai Bhavani Chat Bhandar",
    description: "Your go-to for authentic Indian street food chats & pani puri.",
    slug: "bhavani-street-food",
    rating: 4.5,
    location: "Street Food Corner",
    phone: "+91 98765 43210",
    image: "/bandiwala-items-pics/vendors/jaibhavani.jpeg",
    deliveryTime: "20-30 min",
    deliveryFee: 20,
    minOrderValue: 0,
    isActive: true
  },
  {
    _id: "vendor2",
    name: "BFC Chicken Pakodi Center",
    description: "Specializing in delicious chicken pakodis and a selection of other chicken and fish preparations.",
    slug: "BFC-Chicken-Pakodi-Center",
    rating: 4.3,
    location: "Chicken Corner",
    phone: "+91 87654 32109",
    image: "/bandiwala-items-pics/vendors/bfc.jpeg",
    deliveryTime: "20-30 min",
    deliveryFee: 20,
    minOrderValue: 0,
    isActive: true
  },
  {
    _id: "vendor3",
    name: "Rajahmundry vari Special Muntha Masala",
    description: "Crave local flavors? We offer a wide array of specialty bajjis and unique muntha masala dishes.",
    slug: "Rajahmundry-vari-Special-Muntha-Masala",
    rating: 4.7,
    location: "Bajji Corner",
    phone: "+91 76543 21098",
    image: "/bandiwala-items-pics/vendors/rajamandri.jpeg",
    deliveryTime: "20-30 min",
    deliveryFee: 20,
    minOrderValue: 0,
    isActive: true
  },
  {
    _id: "vendor4",
    name: "Sangamesh Bhavani Fast Food",
    description: "Serving up popular Chinese noodles, fried rice, and more for a quick and satisfying meal.",
    slug: "Sangamesh-Bhavani-Fast-Food",
    rating: 4.4,
    location: "Fast Food Street",
    phone: "+91 65432 10987",
    image: "/bandiwala-items-pics/vendors/sangamesh.jpeg",
    deliveryTime: "20-30 min",
    deliveryFee: 20,
    minOrderValue: 0,
    isActive: true
  }
];

export async function GET(request: Request) {
  try {
    console.log('Static vendors API called');

    // Check authentication
    const { isAuthenticated, user } = verifyAuth(request);

    if (!isAuthenticated) {
      console.log('Unauthorized access to vendors API');
      return NextResponse.json({
        success: false,
        message: 'Authentication required to access vendors data'
      }, { status: 401 });
    }

    console.log('Authenticated user accessing vendors:', user?.userId || user?.id);

    return NextResponse.json({
      success: true,
      count: staticVendors.length,
      data: staticVendors
    });
  } catch (error) {
    console.error('Error in static vendors API:', error);
    return NextResponse.json({
      success: false,
      message: 'Failed to fetch vendors',
      data: []
    }, { status: 500 });
  }
}
