import { NextResponse } from 'next/server';

export async function POST(req: Request) {
  try {
    const { jwt, phone } = await req.json();
    console.log('Phone-email-register request received with phone:', phone);

    if (!jwt || !phone) {
      console.error('Missing JWT or phone number');
      return NextResponse.json(
        { success: false, message: 'JWT and phone number are required' },
        { status: 400 }
      );
    }

    // Log the backend URL we're calling
    const backendUrl = `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:6111'}/api/auth/phone-register`;
    console.log('Calling backend API at:', backendUrl);

    // Call our backend API to register the user
    const response = await fetch(backendUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ jwt, phone }),
    });

    console.log('Backend response status:', response.status);

    if (!response.ok) {
      const errorData = await response.json();
      console.error('Backend registration error:', errorData);
      return NextResponse.json(
        { success: false, message: errorData.message || 'Failed to register user' },
        { status: response.status }
      );
    }

    const data = await response.json();
    console.log('Backend registration success:', data);
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in phone-email-register:', error);
    return NextResponse.json(
      { success: false, message: 'Server error' },
      { status: 500 }
    );
  }
}
