import { NextResponse } from 'next/server';

// In-memory user store for testing
// Using global object to persist between requests
const globalUsers = global as unknown as {
  users?: Record<string, any>;
};

// Initialize users if not already initialized
if (!globalUsers.users) {
  globalUsers.users = {};
}

const users = globalUsers.users;

// Cleanup endpoint to clear all mock users
export async function POST() {
  try {
    console.log('API route /api/auth/cleanup: Clearing all mock users');

    // Clear all users from the in-memory store
    Object.keys(users).forEach(key => {
      delete users[key];
    });

    console.log('API route /api/auth/cleanup: All mock users cleared');

    return NextResponse.json({
      success: true,
      message: 'All mock users cleared successfully'
    });
  } catch (error) {
    console.error('Error in /api/auth/cleanup:', error);
    return NextResponse.json(
      { success: false, message: 'Server error' },
      { status: 500 }
    );
  }
}


