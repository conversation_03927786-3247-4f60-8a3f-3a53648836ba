import { NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';

// In-memory user store for testing
// Using global object to persist between requests
const globalUsers = global as unknown as {
  users?: Record<string, any>;
};

// Initialize users if not already initialized
if (!globalUsers.users) {
  globalUsers.users = {};
}

const users = globalUsers.users;

export async function POST(req: Request) {
  try {
    console.log('API route /api/auth/register: Request received');

    // Parse the request body
    const body = await req.json();
    const { name, phone, email, password, verificationMethod } = body;

    console.log('API route /api/auth/register: Request body:', { name, phone, email, password: '***', verificationMethod });

    if (!phone) {
      console.error('API route /api/auth/register: Phone number is required');
      return NextResponse.json(
        { success: false, message: 'Phone number is required' },
        { status: 400 }
      );
    }

    // Determine backend URL
    const backendUrl = `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:6111'}/api/users/register`;
    console.log('API route /api/auth/register: Calling backend at:', backendUrl);

    // Call backend API to register user
    try {
      const response = await fetch(backendUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify({ name, phone, email, password, verificationMethod })
      });

      console.log('API route /api/auth/register: Backend response status:', response.status);

      const data = await response.json();
      console.log('API route /api/auth/register: Backend response data:', data);

      if (!response.ok) {
        console.error('API route /api/auth/register: Backend returned error:', data);

        // If backend registration fails, use the in-memory user store
        console.log('API route /api/auth/register: Using in-memory user store');

        // Create a user ID from the phone number
        const userId = `user-${phone.replace(/\D/g, '')}`;

        // Check if user already exists
        if (users[userId]) {
          // Generate JWT token for existing user
          const jwtSecret = process.env.JWT_SECRET || 'bandiwala-jwt-secret-key-for-phone-email-auth';
          const token = jwt.sign(
            {
              userId: users[userId]._id,
              phone: users[userId].phone,
              role: 'user'
            },
            jwtSecret,
            { expiresIn: '7d' }
          );

          console.log('API route /api/auth/register: Existing user found, returning token');

          return NextResponse.json({
            success: true,
            data: {
              user: users[userId],
              token
            }
          });
        }

        // Create new user
        const newUser = {
          _id: userId,
          phone,
          email: email || '',
          name: name || '',
          cart: [],
          role: 'user',
          createdAt: new Date(),
          updatedAt: new Date(),
        };

        // Store user
        users[userId] = newUser;

        // Generate JWT token
        const jwtSecret = process.env.JWT_SECRET || 'bandiwala-jwt-secret-key-for-phone-email-auth';
        const token = jwt.sign(
          {
            userId: newUser._id,
            phone: newUser.phone,
            role: newUser.role
          },
          jwtSecret,
          { expiresIn: '7d' }
        );

        console.log('API route /api/auth/register: New user created, returning token');

        return NextResponse.json({
          success: true,
          data: {
            user: newUser,
            token
          }
        }, { status: 201 });
      }

      // Return the backend response
      return NextResponse.json(data);
    } catch (error) {
      console.error('API route /api/auth/register: Error calling backend:', error);

      // If backend call fails, use the in-memory user store
      console.log('API route /api/auth/register: Using in-memory user store');

      // Create a user ID from the phone number
      const userId = `user-${phone.replace(/\D/g, '')}`;

      // Check if user already exists
      if (users[userId]) {
        // Generate JWT token for existing user
        const jwtSecret = process.env.JWT_SECRET || 'bandiwala-jwt-secret-key-for-phone-email-auth';
        const token = jwt.sign(
          {
            userId: users[userId]._id,
            phone: users[userId].phone,
            role: 'user'
          },
          jwtSecret,
          { expiresIn: '7d' }
        );

        console.log('API route /api/auth/register: Existing user found, returning token');

        return NextResponse.json({
          success: true,
          data: {
            user: users[userId],
            token
          }
        });
      }

      // Create new user
      const newUser = {
        _id: userId,
        phone,
        email: email || '',
        name: name || '',
        cart: [],
        role: 'user',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      // Store user
      users[userId] = newUser;

      // Generate JWT token
      const jwtSecret = process.env.JWT_SECRET || 'bandiwala-jwt-secret-key-for-phone-email-auth';
      const token = jwt.sign(
        {
          userId: newUser._id,
          phone: newUser.phone,
          role: newUser.role
        },
        jwtSecret,
        { expiresIn: '7d' }
      );

      console.log('API route /api/auth/register: New user created, returning token');

      return NextResponse.json({
        success: true,
        data: {
          user: newUser,
          token
        }
      }, { status: 201 });
    }
  } catch (error) {
    console.error('Error in /api/auth/register:', error);
    return NextResponse.json(
      { success: false, message: 'Server error' },
      { status: 500 }
    );
  }
}
