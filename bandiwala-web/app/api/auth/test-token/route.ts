import { NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';

// This is a test endpoint that provides a valid token for testing purposes
// In a production environment, this should be removed or secured
export async function GET() {
  try {
    console.log('API route /api/auth/test-token: Generating test token');

    // Create a test user object
    const testUser = {
      _id: 'test-user-id',
      name: 'Test User',
      email: '<EMAIL>',
      phone: '1234567890', // Make sure phone number is included
      address: '123 Test Street, Test City',
      role: 'user'
    };

    // Generate a JWT token
    // Use the same secret as your backend
    const jwtSecret = process.env.JWT_SECRET || 'your-secret-key';
    const token = jwt.sign(
      {
        userId: testUser._id,
        role: testUser.role
      },
      jwtSecret,
      { expiresIn: '24h' }
    );

    console.log('API route /api/auth/test-token: Token generated successfully');

    // Return the token
    return NextResponse.json({
      success: true,
      data: {
        token,
        user: testUser
      }
    });
  } catch (error) {
    console.error('Error in /api/auth/test-token:', error);
    return NextResponse.json(
      { success: false, message: 'Server error' },
      { status: 500 }
    );
  }
}
