import { NextResponse } from "next/server";

export async function GET(request: Request) {
  try {
    console.log('API route /api/test-auth: Request received');

    // Get auth token from cookies or headers
    const cookies = request.headers.get('cookie');
    let token = null;
    
    if (cookies) {
      const tokenMatch = cookies.match(/token=([^;]+)/);
      if (tokenMatch) {
        token = tokenMatch[1];
      }
    }
    
    if (!token) {
      const authHeader = request.headers.get('authorization');
      if (authHeader && authHeader.startsWith('Bearer ')) {
        token = authHeader.split(' ')[1];
      }
    }

    console.log('API route /api/test-auth: Token found:', token ? `${token.substring(0, 10)}...` : 'None');
    console.log('API route /api/test-auth: Cookies:', cookies);
    console.log('API route /api/test-auth: Auth header:', request.headers.get('authorization'));

    if (!token) {
      return NextResponse.json({
        success: false,
        message: 'No authentication token found',
        debug: {
          cookies: cookies,
          authHeader: request.headers.get('authorization'),
          allHeaders: Object.fromEntries(request.headers.entries())
        }
      });
    }

    // Test backend connection
    const backendUrl = `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:6111'}/api/test`;
    console.log('API route /api/test-auth: Testing backend at:', backendUrl);

    const response = await fetch(backendUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': `Bearer ${token}`
      }
    });

    const data = await response.json();
    console.log('API route /api/test-auth: Backend response:', data);

    return NextResponse.json({
      success: true,
      message: 'Authentication test successful',
      token: token ? `${token.substring(0, 10)}...` : 'None',
      backendResponse: data,
      backendStatus: response.status
    });
  } catch (error) {
    console.error('Error in /api/test-auth:', error);
    return NextResponse.json({
      success: false,
      message: 'Server error',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}
