import { NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';

// This is a direct route handler for /api/register
// It handles registration directly without calling the backend

// In-memory user store for testing
// Using global object to persist between requests
const globalUsers = global as unknown as {
  users?: Record<string, any>;
};

// Initialize users if not already initialized
if (!globalUsers.users) {
  globalUsers.users = {};
}

const users = globalUsers.users;

export async function POST(req: Request) {
  try {
    console.log('API route /api/register: Request received');

    // Get the request body
    const body = await req.json();
    const { name, phone, email } = body;

    console.log('API route /api/register: Request body:', { name, phone, email });

    if (!phone) {
      console.error('API route /api/register: Phone number is required');
      return NextResponse.json(
        { success: false, message: 'Phone number is required' },
        { status: 400 }
      );
    }

    // Create a user ID from the phone number
    const userId = `user-${phone.replace(/\D/g, '')}`;

    // Check if user already exists
    if (users[userId]) {
      // Generate JWT token for existing user
      const jwtSecret = process.env.JWT_SECRET || 'bandiwala-jwt-secret-key-for-phone-email-auth';
      const token = jwt.sign(
        {
          userId: users[userId]._id,
          phone: users[userId].phone,
          role: 'user'
        },
        jwtSecret,
        { expiresIn: '7d' }
      );

      console.log('API route /api/register: Existing user found, returning token');

      return NextResponse.json({
        success: true,
        data: {
          user: users[userId],
          token
        }
      });
    }

    // Create new user
    const newUser = {
      _id: userId,
      phone,
      email: email || '',
      name: name || '',
      cart: [],
      role: 'user',
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    // Store user
    users[userId] = newUser;

    // Generate JWT token
    const jwtSecret = process.env.JWT_SECRET || 'bandiwala-jwt-secret-key-for-phone-email-auth';
    const token = jwt.sign(
      {
        userId: newUser._id,
        phone: newUser.phone,
        role: newUser.role
      },
      jwtSecret,
      { expiresIn: '7d' }
    );

    console.log('API route /api/register: New user created, returning token');

    return NextResponse.json({
      success: true,
      data: {
        user: newUser,
        token
      }
    }, { status: 201 });
  } catch (error) {
    console.error('Error in /api/register:', error);
    return NextResponse.json(
      { success: false, message: 'Server error in register' },
      { status: 500 }
    );
  }
}
