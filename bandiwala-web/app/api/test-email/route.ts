import { NextResponse } from 'next/server';
import emailjs from '@emailjs/browser';

export async function GET() {
  try {
    // Get EmailJS configuration
    const serviceId = process.env.NEXT_PUBLIC_EMAILJS_SERVICE_ID;
    const templateId = process.env.NEXT_PUBLIC_EMAILJS_TEMPLATE_ID;
    const userId = process.env.NEXT_PUBLIC_EMAILJS_USER_ID;
    const toEmail = process.env.NEXT_PUBLIC_EMAILJS_TO_EMAIL || '<EMAIL>';

    // Log configuration for debugging
    console.log('EmailJS Configuration:', {
      serviceId,
      templateId,
      userId,
      toEmail
    });

    if (!serviceId || !templateId || !userId) {
      return NextResponse.json({
        success: false,
        message: 'EmailJS configuration is missing',
        config: { serviceId: !!serviceId, templateId: !!templateId, userId: !!userId }
      }, { status: 400 });
    }

    // Sample order data for testing
    const testOrderId = 'TEST-' + Date.now();

    // Sample items for the order
    const testItems = [
      {
        name: 'Test Item 1',
        quantity: 2,
        price: '199.00'
      },
      {
        name: 'Test Item 2',
        quantity: 1,
        price: '299.00'
      }
    ];

    // Format items for the email - create a simple string representation
    const itemsText = testItems.map(item =>
      `${item.name} x${item.quantity} — ₹${item.price}`
    ).join('\n');

    // Calculate totals
    const subtotal = 697; // 2*199 + 1*299
    const platformFee = 20;
    const deliveryCharge = 40;
    const taxes = 35; // 5% of subtotal
    const total = subtotal + platformFee + deliveryCharge + taxes;

    // Sample coordinates
    const coordinates = {
      lat: 17.4485835,
      lng: 78.3908034
    };

    // Create Google Maps URL
    const mapUrl = `https://www.google.com/maps?q=${coordinates.lat},${coordinates.lng}`;

    // Prepare the template parameters to match your Handlebars template
    const templateParams = {
      // EmailJS standard parameters
      to_name: 'Karthik', // Recipient name
      from_name: 'Bandiwala Order System', // Sender name
      reply_to: '<EMAIL>', // Reply-to address
      recipient: toEmail, // Try different parameter name
      email: toEmail, // Try another parameter name
      to_email: toEmail, // Original parameter

      // Order details matching your template variables
      order_id: testOrderId,
      items_text: itemsText, // Plain text representation of items
      address: '123 Test Street, Test City',
      coordinates: `${coordinates.lat}, ${coordinates.lng}`,
      map_url: mapUrl,
      subtotal: subtotal.toFixed(2),
      platform_fee: platformFee.toFixed(2),
      delivery_charge: deliveryCharge.toFixed(2),
      taxes: taxes.toFixed(2),
      total: total.toFixed(2),

      // Shop information
      shop_name: 'Jai Bhavani Chat Bhandar',
      shop_phone: '+91 98765 43210',

      // Additional info
      customer_name: 'Test Customer',
      customer_email: '<EMAIL>',
      phone_number: '+91 98765 43210',
      order_date: new Date().toLocaleDateString('en-IN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      })
    };

    console.log('Sending test email with parameters:', templateParams);

    // Initialize EmailJS
    emailjs.init(userId);

    // Send the email directly
    const response = await emailjs.send(serviceId, templateId, templateParams);

    console.log('EmailJS Response:', response);

    return NextResponse.json({
      success: true,
      message: 'Test email sent successfully',
      details: {
        to: toEmail,
        orderId: testOrderId,
        response
      }
    });
  } catch (error) {
    console.error('Error sending test email:', error);

    // Get detailed error information
    const errorDetails = {
      name: error instanceof Error ? error.name : 'Unknown',
      message: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined
    };

    console.error('Error details:', errorDetails);

    return NextResponse.json({
      success: false,
      message: 'Failed to send test email',
      error: errorDetails
    }, { status: 500 });
  }
}
