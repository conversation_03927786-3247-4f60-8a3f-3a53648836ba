import { NextResponse } from "next/server";

export async function POST(request: Request) {
  try {
    console.log('API route /api/verify: Request received');

    // Parse the request body
    const body = await request.json();

    // Check if this is a payment verification request
    if (body.razorpay_payment_id && body.razorpay_order_id && body.razorpay_signature) {
      console.log('Payment verification request detected');
      console.log('Payment verification body:', {
        razorpay_payment_id: body.razorpay_payment_id,
        razorpay_order_id: body.razorpay_order_id,
        razorpay_signature: body.razorpay_signature ? 'present' : 'missing',
        deliveryAddress: body.deliveryAddress ? 'present' : 'missing',
        promoCode: body.promoCode
      });

      // Get auth token from cookies or headers
      const cookies = request.headers.get('cookie');
      let token = null;

      if (cookies) {
        const tokenMatch = cookies.match(/token=([^;]+)/);
        if (tokenMatch) {
          token = tokenMatch[1];
        }
      }

      if (!token) {
        const authHeader = request.headers.get('authorization');
        if (authHeader && authHeader.startsWith('Bearer ')) {
          token = authHeader.split(' ')[1];
        }
      }

      if (!token) {
        console.error('API route /api/verify: No authentication token found for payment verification');
        console.error('Cookies:', cookies);
        console.error('Auth header:', request.headers.get('authorization'));
        return NextResponse.json(
          { success: false, message: 'Authentication required' },
          { status: 401 }
        );
      }

      console.log('API route /api/verify: Token found for payment verification:', token ? `${token.substring(0, 10)}...` : 'None');

      // Call backend API to verify payment
      const backendUrl = `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000'}/api/payments/verify`;
      console.log('API route /api/verify: Calling backend payment verification at:', backendUrl);

      const response = await fetch(backendUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(body)
      });

      console.log('API route /api/verify: Backend payment response status:', response.status);

      const data = await response.json();
      console.log('API route /api/verify: Backend payment response data:', data);

      return NextResponse.json(data);
    }

    // Handle OTP verification (existing functionality)
    const { email, phone, otp } = body;

    console.log('API route /api/verify: OTP verification request body:', { email, phone, otp: '***' });

    if (!email || !phone || !otp) {
      console.error('API route /api/verify: Email, phone, and OTP are required');
      return NextResponse.json(
        { success: false, message: 'Email, phone, and OTP are required' },
        { status: 400 }
      );
    }

    // Call backend API to verify OTP
    const backendUrl = `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:6111'}/api/users/otp-verification`;
    console.log('API route /api/verify: Calling backend at:', backendUrl);

    const response = await fetch(backendUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify({ email, phone, otp })
    });

    console.log('API route /api/verify: Backend response status:', response.status);

    const data = await response.json();
    console.log('API route /api/verify: Backend response data:', data);

    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in /api/verify:', error);
    return NextResponse.json(
      { success: false, message: 'Server error' },
      { status: 500 }
    );
  }
}