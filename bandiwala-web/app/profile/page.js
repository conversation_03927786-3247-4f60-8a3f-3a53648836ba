'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'sonner';
import Image from 'next/image';
import PickableMap from '@/components/maps/PickableMap';
import useUserLocation from '@/hooks/useUserLocation';
import PhotoUpload from '@/components/PhotoUpload';
import { BackButton } from '@/components/ui/back-button';
import { reviewService } from '@/services/api';
import UserReviews from '@/components/reviews/UserReviews';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';

function ProfilePage() {
  const router = useRouter();
  const { user, isAuthenticated, isLoading, logout, updateUser } = useAuth();

  const [isEditing, setIsEditing] = useState(false);
  const [isEditingAddress, setIsEditingAddress] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isUploadingPhoto, setIsUploadingPhoto] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [activeTab, setActiveTab] = useState('profile');
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    address: ''
  });
  const [addressFormData, setAddressFormData] = useState({
    address: ''
  });

  const {
    coordinates,
    formattedAddress,
    loading: locationLoading,
    error: locationError,
    getCurrentLocation,
    updateLocation,
    saveLocation
  } = useUserLocation();

  useEffect(() => {
    console.log('Profile page: Auth state check', { isLoading, isAuthenticated, user });

    // If not loading and not authenticated, redirect to login
    if (!isLoading && !isAuthenticated) {
      console.log('Profile page: Not authenticated, redirecting to login');
      router.push('/login');
    }
  }, [isLoading, isAuthenticated, router]);

  // Helper function to clean up user data
  const cleanUserData = (userData) => {
    if (!userData) return null;

    // Clean up email - remove placeholder emails
    let cleanEmail = userData.email || '';
    if (cleanEmail.includes('@bandiwala.com') || cleanEmail.includes('@example.com') || cleanEmail.includes('user-')) {
      cleanEmail = '';
    }

    // Clean up name - provide better default
    let cleanName = userData.name || '';
    if (cleanName === 'User' || cleanName.startsWith('user-') || cleanName.startsWith('User ') || !cleanName.trim()) {
      cleanName = '';
    }

    return {
      ...userData,
      email: cleanEmail,
      name: cleanName
    };
  };

  // Update form data when user data is loaded
  useEffect(() => {
    if (user) {
      console.log('Profile page: Setting form data from user:', user);
      const cleanedUser = cleanUserData(user);
      setFormData({
        name: cleanedUser.name || '',
        email: cleanedUser.email || '',
        address: cleanedUser.address || ''
      });

      // Initialize address form data
      setAddressFormData({
        address: cleanedUser.address || ''
      });

      // If the user has a saved location, use it
      if (cleanedUser.location?.formattedAddress) {
        setFormData(prev => ({
          ...prev,
          address: cleanedUser.location.formattedAddress
        }));
        setAddressFormData({
          address: cleanedUser.location.formattedAddress
        });
      }
    }
  }, [user]);

  // Update address when formattedAddress changes
  useEffect(() => {
    if (formattedAddress && (isEditing || isEditingAddress)) {
      setFormData(prev => ({
        ...prev,
        address: formattedAddress
      }));
      setAddressFormData({
        address: formattedAddress
      });
    }
  }, [formattedAddress, isEditing, isEditingAddress]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleAddressChange = (e) => {
    const { value } = e.target;
    setAddressFormData({
      address: value
    });
  };

  const handleEdit = () => {
    setIsEditing(true);
    setError('');
    setSuccess('');
  };

  const handleEditAddress = () => {
    setIsEditingAddress(true);
    setError('');
    setSuccess('');
  };

  const handleCancel = () => {
    setIsEditing(false);
    // Reset form data to original user data
    if (user) {
      const cleanedUser = cleanUserData(user);
      setFormData({
        name: cleanedUser.name || '',
        email: cleanedUser.email || '',
        address: cleanedUser.address || ''
      });
    }
  };

  const handleCancelAddress = () => {
    setIsEditingAddress(false);
    // Reset address form data to original user data
    if (user) {
      setAddressFormData({
        address: user.address || ''
      });
      if (user.location?.formattedAddress) {
        setAddressFormData({
          address: user.location.formattedAddress
        });
      }
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSaving(true);
    setError('');
    setSuccess('');

    try {
      // Validate form
      if (!formData.name.trim()) {
        throw new Error('Name is required');
      }

      if (formData.email && !/^\S+@\S+\.\S+$/.test(formData.email)) {
        throw new Error('Invalid email format');
      }

      // Save location data if available
      if (coordinates) {
        await saveLocation();
      }

      // Call update profile API
      await updateUser({
        name: formData.name,
        email: formData.email,
        address: formData.address,
        location: coordinates ? {
          coordinates,
          formattedAddress: formattedAddress || formData.address
        } : undefined
      });

      setSuccess('Profile updated successfully!');
      toast.success('Profile updated successfully!');
      setIsEditing(false);
    } catch (err) {
      console.error('Error updating profile:', err);
      setError(err.message || 'Failed to update profile');
      toast.error(err.message || 'Failed to update profile');
    } finally {
      setIsSaving(false);
    }
  };

  const handleSubmitAddress = async (e) => {
    e.preventDefault();
    setIsSaving(true);
    setError('');
    setSuccess('');

    try {
      // Validate address
      if (!addressFormData.address.trim()) {
        throw new Error('Address is required');
      }

      // Save location data if available
      if (coordinates) {
        await saveLocation();
      }

      // Call update profile API with address
      await updateUser({
        address: addressFormData.address,
        location: coordinates ? {
          coordinates,
          formattedAddress: formattedAddress || addressFormData.address
        } : undefined
      });

      setSuccess('Address updated successfully!');
      toast.success('Address updated successfully!');
      setIsEditingAddress(false);
    } catch (err) {
      console.error('Error updating address:', err);
      setError(err.message || 'Failed to update address');
      toast.error(err.message || 'Failed to update address');
    } finally {
      setIsSaving(false);
    }
  };

  const handlePhotoUploadSuccess = async (imageUrl) => {
    try {
      setIsUploadingPhoto(true);
      // Update user profile with new image
      await updateUser({
        profileImage: imageUrl
      });
      setSuccess('Profile photo updated successfully!');
      toast.success('Profile photo updated successfully!');
    } catch (err) {
      console.error('Error updating profile photo:', err);
      setError('Failed to update profile photo');
      toast.error('Failed to update profile photo');
    } finally {
      setIsUploadingPhoto(false);
    }
  };

  const handleLogout = async () => {
    try {
      await logout();
      toast.success('Logged out successfully');
      router.push('/');
    } catch (err) {
      console.error('Error logging out:', err);
      setError('Failed to logout. Please try again.');
      toast.error('Failed to logout. Please try again.');
    }
  };

  // Show loading state
  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen bg-gray-50">
        <div className="animate-spin rounded-full h-16 w-16 border-b-4 border-green-600 mb-4"></div>
        <p className="text-gray-600 font-medium">Loading your profile...</p>
      </div>
    );
  }

  // If not authenticated, redirect to login (handled by useEffect)
  if (!isAuthenticated || !user) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen bg-gray-50">
        <div className="animate-spin rounded-full h-16 w-16 border-b-4 border-green-600 mb-4"></div>
        <p className="text-gray-600 font-medium">Redirecting to login...</p>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4 max-w-4xl">
        {/* Back Button */}
        <div className="mb-6">
          <BackButton text="Back to Home" href="/" />
        </div>
        <div className="bg-white rounded-xl shadow-lg overflow-hidden">
          {/* Profile Header */}
          <div className="relative h-48 bg-gradient-to-r from-green-500 to-green-700">
            <div className="absolute -bottom-16 left-8">
              <PhotoUpload
                currentImage={user.profileImage}
                onUploadSuccess={handlePhotoUploadSuccess}
                isUploading={isUploadingPhoto}
              />
            </div>
          </div>

          {/* Profile Navigation */}
          <div className="pt-20 px-8 pb-4 border-b border-gray-200">
            <div className="flex justify-between items-center">
              <div>
                <h1 className="text-2xl font-bold text-gray-800">
                <p className="text-gray-600">{user.phone || ''}</p>
                  {cleanUserData(user)?.name || 'Welcome!'}
                </h1>
              </div>
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <button className="px-4 py-2 border border-red-300 text-red-600 rounded-md hover:bg-red-50 transition">
                    Logout
                  </button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Confirm Logout</AlertDialogTitle>
                    <AlertDialogDescription>
                      Are you sure you want to logout? You will need to login again to access your account.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction
                      onClick={handleLogout}
                      className="bg-red-500 hover:bg-red-600 text-white"
                    >
                      Logout
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </div>

            <div className="flex space-x-6 mt-6">
              <button
                onClick={() => setActiveTab('profile')}
                className={`pb-2 font-medium ${activeTab === 'profile'
                  ? 'text-green-600 border-b-2 border-green-600'
                  : 'text-gray-500 hover:text-gray-700'}`}
              >
                Profile
              </button>
              <button
                onClick={() => setActiveTab('orders')}
                className={`pb-2 font-medium ${activeTab === 'orders'
                  ? 'text-green-600 border-b-2 border-green-600'
                  : 'text-gray-500 hover:text-gray-700'}`}
              >
                Orders
              </button>
              <button
                onClick={() => setActiveTab('addresses')}
                className={`pb-2 font-medium ${activeTab === 'addresses'
                  ? 'text-green-600 border-b-2 border-green-600'
                  : 'text-gray-500 hover:text-gray-700'}`}
              >
                Addresses
              </button>
              <button
                onClick={() => setActiveTab('reviews')}
                className={`pb-2 font-medium ${activeTab === 'reviews'
                  ? 'text-green-600 border-b-2 border-green-600'
                  : 'text-gray-500 hover:text-gray-700'}`}
              >
                My Reviews
              </button>
            </div>
          </div>

          {/* Profile Content */}
          <div className="p-8">
            {error && (
              <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
                {error}
              </div>
            )}

            {success && (
              <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
                {success}
              </div>
            )}

            {/* Profile Completion Prompt */}
            {activeTab === 'profile' && !isEditing && user && (
              (() => {
                const cleanedUser = cleanUserData(user);
                const isIncomplete = !cleanedUser.name || !cleanedUser.email;

                if (isIncomplete) {
                  return (
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                      <div className="flex items-start">
                        <div className="flex-shrink-0">
                          <svg className="h-5 w-5 text-blue-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                          </svg>
                        </div>
                        <div className="ml-3 flex-1">
                          <h3 className="text-sm font-medium text-blue-800">Complete Your Profile</h3>
                          <div className="mt-1 text-sm text-blue-700">
                            <p>Please complete your profile information to get the best experience.</p>
                            <ul className="list-disc list-inside mt-2 space-y-1">
                              {!cleanedUser.name && <li>Add your full name</li>}
                              {!cleanedUser.email && <li>Add your email address</li>}
                            </ul>
                          </div>
                          <div className="mt-3">
                            <button
                              onClick={handleEdit}
                              className="text-sm bg-blue-600 text-white px-3 py-1 rounded hover:bg-blue-700 transition"
                            >
                              Complete Profile
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                }
                return null;
              })()
            )}

            {activeTab === 'profile' && (
              <div className="bg-white rounded-lg">
                <div className="flex justify-between items-center mb-6">
                  <h2 className="text-xl font-semibold text-gray-800">Personal Information</h2>
                  {!isEditing && (
                    <button
                      type="button"
                      onClick={handleEdit}
                      className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition"
                    >
                      Edit Profile
                    </button>
                  )}
                </div>

                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Phone Number</label>
                      <div className="border border-gray-300 rounded-md px-4 py-3 bg-gray-50 text-gray-700">
                        {user.phone || 'Not provided'}
                      </div>
                      <p className="text-xs text-gray-500 mt-1">Phone number cannot be changed</p>
                    </div>

                    <div>
                      <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">Full Name</label>
                      <input
                        type="text"
                        id="name"
                        name="name"
                        value={formData.name}
                        onChange={handleChange}
                        disabled={!isEditing}
                        className={`w-full px-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500 ${!isEditing ? 'bg-gray-50' : ''}`}
                        placeholder="Enter your full name"
                      />
                    </div>

                    <div>
                      <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">Email Address</label>
                      <input
                        type="email"
                        id="email"
                        name="email"
                        value={formData.email}
                        onChange={handleChange}
                        disabled={!isEditing}
                        className={`w-full px-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500 ${!isEditing ? 'bg-gray-50' : ''}`}
                        placeholder="Enter your email address"
                      />
                    </div>

                    <div className="md:col-span-2">
                      <label htmlFor="address" className="block text-sm font-medium text-gray-700 mb-1">Delivery Address</label>
                      <textarea
                        id="address"
                        name="address"
                        value={formData.address}
                        onChange={handleChange}
                        disabled={!isEditing}
                        rows="3"
                        className={`w-full px-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500 ${!isEditing ? 'bg-gray-50' : ''}`}
                        placeholder="Enter your delivery address"
                      ></textarea>

                      {isEditing && (
                        <div className="mt-4">
                          <p className="text-sm font-medium text-gray-700 mb-2">Pick your location on the map</p>
                          <div className="h-64 rounded-md overflow-hidden">
                            <PickableMap
                              initialCoordinates={coordinates || { lat: 17.3850, lng: 78.4867 }}
                              onCoordinatesChange={updateLocation}
                              showCurrentLocationButton={true}
                              onGetCurrentLocation={getCurrentLocation}
                              isLoading={locationLoading}
                              height="100%"
                            />
                          </div>
                          {locationError && (
                            <p className="text-sm text-red-600 mt-1">{locationError}</p>
                          )}
                          <p className="text-xs text-gray-500 mt-1">
                            Click on the map to set your location or use the "Use My Location" button
                          </p>
                        </div>
                      )}
                    </div>
                  </div>

                  {isEditing && (
                    <div className="flex justify-end space-x-4 pt-4">
                      <button
                        type="button"
                        onClick={handleCancel}
                        className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition"
                      >
                        Cancel
                      </button>
                      <button
                        type="submit"
                        disabled={isSaving}
                        className="px-6 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 transition"
                      >
                        {isSaving ? 'Saving...' : 'Save Changes'}
                      </button>
                    </div>
                  )}
                </form>
              </div>
            )}

            {activeTab === 'orders' && (
              <div className="bg-white rounded-lg">
                <div className="flex justify-between items-center mb-6">
                  <h2 className="text-xl font-semibold text-gray-800">Order History</h2>
                  <Link href="/orders" className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition">
                    View All Orders
                  </Link>
                </div>
                <div className="bg-gray-50 rounded-lg p-8 text-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 text-gray-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                  </svg>
                  <h3 className="text-lg font-medium text-gray-700 mb-2">View your order history</h3>
                  <p className="text-gray-500 mb-4">Track your orders, view order details, and more.</p>
                  <Link href="/orders" className="inline-block px-6 py-3 bg-green-600 text-white rounded-md hover:bg-green-700 transition">
                    Go to Orders
                  </Link>
                </div>
              </div>
            )}

            {activeTab === 'addresses' && (
              <div className="bg-white rounded-lg">
                <div className="flex justify-between items-center mb-6">
                  <h2 className="text-xl font-semibold text-gray-800">Saved Addresses</h2>
                  {!isEditingAddress && user.address && (
                    <button
                      type="button"
                      onClick={handleEditAddress}
                      className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition"
                    >
                      Edit Address
                    </button>
                  )}
                </div>

                {!isEditingAddress ? (
                  // Display mode
                  user.address ? (
                    <div className="border border-gray-200 rounded-lg p-4 mb-4">
                      <div className="flex justify-between items-start">
                        <div>
                          <h3 className="font-medium text-gray-800">Default Address</h3>
                          <p className="text-gray-600 mt-1">{user.address}</p>
                          {user.location?.coordinates && (
                            <p className="text-sm text-gray-500 mt-1">
                              Coordinates: {user.location.coordinates.lat.toFixed(6)}, {user.location.coordinates.lng.toFixed(6)}
                            </p>
                          )}
                        </div>
                        <button
                          onClick={handleEditAddress}
                          className="text-green-600 hover:text-green-700"
                        >
                          Edit
                        </button>
                      </div>
                    </div>
                  ) : (
                    <div className="bg-gray-50 rounded-lg p-8 text-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 text-gray-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                      </svg>
                      <h3 className="text-lg font-medium text-gray-700 mb-2">No addresses saved</h3>
                      <p className="text-gray-500 mb-4">Add an address to make checkout faster.</p>
                      <button
                        onClick={handleEditAddress}
                        className="inline-block px-6 py-3 bg-green-600 text-white rounded-md hover:bg-green-700 transition"
                      >
                        Add Address
                      </button>
                    </div>
                  )
                ) : (
                  // Edit mode
                  <form onSubmit={handleSubmitAddress} className="space-y-6">
                    <div>
                      <label htmlFor="addressField" className="block text-sm font-medium text-gray-700 mb-1">Delivery Address</label>
                      <textarea
                        id="addressField"
                        name="address"
                        value={addressFormData.address}
                        onChange={handleAddressChange}
                        rows="3"
                        className="w-full px-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500"
                        placeholder="Enter your delivery address"
                      ></textarea>

                      <div className="mt-4">
                        <p className="text-sm font-medium text-gray-700 mb-2">Pick your location on the map</p>
                        <div className="h-64 rounded-md overflow-hidden">
                          <PickableMap
                            initialCoordinates={coordinates || { lat: 17.3850, lng: 78.4867 }}
                            onCoordinatesChange={updateLocation}
                            showCurrentLocationButton={true}
                            onGetCurrentLocation={getCurrentLocation}
                            isLoading={locationLoading}
                            height="100%"
                          />
                        </div>
                        {locationError && (
                          <p className="text-sm text-red-600 mt-1">{locationError}</p>
                        )}
                        {formattedAddress && (
                          <p className="text-sm text-green-600 mt-1">
                            Selected location: {formattedAddress}
                          </p>
                        )}
                        <p className="text-xs text-gray-500 mt-1">
                          Click on the map to set your location or use the "Use My Location" button
                        </p>
                      </div>
                    </div>

                    <div className="flex justify-end space-x-4 pt-4">
                      <button
                        type="button"
                        onClick={handleCancelAddress}
                        className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition"
                      >
                        Cancel
                      </button>
                      <button
                        type="submit"
                        disabled={isSaving}
                        className="px-6 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 transition"
                      >
                        {isSaving ? 'Saving...' : 'Save Address'}
                      </button>
                    </div>
                  </form>
                )}
              </div>
            )}

            {activeTab === 'reviews' && (
              <div className="bg-white rounded-lg">
                <div className="mb-6">
                  <h2 className="text-xl font-semibold text-gray-800">My Reviews</h2>
                  <p className="text-gray-600 mt-1">Manage your reviews and see your rating history</p>
                </div>
                <UserReviews userId={user._id} />
              </div>
            )}

            <div className="mt-8 pt-6 border-t border-gray-200">
              <h2 className="text-lg font-medium mb-4">Account Actions</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Link
                  href="/orders"
                  className="block text-center px-4 py-3 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition"
                >
                  My Orders
                </Link>
                <Link
                  href="/forgot-password"
                  className="block text-center px-4 py-3 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition"
                >
                  Reset Password
                </Link>
                <Link
                  href="/"
                  className="block text-center px-4 py-3 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition"
                >
                  Back to Home
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Make sure this is the default export
const Page = ProfilePage;
export default Page;
