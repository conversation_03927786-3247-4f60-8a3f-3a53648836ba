'use client';

import { toast } from 'sonner';
import { Button } from '@/components/ui/button';

export default function TestToastPage() {
  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-4">
      <h1 className="text-2xl font-bold mb-8">Toast Test Page</h1>
      
      <div className="flex flex-col space-y-4 w-full max-w-md">
        <Button 
          onClick={() => {
            toast.success('Cart cleared', {
              description: 'Your cart has been successfully cleared',
            });
          }}
          className="w-full bg-green-500 hover:bg-green-600"
        >
          Test Cart Clear Toast (Success)
        </Button>
        
        <Button 
          onClick={() => {
            toast.error('Error', {
              description: 'Failed to clear cart. Please try again.',
            });
          }}
          className="w-full bg-red-500 hover:bg-red-600"
        >
          Test Cart Clear Toast (Error)
        </Button>
        
        <Button 
          onClick={() => {
            toast.success('Promo code applied', {
              description: 'You got 10% off your order!',
            });
          }}
          className="w-full bg-blue-500 hover:bg-blue-600"
        >
          Test Promo Code Toast
        </Button>
        
        <Button 
          onClick={() => {
            toast.info('Add more items', {
              description: 'This would navigate to vendor page in a real app.',
            });
          }}
          className="w-full bg-yellow-500 hover:bg-yellow-600"
        >
          Test Info Toast
        </Button>
      </div>
    </div>
  );
}
