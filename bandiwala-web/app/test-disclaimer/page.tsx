'use client';

import React, { useState, useEffect } from 'react';
import Header from '@/components/Header';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Clock, AlertTriangle, CheckCircle, XCircle } from 'lucide-react';

const TestDisclaimerPage: React.FC = () => {
  const [currentTime, setCurrentTime] = useState<string>('');
  const [isServiceHours, setIsServiceHours] = useState<boolean>(false);
  const [isClient, setIsClient] = useState(false);
  const [testMode, setTestMode] = useState<'real' | 'service' | 'outside'>('real');

  // Function to check if current time is within service hours (5 PM - 9 PM IST)
  const checkServiceHours = () => {
    const now = new Date();

    // Get current time in IST (UTC+5:30)
    const istTime = new Intl.DateTimeFormat('en-US', {
      timeZone: 'Asia/Kolkata',
      hour: 'numeric',
      hour12: false
    }).formatToParts(now);

    const hourPart = istTime.find(part => part.type === 'hour');
    const currentHour = hourPart ? parseInt(hourPart.value, 10) : 0;

    // Service hours: 17:00 (5 PM) to 21:00 (9 PM) IST
    return currentHour >= 17 && currentHour < 21;
  };

  const updateTime = () => {
    const now = new Date();
    const istTime = new Date(now.toLocaleString("en-US", {timeZone: "Asia/Kolkata"}));

    setCurrentTime(istTime.toLocaleString('en-IN', {
      timeZone: 'Asia/Kolkata',
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: true
    }));

    if (testMode === 'real') {
      setIsServiceHours(checkServiceHours());
    }
  };

  useEffect(() => {
    setIsClient(true);
    updateTime();

    // Update every second for real-time display
    const interval = setInterval(updateTime, 1000);

    return () => clearInterval(interval);
  }, [testMode]);

  const handleTestMode = (mode: 'real' | 'service' | 'outside') => {
    setTestMode(mode);
    if (mode === 'service') {
      setIsServiceHours(true);
    } else if (mode === 'outside') {
      setIsServiceHours(false);
    } else {
      setIsServiceHours(checkServiceHours());
    }
  };

  if (!isClient) {
    return <div>Loading...</div>;
  }

  const getStatusBadge = () => {
    if (testMode === 'service') {
      return <Badge className="bg-green-100 text-green-800 border-green-300">🟢 TEST: Service Hours</Badge>;
    } else if (testMode === 'outside') {
      return <Badge className="bg-red-100 text-red-800 border-red-300">🔴 TEST: Outside Hours</Badge>;
    } else {
      return isServiceHours
        ? <Badge className="bg-green-100 text-green-800 border-green-300">🟢 REAL: Service Hours</Badge>
        : <Badge className="bg-red-100 text-red-800 border-red-300">🔴 REAL: Outside Hours</Badge>;
    }
  };

  const shouldShowBanner = testMode === 'outside' || (testMode === 'real' && !isServiceHours);

  return (
    <div className="min-h-screen bg-gray-50">
      <Header showBackButton={true} backButtonHref="/" />

      <div className="max-w-6xl mx-auto px-4 py-8">
        <div className="space-y-6">
          {/* Header */}
          <Card>
            <CardHeader className="text-center">
              <CardTitle className="text-3xl font-bold text-gray-900 flex items-center justify-center gap-3">
                <AlertTriangle className="h-8 w-8 text-yellow-600" />
                Disclaimer Banner Test Center
              </CardTitle>
              <CardDescription className="text-lg">
                Test all disclaimer scenarios and banner visibility states
              </CardDescription>
            </CardHeader>
          </Card>

          {/* Current Status */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-5 w-5" />
                Current Status
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-gray-600">Current IST Time</p>
                  <p className="text-xl font-mono text-blue-700">{currentTime}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600">Banner Status</p>
                  <div className="flex items-center gap-2">
                    {getStatusBadge()}
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 p-4 rounded-lg">
                <p className="text-sm text-gray-700">
                  <span className="font-semibold">Service Hours:</span> 5:00 PM - 9:00 PM IST
                </p>
                <p className="text-sm text-gray-700 mt-1">
                  <span className="font-semibold">Banner Logic:</span> Shows when service is UNAVAILABLE (outside 5-9 PM)
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Test Controls */}
          <Card>
            <CardHeader>
              <CardTitle>Test Controls</CardTitle>
              <CardDescription>
                Switch between different scenarios to test banner behavior
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Button
                  onClick={() => handleTestMode('real')}
                  variant={testMode === 'real' ? 'default' : 'outline'}
                  className="h-auto p-4 flex flex-col items-center gap-2"
                >
                  <Clock className="h-6 w-6" />
                  <div className="text-center">
                    <div className="font-semibold">Real Time</div>
                    <div className="text-xs opacity-75">Use actual IST time</div>
                  </div>
                </Button>

                <Button
                  onClick={() => handleTestMode('service')}
                  variant={testMode === 'service' ? 'default' : 'outline'}
                  className="h-auto p-4 flex flex-col items-center gap-2"
                >
                  <CheckCircle className="h-6 w-6 text-green-600" />
                  <div className="text-center">
                    <div className="font-semibold">Service Hours</div>
                    <div className="text-xs opacity-75">Simulate 5-9 PM</div>
                  </div>
                </Button>

                <Button
                  onClick={() => handleTestMode('outside')}
                  variant={testMode === 'outside' ? 'default' : 'outline'}
                  className="h-auto p-4 flex flex-col items-center gap-2"
                >
                  <XCircle className="h-6 w-6 text-red-600" />
                  <div className="text-center">
                    <div className="font-semibold">Outside Hours</div>
                    <div className="text-xs opacity-75">Simulate unavailable</div>
                  </div>
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Banner Preview */}
          <Card>
            <CardHeader>
              <CardTitle>Banner Preview</CardTitle>
              <CardDescription>
                This shows what the banner looks like in different states
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Service Hours Banner (when outside hours) */}
              <div>
                <h4 className="font-semibold mb-2">Service Hours Banner (Outside 5-9 PM):</h4>
                {shouldShowBanner ? (
                  <div className="w-full bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200 rounded-lg">
                    <div className="px-4 py-3">
                      <div className="flex items-center justify-center gap-2 text-center">
                        <AlertTriangle className="h-4 w-4 text-yellow-600 flex-shrink-0" />
                        <p className="text-sm md:text-base font-bold text-yellow-800">
                          ⚠️ Note: Our service is available only between 5:00 PM and 9:00 PM. Please place your orders during this time. ⚠️
                        </p>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="bg-green-50 border border-green-200 rounded-lg p-4 text-center">
                    <p className="text-green-700 font-medium">✅ Banner Hidden (Service Available)</p>
                  </div>
                )}
              </div>

              {/* Cart Page Banner */}
              <div>
                <h4 className="font-semibold mb-2">Cart Page Banner (Only Outside Service Hours):</h4>
                {shouldShowBanner ? (
                  <div className="w-full bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg">
                    <div className="px-4 py-3">
                      <div className="flex items-start justify-center gap-3 text-center">
                        <AlertTriangle className="h-4 w-4 text-blue-600 flex-shrink-0 mt-0.5" />
                        <p className="text-xs md:text-sm font-medium text-blue-800 leading-relaxed">
                          <span className="font-semibold">📝 Note before ordering:</span> The items added to your cart are subject to availability.
                          In case of item unavailability, our representative will contact you shortly after you place your order.
                          Thank you for your understanding. We're working on launching our app soon to provide you with a better experience.
                        </p>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="bg-green-50 border border-green-200 rounded-lg p-4 text-center">
                    <p className="text-green-700 font-medium">✅ Cart Banner Hidden (Service Available)</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Test Links */}
          <Card>
            <CardHeader>
              <CardTitle>Test Different Pages</CardTitle>
              <CardDescription>
                Visit different pages to see how disclaimers appear
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <Button asChild variant="outline">
                  <a href="/" target="_blank">Home Page</a>
                </Button>
                <Button asChild variant="outline">
                  <a href="/dishes" target="_blank">Dishes Page</a>
                </Button>
                <Button asChild variant="outline">
                  <a href="/vendors" target="_blank">Vendors Page</a>
                </Button>
                <Button asChild variant="outline">
                  <a href="/cart" target="_blank">Cart Page</a>
                </Button>
              </div>
              <p className="text-sm text-gray-600 mt-4">
                💡 <strong>Tip:</strong> Open these in new tabs to see the actual banner behavior on each page.
                <strong>During service hours (5-9 PM IST):</strong> NO disclaimers will appear on any page.
                <strong>Outside service hours:</strong> Warning disclaimers will appear on all pages including cart.
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default TestDisclaimerPage;
