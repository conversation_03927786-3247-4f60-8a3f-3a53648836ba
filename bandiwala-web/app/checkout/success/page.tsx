'use client';

import React, { useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { CheckCircle, ArrowRight, ShoppingBag, ClipboardList } from 'lucide-react';
import { toast } from 'sonner';

export default function CheckoutSuccessPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const orderId = searchParams.get('orderId');

  useEffect(() => {
    if (!orderId) {
      toast.info('No order information found. Redirecting to orders page.');
      setTimeout(() => {
        router.push('/orders');
      }, 3000);
    }
  }, [orderId, router]);

  return (
    <div className="container mx-auto px-4 py-12 max-w-4xl">
      <div className="bg-white rounded-lg shadow-sm p-8 text-center">
        <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
          <CheckCircle className="h-10 w-10 text-green-600" />
        </div>
        
        <h1 className="text-2xl font-bold mb-2">Order Placed!</h1>
        
        {orderId ? (
          <p className="text-gray-600 mb-6">
            Your order <span className="font-medium">#{orderId.slice(-8)}</span> has been placed successfully.
          </p>
        ) : (
          <p className="text-gray-600 mb-6">
            Your order has been placed successfully.
          </p>
        )}
        
        <p className="text-gray-600 mb-8">
          We've sent a confirmation email with your order details.
          You can track your order status in the Orders section.
        </p>
        
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          {orderId ? (
            <Link href={`/orders/${orderId}`}>
              <Button className="w-full sm:w-auto">
                View Order Details
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </Link>
          ) : null}
          
          <Link href="/orders">
            <Button variant="outline" className="w-full sm:w-auto">
              <ClipboardList className="mr-2 h-4 w-4" />
              My Orders
            </Button>
          </Link>
          
          <Link href="/">
            <Button variant="outline" className="w-full sm:w-auto">
              <ShoppingBag className="mr-2 h-4 w-4" />
              Continue Shopping
            </Button>
          </Link>
        </div>
      </div>
    </div>
  );
}
