'use client';

import { useState } from 'react';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import PickableMap from '@/components/maps/PickableMap';
import useUserLocation from '@/hooks/useUserLocation';
import { calculateDistance, isWithinAllowedDistance } from '@/utils/distance';

interface Coordinates {
  lat: number;
  lng: number;
}

export default function ToastTestPage() {
  const [selectedCoords, setSelectedCoords] = useState<Coordinates | null>(null);
  const {
    coordinates,
    formattedAddress,
    loading,
    error,
    isWithinServiceArea,
    getCurrentLocation,
    updateLocation
  } = useUserLocation();

  const testLocations = [
    { name: 'Kukatpally (Service Area)', coords: { lat: 17.4933, lng: 78.3915 } },
    { name: 'VNR Hostel (Service Area)', coords: { lat: 17.538779, lng: 78.395689 } },
    { name: 'Hyderabad City Center (Outside)', coords: { lat: 17.385044, lng: 78.486671 } },
    { name: 'Banjara Hills (Outside)', coords: { lat: 17.4375, lng: 78.4452 } }
  ];

  const handleTestLocation = (testCoords: Coordinates, name: string) => {
    const distance = calculateDistance(testCoords, { lat: 17.4933, lng: 78.3915 });
    const withinArea = isWithinAllowedDistance(testCoords, undefined, 10);

    toast(`Testing ${name}`, {
      description: `Distance: ${distance.toFixed(2)}km | Within Service Area: ${withinArea ? 'Yes' : 'No'}`,
      duration: 5000
    });

    setSelectedCoords(testCoords);
    updateLocation(testCoords);
  };

  const handleMapCoordinateChange = (coords: Coordinates) => {
    setSelectedCoords(coords);
    const distance = calculateDistance(coords, { lat: 17.4933, lng: 78.3915 });
    const withinArea = isWithinAllowedDistance(coords, undefined, 10);

    console.log('Map coordinates changed:', {
      coords,
      distance: distance.toFixed(2) + 'km',
      withinServiceArea: withinArea
    });
  };

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <h1 className="text-3xl font-bold mb-6">Location & Map Testing</h1>

      {/* Current Location Status */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4">Current Location Status</h2>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <p><strong>Coordinates:</strong> {coordinates ? `${coordinates.lat.toFixed(6)}, ${coordinates.lng.toFixed(6)}` : 'Not set'}</p>
            <p><strong>Address:</strong> {formattedAddress || 'Not available'}</p>
            <p><strong>Within Service Area:</strong>
              <span className={`ml-2 px-2 py-1 rounded text-sm ${isWithinServiceArea ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                {isWithinServiceArea ? 'Yes' : 'No'}
              </span>
            </p>
            <p><strong>Loading:</strong> {loading ? 'Yes' : 'No'}</p>
            {error && <p className="text-red-600"><strong>Error:</strong> {error}</p>}
          </div>

          <div className="space-y-2">
            <Button
              onClick={() => {
                console.log('Test page: Get My Current Location button clicked');
                console.log('getCurrentLocation function:', getCurrentLocation);
                console.log('Current loading state:', loading);
                getCurrentLocation();
              }}
              disabled={loading}
              className="w-full"
            >
              {loading ? 'Getting Location...' : 'Get My Current Location'}
            </Button>

            <Button
              onClick={() => {
                console.log('Testing direct geolocation...');
                if (navigator.geolocation) {
                  navigator.geolocation.getCurrentPosition(
                    (position) => {
                      console.log('Direct geolocation success:', position);
                      toast.success(`Direct location: ${position.coords.latitude.toFixed(6)}, ${position.coords.longitude.toFixed(6)}`);
                    },
                    (error) => {
                      console.error('Direct geolocation error:', error);
                      toast.error(`Direct geolocation failed: ${error.message}`);
                    },
                    { enableHighAccuracy: true, timeout: 10000, maximumAge: 0 }
                  );
                } else {
                  toast.error('Geolocation not supported');
                }
              }}
              variant="outline"
              className="w-full"
            >
              Test Direct Geolocation
            </Button>
          </div>
        </div>
      </div>

      {/* Test Locations */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4">Test Locations</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          {testLocations.map((location, index) => (
            <Button
              key={index}
              onClick={() => handleTestLocation(location.coords, location.name)}
              variant="outline"
              className="text-left justify-start"
            >
              {location.name}
            </Button>
          ))}
        </div>
      </div>

      {/* Interactive Map */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4">Interactive Map</h2>
        <p className="text-gray-600 mb-4">
          Click on the map or drag the marker to test different locations.
          Red markers indicate locations outside our service area.
        </p>

        <div className="h-96 mb-4">
          <PickableMap
            initialCoordinates={selectedCoords || coordinates || { lat: 17.4933, lng: 78.3915 }}
            onCoordinatesChange={handleMapCoordinateChange}
            showCurrentLocationButton={true}
            onGetCurrentLocation={getCurrentLocation}
            isLoading={loading}
            showServiceAreaValidation={true}
            height="100%"
          />
        </div>

        {selectedCoords && (
          <div className="p-3 bg-gray-50 rounded">
            <p><strong>Selected Coordinates:</strong> {selectedCoords.lat.toFixed(6)}, {selectedCoords.lng.toFixed(6)}</p>
            <p><strong>Distance from Kukatpally:</strong> {calculateDistance(selectedCoords, { lat: 17.4933, lng: 78.3915 }).toFixed(2)} km</p>
            <p><strong>Within Service Area:</strong> {isWithinAllowedDistance(selectedCoords, undefined, 10) ? 'Yes' : 'No'}</p>
          </div>
        )}
      </div>

      {/* Toast Tests */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-xl font-semibold mb-4">Toast Notifications Test</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
          <Button onClick={() => toast.success('Success toast!')}>
            Success Toast
          </Button>
          <Button onClick={() => toast.error('Error toast!')}>
            Error Toast
          </Button>
          <Button onClick={() => toast.info('Info toast!')}>
            Info Toast
          </Button>
          <Button onClick={() => toast('Default toast!')}>
            Default Toast
          </Button>
        </div>
      </div>
    </div>
  );
}
