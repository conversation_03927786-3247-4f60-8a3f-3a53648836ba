import Footer from "@/components/Footer";
import Header from "@/components/Header";

export default function PrivacyPolicy(){
    return <div>
        <Header/>
        <section className="flex flex-col bg-white text-gray-800 px-6 py-12 md:px-20 lg:px-32">
          <h1 className="text-3xl font-bold mb-6">Privacy Policy</h1>
          <p className="mb-8 text-gray-600">Effective Date: <strong>3rd May 2025</strong></p>
        
          <div className="space-y-8 text-gray-700">
        <div>
          <h2 className="text-xl font-semibold mb-2">1. Information We Collect</h2>
          <p>We may collect and process the following information:</p>
          <ul className="list-disc ml-6">
            <li>Name, age, gender, profession, email address, phone number</li>
            <li>Current or saved address and GPS-based location</li>
            <li>WhatsApp number or contact method</li>
            <li>Billing details and transaction data (via Razorpay)</li>
            <li>IP address, cookies, device info, and usage data</li>
          </ul>
        </div>
        <div>
          <h2 className="text-xl font-semibold mb-2">2. How We Use Your Information</h2>
          <p>We use your data to:</p>
          <ul className="list-disc ml-6">
            <li>Facilitate food ordering, delivery, and payment</li>
            <li>Connect you with local vendors or home chefs</li>
            <li>Communicate via WhatsApp and email</li>
            <li>Enhance customer service and user experience</li>
            <li>Analyze and improve our offerings and security</li>
          </ul>
        </div>
        <div>
          <h2 className="text-xl font-semibold mb-2">3. Data Sharing</h2>
          <p>We only share necessary data:</p>
          <ul className="list-disc ml-6">
            <li>With vendors/home chefs: your name and phone number</li>
            <li>With third-party tools:
              <ul className="list-disc ml-6">
                <li>Razorpay (payments)</li>
                <li>Meta Cloud / WhatsApp Business API (messaging)</li>
                <li>Google Maps (location & routing)</li>
                <li>AWS & MongoDB (data storage)</li>
                <li>Logistics partners (for delivery)</li>
              </ul>
            </li>
          </ul>
          <p className="mt-2">We <strong>do not sell</strong> your personal data.</p>
        </div>
        <div>
          <h2 className="text-xl font-semibold mb-2">4. Cookies & Tracking</h2>
          <p>We use cookies and similar tracking technologies to enhance experience, analyze traffic, and personalize content. You can manage cookies via your browser settings.</p>
        </div>
        <div>
          <h2 className="text-xl font-semibold mb-2">5. Children’s Privacy</h2>
          <p>We do not knowingly collect data from users under 13 years of age. If you’re under 13, please refrain from using our services.</p>
        </div>
        <div>
          <h2 className="text-xl font-semibold mb-2">6. Data Security</h2>
          <p>All data is stored securely on encrypted databases hosted via AWS and MongoDB with restricted access. We use industry-standard security protocols to protect your data.</p>
        </div>
        <div>
          <h2 className="text-xl font-semibold mb-2">7. Your Rights</h2>
          <p>You can:</p>
          <ul className="list-disc ml-6">
            <li>Access, correct, or delete your personal data</li>
            <li>Opt out of marketing communications</li>
          </ul>
          <p>To do so, contact us at: <strong><EMAIL></strong></p>
        </div>
        <div>
          <h2 className="text-xl font-semibold mb-2">8. Changes to this Policy</h2>
          <p>We may update this Privacy Policy periodically. Continued use of our services means you accept the latest version.</p>
        </div>
        <div>
          <h2 className="text-xl font-semibold mb-2">9. Contact Us</h2>
          <p>If you have questions or requests related to your data:</p>
          <p>Email: <strong><EMAIL></strong></p>
        </div>
          </div>
        </section>
        <Footer/>
    </div>
}