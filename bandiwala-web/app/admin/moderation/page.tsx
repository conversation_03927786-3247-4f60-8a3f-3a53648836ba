'use client';

import { useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import ModerationDashboard from '@/components/reviews/ModerationDashboard';
import { BackButton } from '@/components/ui/back-button';

export default function ModerationPage() {
  const { user, isAuthenticated } = useAuth();
  const router = useRouter();

  useEffect(() => {
    // Proper role-based access control
    if (!isAuthenticated) {
      router.push('/login');
      return;
    }

    // Check if user has admin role
    const isAdmin = user?.role === 'admin';

    if (!isAdmin) {
      router.push('/');
      return;
    }
  }, [isAuthenticated, user, router]);

  if (!isAuthenticated || !user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-6">
          <BackButton />
        </div>
        
        <ModerationDashboard />
      </main>
      
      <Footer />
    </div>
  );
}
