'use client';

import { useEffect, useState } from 'react';
import Catalog from "@/components/dishes/catalog";
import Footer from "@/components/Footer";
import Header from "@/components/Header";
import { Button } from "@/components/ui/button";
import { useSearch } from "@/context/SearchContext";
import { menuItemService } from '@/services/api';

export default function Dishes() {
  const { searchQuery } = useSearch();
  const [selectedCategory, setSelectedCategory] = useState('');
  const [availableCategories, setAvailableCategories] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);

  // Fetch available categories from menu items
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await menuItemService.getAllMenuItems();
        const menuItems = response.data;

        // Extract unique categories from menu items
        const uniqueCategories = Array.from(
          new Set(menuItems.map((item: any) => item.itemCategory as string))
        ).sort();

        setAvailableCategories(['All', ...uniqueCategories as string[]]);
      } catch (error) {
        console.error('Error fetching categories:', error);
        // Fallback categories if API fails
        setAvailableCategories(['All']);
      } finally {
        setLoading(false);
      }
    };

    fetchCategories();
  }, []);

  // Set category if it matches the search query
  useEffect(() => {
    if (availableCategories.length > 0) {
      const matchingCategory = availableCategories.find(
        category => category.toLowerCase() === searchQuery.toLowerCase()
      );

      if (matchingCategory) {
        setSelectedCategory(matchingCategory);
      }
    }
  }, [searchQuery, availableCategories]);

  const handleCategoryClick = (category: string) => {
    setSelectedCategory(category === 'All' ? '' : category);
  };

  return (
    <div>
      <Header showBackButton={true} />
      <div className="section-container py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-center mb-3 bg-gradient-to-r from-bandiwala-orange to-bandiwala-red bg-clip-text text-transparent">
            Our Menu Categories
          </h1>
          <p className="text-center text-gray-600 text-sm">Discover delicious dishes from various categories</p>
        </div>
        {loading ? (
          <div className="flex justify-center mb-4">
            <div className="animate-pulse">Loading categories...</div>
          </div>
        ) : (
          <div className="max-w-6xl mx-auto mb-6 px-4">
            {/* For large screens: use flex wrap with centered layout */}
            <div className="hidden lg:flex flex-wrap justify-center gap-4 xl:gap-5">
              {availableCategories.map((category) => (
                <Button
                  key={category}
                  variant={selectedCategory === category ? "default" : "outline"}
                  className={`rounded-full transition-all duration-300 text-base px-8 py-4 h-auto min-h-[48px] whitespace-nowrap font-medium shadow-sm hover:shadow-lg transform hover:scale-105 ${
                    selectedCategory === category
                    ? "bg-gradient-to-r from-bandiwala-orange to-bandiwala-red text-white border-none shadow-lg scale-105"
                    : "bg-white hover:bg-gradient-to-r hover:from-bandiwala-orange hover:to-bandiwala-red hover:text-white border-2 border-gray-200 text-gray-700 hover:border-transparent"
                  }`}
                  onClick={() => handleCategoryClick(category)}
                >
                  {category}
                </Button>
              ))}
            </div>

            {/* For medium screens: use grid layout */}
            <div className="hidden md:grid md:grid-cols-4 lg:hidden gap-4">
              {availableCategories.map((category) => (
                <Button
                  key={category}
                  variant={selectedCategory === category ? "default" : "outline"}
                  className={`rounded-full transition-all duration-300 text-sm px-5 py-3 h-auto min-h-[40px] font-medium shadow-sm hover:shadow-md transform hover:scale-105 ${
                    selectedCategory === category
                    ? "bg-gradient-to-r from-bandiwala-orange to-bandiwala-red text-white border-none shadow-md scale-105"
                    : "bg-white hover:bg-gradient-to-r hover:from-bandiwala-orange hover:to-bandiwala-red hover:text-white border-2 border-gray-200 text-gray-700 hover:border-transparent"
                  }`}
                  onClick={() => handleCategoryClick(category)}
                >
                  <span className="truncate">{category}</span>
                </Button>
              ))}
            </div>

            {/* For small screens: beautiful compact design */}
            <div className="md:hidden bg-gradient-to-br from-orange-50 to-red-50 rounded-2xl p-4 shadow-sm border border-orange-100">
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">Choose Category</label>
                <select
                  value={selectedCategory || 'All'}
                  onChange={(e) => handleCategoryClick(e.target.value)}
                  className="w-full px-4 py-3 border-2 border-gray-200 rounded-xl bg-white text-gray-700 focus:border-bandiwala-orange focus:ring-2 focus:ring-orange-100 focus:outline-none shadow-sm transition-all duration-200 font-medium"
                >
                  {availableCategories.map((category) => (
                    <option key={category} value={category === 'All' ? '' : category}>
                      {category}
                    </option>
                  ))}
                </select>
              </div>

              {/* Show first 6 categories as beautiful buttons for quick access */}
              <div className="grid grid-cols-3 gap-3">
                {availableCategories.slice(0, 6).map((category) => (
                  <Button
                    key={category}
                    variant={selectedCategory === category ? "default" : "outline"}
                    className={`rounded-xl transition-all duration-300 text-xs px-3 py-2.5 h-auto min-h-[36px] font-medium shadow-sm hover:shadow-md transform hover:scale-105 ${
                      selectedCategory === category
                      ? "bg-gradient-to-r from-bandiwala-orange to-bandiwala-red text-white border-none shadow-md scale-105"
                      : "bg-white hover:bg-gradient-to-r hover:from-bandiwala-orange hover:to-bandiwala-red hover:text-white border-2 border-gray-200 text-gray-700 hover:border-transparent"
                    }`}
                    onClick={() => handleCategoryClick(category)}
                  >
                    <span className="truncate">{category}</span>
                  </Button>
                ))}
              </div>

              {availableCategories.length > 6 && (
                <div className="text-xs text-gray-500 text-center mt-3">
                  Use dropdown above to see all {availableCategories.length} categories
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      <Catalog searchQuery={searchQuery} categoryFilter={selectedCategory} />
      <p className="text-sm text-muted-foreground text-center mt-8">
        We&apos;re working on bringing more dishes to you.
      </p>
      <Footer />
    </div>
  );
}