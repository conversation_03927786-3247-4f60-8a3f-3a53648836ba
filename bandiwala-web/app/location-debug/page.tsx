'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';

export default function LocationDebugPage() {
  const [status, setStatus] = useState('Ready');
  const [coords, setCoords] = useState<{lat: number, lng: number} | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);

  const testBasicGeolocation = () => {
    setStatus('Testing basic geolocation...');
    setLoading(true);
    setError(null);
    setCoords(null);

    console.log('Starting basic geolocation test');
    console.log('Navigator geolocation available:', !!navigator.geolocation);

    if (!navigator.geolocation) {
      const errorMsg = 'Geolocation is not supported by this browser';
      setError(errorMsg);
      setStatus('Failed');
      setLoading(false);
      toast.error(errorMsg);
      return;
    }

    console.log('Requesting position...');
    navigator.geolocation.getCurrentPosition(
      (position) => {
        console.log('Success! Position received:', position);
        const newCoords = {
          lat: position.coords.latitude,
          lng: position.coords.longitude
        };
        setCoords(newCoords);
        setStatus('Success');
        setLoading(false);
        toast.success(`Location found: ${newCoords.lat.toFixed(6)}, ${newCoords.lng.toFixed(6)}`);
      },
      (error) => {
        console.error('Geolocation error:', error);
        let errorMessage = 'Unknown error';
        
        switch (error.code) {
          case error.PERMISSION_DENIED:
            errorMessage = 'User denied the request for Geolocation.';
            break;
          case error.POSITION_UNAVAILABLE:
            errorMessage = 'Location information is unavailable.';
            break;
          case error.TIMEOUT:
            errorMessage = 'The request to get user location timed out.';
            break;
        }
        
        setError(errorMessage);
        setStatus('Failed');
        setLoading(false);
        toast.error(errorMessage);
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 0
      }
    );
  };

  const testWithPermissionCheck = async () => {
    setStatus('Testing with permission check...');
    setLoading(true);
    setError(null);
    setCoords(null);

    try {
      // Check if permissions API is available
      if ('permissions' in navigator) {
        console.log('Checking geolocation permission...');
        const permission = await navigator.permissions.query({ name: 'geolocation' });
        console.log('Permission state:', permission.state);
        
        if (permission.state === 'denied') {
          throw new Error('Geolocation permission is denied. Please enable it in your browser settings.');
        }
      }

      // Proceed with geolocation
      testBasicGeolocation();
    } catch (err) {
      console.error('Permission check failed:', err);
      setError(err instanceof Error ? err.message : 'Permission check failed');
      setStatus('Failed');
      setLoading(false);
      toast.error('Permission check failed');
    }
  };

  const checkBrowserSupport = () => {
    const support = {
      geolocation: 'geolocation' in navigator,
      permissions: 'permissions' in navigator,
      https: location.protocol === 'https:',
      localhost: location.hostname === 'localhost' || location.hostname === '127.0.0.1'
    };

    console.log('Browser support check:', support);
    
    let message = 'Browser Support:\n';
    message += `Geolocation API: ${support.geolocation ? '✅' : '❌'}\n`;
    message += `Permissions API: ${support.permissions ? '✅' : '❌'}\n`;
    message += `HTTPS: ${support.https ? '✅' : '❌'}\n`;
    message += `Localhost: ${support.localhost ? '✅' : '❌'}`;

    toast(message, { duration: 8000 });
    setStatus('Support checked - see console and toast');
  };

  return (
    <div className="container mx-auto p-6 max-w-2xl">
      <h1 className="text-3xl font-bold mb-6">Geolocation Debug Page</h1>
      
      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4">Current Status</h2>
        <p><strong>Status:</strong> {status}</p>
        <p><strong>Loading:</strong> {loading ? 'Yes' : 'No'}</p>
        {coords && (
          <p><strong>Coordinates:</strong> {coords.lat.toFixed(6)}, {coords.lng.toFixed(6)}</p>
        )}
        {error && (
          <p className="text-red-600"><strong>Error:</strong> {error}</p>
        )}
      </div>

      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4">Tests</h2>
        <div className="space-y-3">
          <Button 
            onClick={checkBrowserSupport}
            className="w-full"
            variant="outline"
          >
            Check Browser Support
          </Button>
          
          <Button 
            onClick={testBasicGeolocation}
            disabled={loading}
            className="w-full"
          >
            {loading ? 'Testing...' : 'Test Basic Geolocation'}
          </Button>
          
          <Button 
            onClick={testWithPermissionCheck}
            disabled={loading}
            className="w-full"
            variant="secondary"
          >
            {loading ? 'Testing...' : 'Test with Permission Check'}
          </Button>
        </div>
      </div>

      <div className="bg-gray-50 rounded-lg p-4">
        <h3 className="font-semibold mb-2">Debug Info</h3>
        <p className="text-sm text-gray-600">
          Open browser console (F12) to see detailed logs.
          Make sure you're using HTTPS or localhost for geolocation to work.
        </p>
        <p className="text-sm text-gray-600 mt-2">
          Current URL: {typeof window !== 'undefined' ? window.location.href : 'Loading...'}
        </p>
      </div>
    </div>
  );
}
