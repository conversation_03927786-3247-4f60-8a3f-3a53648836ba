'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useCart } from '@/contexts/CartContext';
import { useAuth } from '@/contexts/AuthContext';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import EmptyCart from '@/components/cart/EmptyCart';
import VendorGroup from '@/components/cart/VendorGroup';
import OrderSummary from '@/components/cart/OrderSummary';
import CartSkeleton from '@/components/cart/CartSkeleton';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { CartPopup } from '@/components/ui/cart-popup';
import { Trash2 } from 'lucide-react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
// import { CartStatus } from '@/components/CartStatus';
import { CartErrorBoundary } from '@/components/CartErrorBoundary';
import { isWithinDeliveryArea } from '@/utils/distance';
import useUserLocation from '@/hooks/useUserLocation';
// import { SyncIndicator } from '@/components/SyncIndicator';

// Group cart items by vendor
interface GroupedItems {
  [vendorId: string]: {
    vendorName: string;
    deliveryTime: string;
    deliveryFee: number;
    minOrderValue: number;
    items: any[];
  };
}

export default function CartPage() {
  return (
    <CartErrorBoundary>
      <CartPageContent />
    </CartErrorBoundary>
  );
}

function CartPageContent() {
  const router = useRouter();
  const { user, isAuthenticated, isLoading: authLoading } = useAuth();
  const { coordinates } = useUserLocation();
  const {
    cart,
    removeFromCart,
    updateCartItem,
    clearCart,
    reorderCartItems,
    isOnline,
    loading,
    error,
    refreshCart,
    calculateCartTotals,
    promoState,
    applyPromoCode,
    removePromoCode
  } = useCart();

  // Extract items from cart
  const items = cart?.items || [];

  const [isLoading, setIsLoading] = useState(true);
  const [groupedItems, setGroupedItems] = useState<GroupedItems>({});
  const [showClearCartPopup, setShowClearCartPopup] = useState(false);

  // Authentication protection - redirect to login if not authenticated
  useEffect(() => {
    console.log('Cart page: Auth state check', { authLoading, isAuthenticated, user });

    // If not loading and not authenticated, redirect to login
    if (!authLoading && !isAuthenticated) {
      const hasToken = typeof window !== 'undefined' && localStorage.getItem('auth_token');

      if (hasToken) {
        console.log('Cart page: Token exists but not authenticated yet, waiting...');

        // Wait a bit longer before taking action
        const timer = setTimeout(() => {
          if (!isAuthenticated) {
            console.log('Cart page: Still not authenticated after delay, redirecting to login');
            toast.error('Please log in to access your cart');
            router.push('/login');
          }
        }, 3000);

        return () => clearTimeout(timer);
      } else {
        console.log('Cart page: No token found, redirecting to login immediately');
        toast.error('Please log in to access your cart');
        router.push('/login');
      }
    }
  }, [authLoading, isAuthenticated, router, user]);

  // Note: Cart context already handles initial cart loading
  // No need to manually refresh cart here

  // Group items by vendor
  useEffect(() => {
    const grouped: GroupedItems = {};

    if (items && items.length > 0) {
      items.forEach(item => {
        // Use a default vendor ID if it's undefined
        const vendorId = item.vendorId || 'unknown';

        if (!grouped[vendorId]) {
          grouped[vendorId] = {
            vendorName: item.vendorName || 'Unknown Vendor',
            deliveryTime: '20-30 min', // Default or fetch from API
            deliveryFee: 20, // Default or fetch from API
            minOrderValue: 0, // Default or fetch from API
            items: []
          };
        }

        grouped[vendorId].items.push(item);
      });
    }

    setGroupedItems(grouped);
    setIsLoading(false);
  }, [items]);

  // Handle promo code application
  const handleApplyPromo = async (code: string) => {
    if (!code.trim()) return;

    const totals = calculateCartTotals();
    const success = await applyPromoCode(code, totals.subtotal);

    if (success) {
      const validation = promoState.promoValidation;
      if (validation?.isFreeDelivery) {
        toast.success('Free delivery applied!', {
          description: validation.message
        });
      } else {
        toast.success('Promo code applied!', {
          description: validation?.message || `You saved ₹${validation?.discountAmount}!`
        });
      }
    } else {
      toast.error('Invalid promo code', {
        description: promoState.promoValidation?.message || 'Please enter a valid promo code.'
      });
    }
  };

  // Handle removing promo code
  const handleRemovePromo = () => {
    removePromoCode();
    toast.success('Promo code removed');
  };

  // Handle proceeding to payment
  const handleProceedToPayment = () => {
    router.push('/checkout/summary');
  };

  // Handle adding more items from a vendor
  const handleAddMoreItems = (vendorName: string) => {
    router.push(`/vendors`);
  };

  // Handle reordering items within a vendor group
  const handleReorderItems = async (vendorId: string, reorderedItems: any[]) => {
    try {
      // Get all items from all vendors
      const allItems = Object.values(groupedItems).flatMap(vendor => vendor.items);

      // Replace the items for this vendor with the reordered ones
      const updatedItems = allItems.map(item => {
        const reorderedItem = reorderedItems.find(ri =>
          ri.menuItemId === item.menuItemId &&
          ri.selectedSubcategory.title === item.selectedSubcategory.title
        );
        return reorderedItem || item;
      });

      // Call the reorder function from context
      await reorderCartItems(updatedItems);
    } catch (error) {
      console.error('Error reordering items:', error);
    }
  };

  // Handle clear cart confirmation
  const handleConfirmClearCart = async () => {
    try {
      await clearCart();
      toast.success('Cart cleared', {
        description: 'Your cart has been successfully cleared',
      });
      setShowClearCartPopup(true);
      setTimeout(() => {
        setShowClearCartPopup(false);
      }, 3000);
    } catch (error) {
      console.error('Error clearing cart:', error);
      toast.error('Error', {
        description: 'Failed to clear cart. Please try again.',
      });
    }
  };

  // Show loading state while checking authentication
  if (authLoading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen bg-gray-50">
        <div className="animate-spin rounded-full h-16 w-16 border-b-4 border-green-600 mb-4"></div>
        <p className="text-gray-600 font-medium">Loading...</p>
      </div>
    );
  }

  // If not authenticated and no token, the useEffect will handle redirect
  // Show loading state while redirect is happening
  if (!isAuthenticated) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen bg-gray-50">
        <div className="animate-spin rounded-full h-16 w-16 border-b-4 border-green-600 mb-4"></div>
        <p className="text-gray-600 font-medium">Redirecting to login...</p>
      </div>
    );
  }

  if (isLoading) {
    return <CartSkeleton />;
  }

  if (items.length === 0) {
    return (
      <>
        <Header showBackButton={true} />
        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <EmptyCart />
        </main>
        <Footer />
      </>
    );
  }

  const cartTotals = calculateCartTotals();

  // Calculate discount and adjusted totals based on promo code type
  const discount = promoState.promoValidation?.discountAmount || 0;
  const adjustedDeliveryCharge = promoState.promoValidation?.isFreeDelivery ? 0 : cartTotals.deliveryCharge;
  const adjustedTotal = cartTotals.subtotal + cartTotals.platformFee + adjustedDeliveryCharge + cartTotals.tax - discount;

  return (
    <>
      <Header showBackButton={true} />
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">Your Cart</h1>
          <div className="flex items-center gap-4">
            {/* {isOnline !== undefined && (
              // <SyncIndicator
              //   isOnline={isOnline}
              //   isSyncing={loading}
              //   pendingChanges={!isOnline && items.length > 0}
              // />
            )} */}
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button
                  variant="outline"
                  className="text-red-500 hover:text-red-700 hover:bg-red-50"
                >
                  <Trash2 size={16} className="mr-2" />
                  Clear Cart
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Clear Cart</AlertDialogTitle>
                  <AlertDialogDescription>
                    Are you sure you want to clear your cart? This action cannot be undone and all items will be removed.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction
                    onClick={handleConfirmClearCart}
                    className="bg-red-500 hover:bg-red-600 text-white"
                  >
                    Clear Cart
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        </div>

        {/* Location Warning */}
        {coordinates && !isWithinDeliveryArea(coordinates) && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <div className="flex items-start">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">
                  Out of stock for your area
                </h3>
                <div className="mt-2 text-sm text-red-700">
                  <p>
                    Items in your cart are not available for delivery to your current location.
                    Please update your location to within our service area to proceed with your order.
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2">
            {Object.entries(groupedItems).map(([vendorId, vendorData]) => (
              <VendorGroup
                key={vendorId}
                vendorName={vendorData.vendorName}
                deliveryTime={vendorData.deliveryTime}
                deliveryFee={vendorData.deliveryFee}
                minOrderValue={vendorData.minOrderValue}
                items={vendorData.items}
                onUpdateQuantity={(id, qty, item) => updateCartItem(id, qty, undefined, item?.selectedSubcategory)}
                onUpdateNotes={(id, notes, item) => updateCartItem(id, item?.quantity || 1, notes, item?.selectedSubcategory)}
                onRemoveItem={(id, item) => removeFromCart(id, item?.selectedSubcategory)}
                onAddMoreItems={handleAddMoreItems}
                onReorderItems={(reorderedItems) => handleReorderItems(vendorId, reorderedItems)}
              />
            ))}
          </div>

          <div>
            <OrderSummary
              subtotal={cartTotals.subtotal}
              platformFee={cartTotals.platformFee}
              deliveryCharge={adjustedDeliveryCharge}
              tax={cartTotals.tax}
              discount={discount}
              total={adjustedTotal}
              appliedPromoCode={promoState.appliedPromoCode}
              isFreeDelivery={promoState.promoValidation?.isFreeDelivery || false}
              originalDeliveryCharge={cartTotals.deliveryCharge}
              estimatedDeliveryTime="20-30 min"
              onApplyPromo={handleApplyPromo}
              onProceedToPayment={handleProceedToPayment}
            />
          </div>
        </div>
      </main>
      <Footer />
      {/* <CartStatus /> */}

      {/* Cart Clear Popup */}
      <CartPopup
        isOpen={showClearCartPopup}
        onClose={() => setShowClearCartPopup(false)}
        title="Cart cleared"
        description="Your cart has been successfully cleared"
        icon={<Trash2 size={20} className="text-red-500" />}
      />
    </>
  );
}
