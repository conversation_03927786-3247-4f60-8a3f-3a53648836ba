'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import emailjs from '@emailjs/browser';

export default function TestEmailPage() {
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const sendTestEmail = async () => {
    setLoading(true);
    setResult(null);
    setError(null);

    try {
      // Get EmailJS configuration
      const serviceId = process.env.NEXT_PUBLIC_EMAILJS_SERVICE_ID;
      const templateId = process.env.NEXT_PUBLIC_EMAILJS_TEMPLATE_ID;
      const userId = process.env.NEXT_PUBLIC_EMAILJS_USER_ID;
      const toEmail = process.env.NEXT_PUBLIC_EMAILJS_TO_EMAIL || '';

      console.log('EmailJS Config:', { serviceId, templateId, userId, toEmail });

      if (!serviceId || !templateId || !userId) {
        throw new Error('EmailJS configuration is missing');
      }

      // Sample order data for testing
      const testOrderId = 'TEST-' + Date.now();

      // Sample items for the order
      const testItems = [
        {
          name: 'Test Item 1',
          quantity: 2,
          price: '199.00'
        },
        {
          name: 'Test Item 2',
          quantity: 1,
          price: '299.00'
        }
      ];

      // Format items for the email - create a simple string representation
      const itemsText = testItems.map(item =>
        `${item.name} x${item.quantity} — ₹${item.price}`
      ).join('\n');

      // Calculate totals
      const subtotal = 697; // 2*199 + 1*299
      const platformFee = 20;
      const deliveryCharge = 40;
      const taxes = 35; // 5% of subtotal
      const total = subtotal + platformFee + deliveryCharge + taxes;

      // Sample coordinates
      const coordinates = {
        lat: 17.4485835,
        lng: 78.3908034
      };

      // Create Google Maps URL
      const mapUrl = `https://www.google.com/maps?q=${coordinates.lat},${coordinates.lng}`;

      // Prepare the template parameters to match your Handlebars template
      const templateParams = {
        // EmailJS standard parameters
        to_name: 'Karthik', // Add recipient name
        from_name: 'Bandiwala Order System', // Add sender name
        reply_to: '<EMAIL>', // Add reply-to address
        recipient: toEmail, // Try different parameter name
        email: toEmail, // Try another parameter name
        to_email: toEmail, // Original parameter

        // Order details matching your template variables
        order_id: testOrderId,
        items_text: itemsText, // Plain text representation of items
        address: '123 Test Street, Test City',
        coordinates: `${coordinates.lat}, ${coordinates.lng}`,
        map_url: mapUrl,
        subtotal: subtotal.toFixed(2),
        platform_fee: platformFee.toFixed(2),
        delivery_charge: deliveryCharge.toFixed(2),
        taxes: taxes.toFixed(2),
        total: total.toFixed(2),

        // Shop information
        shop_name: 'Jai Bhavani Chat Bhandar',
        shop_phone: '+91 98765 43210',

        // Additional info
        customer_name: 'Test Customer',
        customer_email: '<EMAIL>',
        phone_number: '+91 98765 43210',
        order_date: new Date().toLocaleDateString('en-IN', {
          year: 'numeric',
          month: 'long',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit'
        })
      };

      console.log('Sending test email with parameters:', templateParams);

      // Initialize EmailJS
      emailjs.init(userId);

      console.log('Sending email with EmailJS...');

      // Send the email directly
      const response = await emailjs.send(serviceId, templateId, templateParams);

      console.log('EmailJS Response:', response);
      setResult(response);

      // Also try sending to the API endpoint for comparison
      try {
        const apiResponse = await fetch('/api/test-email');
        const apiResult = await apiResponse.json();
        console.log('API test email response:', apiResult);
      } catch (apiError) {
        console.error('API test email failed:', apiError);
      }
    } catch (err) {
      console.error('Error sending test email:', err);
      setError(err instanceof Error ? err.message : String(err));
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-4">Test EmailJS Integration</h1>

      <div className="mb-6">
        <p className="mb-2">This page tests the EmailJS integration for sending order confirmation emails.</p>
        <p className="mb-4">Click the button below to send a test email to <strong>{process.env.NEXT_PUBLIC_EMAILJS_TO_EMAIL || '<EMAIL>'}</strong>.</p>

        <Button
          onClick={sendTestEmail}
          disabled={loading}
          className="bg-bandiwala-orange hover:bg-orange-600 text-white"
        >
          {loading ? 'Sending...' : 'Send Test Email'}
        </Button>
      </div>

      {error && (
        <div className="p-4 mb-4 bg-red-100 border border-red-400 text-red-700 rounded">
          <h2 className="font-bold mb-2">Error:</h2>
          <p>{error}</p>
        </div>
      )}

      {result && (
        <div className="p-4 mb-4 bg-green-100 border border-green-400 text-green-700 rounded">
          <h2 className="font-bold mb-2">Success!</h2>
          <p>Email sent successfully with status: {result.status}</p>
          <p>Text: {result.text}</p>
        </div>
      )}

      <div className="mt-8 p-4 bg-gray-100 rounded">
        <h2 className="font-bold mb-2">EmailJS Configuration:</h2>
        <ul className="list-disc pl-5">
          <li>Service ID: {process.env.NEXT_PUBLIC_EMAILJS_SERVICE_ID ? '✅ Set' : '❌ Missing'}</li>
          <li>Template ID: {process.env.NEXT_PUBLIC_EMAILJS_TEMPLATE_ID ? '✅ Set' : '❌ Missing'}</li>
          <li>User ID: {process.env.NEXT_PUBLIC_EMAILJS_USER_ID ? '✅ Set' : '❌ Missing'}</li>
          <li>To Email: {process.env.NEXT_PUBLIC_EMAILJS_TO_EMAIL || '<EMAIL>'}</li>
        </ul>
      </div>
    </div>
  );
}
