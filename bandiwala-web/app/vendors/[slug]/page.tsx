import { vendorService, menuItemService } from '@/services/api';
import { VendorClientPage } from './client-page';
import { notFound } from 'next/navigation';
import { Suspense } from 'react';

async function getVendorData(slug: string) {
  try {
    // Log the slug we're trying to fetch
    console.log('Fetching vendor with slug:', slug);

    const response = await vendorService.getVendorBySlug(slug);

    // Log the response for debugging
    console.log('Vendor API response:', JSON.stringify(response, null, 2));

    // Handle the new API response format
    if (response?.success && response?.data) {
      // If the response has vendor and menuItems directly
      if (response.data.vendor && response.data.menuItems) {
        console.log('Successfully retrieved vendor data for slug:', slug);
        return response.data;
      }

      // If the response only has the vendor data
      if (response.data && !response.data.vendor && !response.data.menuItems) {
        console.log('Retrieved vendor but no menu items, fetching menu items separately');

        // Get the vendor ID
        const vendorId = response.data._id;

        // Fetch menu items for this vendor
        const menuItemsResponse = await menuItemService.getMenuItemsByVendor(vendorId);

        if (menuItemsResponse.success && menuItemsResponse.data) {
          return {
            vendor: response.data,
            menuItems: menuItemsResponse.data
          };
        }

        // If no menu items found, return vendor with empty menu items array
        return {
          vendor: response.data,
          menuItems: []
        };
      }
    }

    console.error('Vendor not found or invalid response structure:', response);
    throw new Error('Vendor not found');
  } catch (error) {
    console.error('Error fetching vendor:', error);
    throw error;
  }
}

export default async function VendorPage({
  params
}: {
  params: Promise<{ slug: string }>
}) {
  try {
    // Await the params promise to get the slug
    const { slug } = await params;

    // Get vendor data using the slug
    const data = await getVendorData(slug);

    return (
      <Suspense fallback={<div>Loading...</div>}>
        <VendorClientPage
          initialData={data}
          slug={slug}
        />
      </Suspense>
    );
  } catch (error) {
    return notFound();
  }
}