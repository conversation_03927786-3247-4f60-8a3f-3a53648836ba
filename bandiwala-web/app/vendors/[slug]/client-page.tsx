'use client';

import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Layout from "@/components/vendors/Layout";
import Profile from "@/components/vendors/Profile";
import SearchNav from "@/components/vendors/SearchNav";
import NavigationSearch from "@/components/vendors/NavigationSearch";
import Image from 'next/image';
import Link from 'next/link';
import { useCart } from '@/contexts/CartContext';
import { useAuth } from '@/contexts/AuthContext';
import { getImageUrl } from '@/utils/imageUtils';
import { toast } from 'sonner';
import { Plus, Minus, ShoppingBag } from 'lucide-react';
import ReviewsList from '@/components/reviews/ReviewsList';
import useUserLocation from '@/hooks/useUserLocation';

// Define types for our menu items and vendors
interface Subcategory {
  title: string;
  quantity: string;
  price: number;
}

interface MenuItem {
  _id: string;
  itemName: string;
  slug: string;
  description: string;
  subcategories: Subcategory[];
  image: string;
  vendorId: string;
  itemCategory: string;
  isAvailable: boolean;
}

interface Vendor {
  _id: string;
  name: string;
  description: string;
  slug: string;
  rating: number;
  location: string;
  phone?: string;
  image: string;
  deliveryTime: string;
  deliveryFee: number;
  minOrderValue: number;
  isActive: boolean;
}

interface VendorData {
  vendor: Vendor;
  menuItems: MenuItem[];
}

interface VendorClientPageProps {
  initialData: VendorData;
  slug: string;
}

export function VendorClientPage({ initialData, slug }: VendorClientPageProps) {
  const { addToCart, updateCartItem, cart } = useCart();
  const { user, isAuthenticated, isLoading: authLoading } = useAuth();
  const { coordinates } = useUserLocation();
  const router = useRouter();

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push('/login');
    }
  }, [isAuthenticated, authLoading, router]);

  // Show loading while checking authentication
  if (authLoading) {
    return (
      <Layout headerProps={{ showBackButton: true }}>
        <div className="min-h-screen flex items-center justify-center">
          <div className="animate-spin rounded-full h-16 w-16 border-4 border-bandiwala-orange border-t-transparent"></div>
        </div>
      </Layout>
    );
  }

  // Show login prompt if not authenticated
  if (!isAuthenticated) {
    return (
      <Layout headerProps={{ showBackButton: true }}>
        <div className="min-h-screen flex items-center justify-center bg-gray-50">
          <div className="bg-white p-8 rounded-xl shadow-lg max-w-md w-full mx-4 text-center">
            <ShoppingBag className="h-16 w-16 text-bandiwala-orange mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Login Required</h2>
            <p className="text-gray-600 mb-6">
              Please log in to view vendor details and menu items.
            </p>
            <Link href="/login">
              <button className="w-full bg-gradient-to-r from-bandiwala-orange to-bandiwala-red hover:from-bandiwala-red hover:to-bandiwala-orange text-white font-semibold px-6 py-3 rounded-full transition-all duration-300">
                Login to Continue
              </button>
            </Link>
          </div>
        </div>
      </Layout>
    );
  }

  // Enhanced error handling with more detailed feedback and logging
  if (!initialData) {
    console.error('No initialData provided to VendorClientPage');
    return (
      <Layout headerProps={{ showBackButton: true }}>
        <div className="max-w-7xl mx-auto px-4 py-8">
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
            <strong className="font-bold">Error!</strong>
            <span className="block sm:inline"> No vendor data available. Please try again later.</span>
          </div>
        </div>
      </Layout>
    );
  }

  if (!initialData.vendor) {
    console.error('initialData is missing vendor property', initialData);
    return (
      <Layout headerProps={{ showBackButton: true }}>
        <div className="max-w-7xl mx-auto px-4 py-8">
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
            <strong className="font-bold">Error!</strong>
            <span className="block sm:inline"> Vendor information is missing. Please try again later.</span>
            <p className="mt-2">Requested slug: {slug}</p>
          </div>
        </div>
      </Layout>
    );
  }

  if (!Array.isArray(initialData.menuItems)) {
    console.error('initialData is missing menuItems array', initialData);
    // Still show vendor info but with a message about menu items
    return (
      <Layout headerProps={{ showBackButton: true }}>
        <Profile vendor={initialData.vendor}/>
        <SearchNav/>
        <NavigationSearch/>
        <div className="max-w-7xl mx-auto px-4 py-8">
          <h2 className="text-2xl font-bold mb-6">Our Menu</h2>
          <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded relative" role="alert">
            <strong className="font-bold">Notice:</strong>
            <span className="block sm:inline"> No menu items available for this vendor at the moment.</span>
          </div>
        </div>
      </Layout>
    );
  }

  const { vendor, menuItems } = initialData;

  // Helper function to check if item is in cart and get quantity
  const getItemCartInfo = (itemId: string) => {
    const cartItem = cart.items.find(item => item.menuItemId === itemId);
    return {
      isInCart: !!cartItem,
      quantity: cartItem?.quantity || 0
    };
  };

  const handleAddToCart = (menuItem: MenuItem) => {
    // Check authentication before allowing cart operations
    if (!isAuthenticated) {
      toast("Please login to add items to cart", {
        position: 'top-right',
        duration: 3000,
        style: { backgroundColor: '#f44336', color: 'white' }
      });
      return;
    }

    try {
      // Use the first subcategory as default
      const defaultSubcategory = menuItem.subcategories[0];
      if (!defaultSubcategory) {
        toast("No plate sizes available for this item", {
          position: 'top-right',
          duration: 3000,
          style: { backgroundColor: '#f44336', color: 'white' }
        });
        return;
      }

      // Store item details in local storage for reference
      const itemDetails = {
        menuItemId: menuItem._id,
        name: menuItem.itemName,
        selectedSubcategory: defaultSubcategory,
        image: menuItem.image || '/images/vendor1.jpg',
        vendorId: vendor._id,
        vendorName: vendor.name,
        vendorPhone: vendor.phone || 'Not available'
      };
      localStorage.setItem(`item-details-${menuItem._id}`, JSON.stringify(itemDetails));

      // Add to cart with default subcategory and user location
      addToCart(menuItem._id, 1, '', defaultSubcategory, coordinates || undefined);
    } catch (error) {
      console.error('Error adding item to cart:', error);
      toast("Failed to add item to cart", {
        position: 'top-right',
        duration: 3000,
        style: { backgroundColor: '#f44336', color: 'white' }
      });
    }
  };

  // Handle quantity increase
  const handleIncreaseQuantity = (e: React.MouseEvent, menuItem: MenuItem) => {
    e.preventDefault();
    e.stopPropagation();

    // Check authentication before allowing cart operations
    if (!isAuthenticated) {
      toast("Please login to add items to cart", {
        position: 'top-right',
        duration: 3000,
        style: { backgroundColor: '#f44336', color: 'white' }
      });
      return;
    }

    const { isInCart, quantity } = getItemCartInfo(menuItem._id);

    if (isInCart) {
      // For existing items, we need to get the current subcategory from cart
      const cartItem = cart.items.find(item => item.menuItemId === menuItem._id);
      const currentSubcategory = cartItem?.selectedSubcategory || menuItem.subcategories[0];
      updateCartItem(menuItem._id, quantity + 1, '', currentSubcategory);
    } else {
      handleAddToCart(menuItem);
    }
  };

  // Handle quantity decrease
  const handleDecreaseQuantity = (e: React.MouseEvent, itemId: string, menuItem: MenuItem) => {
    e.preventDefault();
    e.stopPropagation();

    const { quantity } = getItemCartInfo(itemId);
    const cartItem = cart.items.find(item => item.menuItemId === itemId);
    const currentSubcategory = cartItem?.selectedSubcategory || menuItem.subcategories[0];

    if (quantity > 1) {
      updateCartItem(itemId, quantity - 1, '', currentSubcategory);
    } else {
      updateCartItem(itemId, 0, '', currentSubcategory); // This will remove the item
    }
  };

  return (
    <Layout headerProps={{ showBackButton: true }}>
      <Profile vendor={vendor}/>

      <div className="max-w-7xl mx-auto px-2 sm:px-4 lg:px-6 py-4 md:py-6">
        <h2 className="text-lg md:text-xl lg:text-2xl font-bold mb-3 md:mb-4">Our Menu</h2>
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-2 sm:gap-3 md:gap-4">
          {menuItems.map((item: MenuItem) => {
            const { isInCart, quantity } = getItemCartInfo(item._id);

            return (
              <div key={item._id} className="bg-white rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-shadow duration-300">
                <Link href={item.slug ? `/items/${item.slug}` : `/items/${item._id}`} className="block">
                  <div className="h-24 sm:h-28 md:h-32 bg-gray-200 relative">
                    {item.image && (
                      <Image
                        src={getImageUrl(item.image || '/images/vendor1.jpg')}
                        alt={item.itemName}
                        width={400}
                        height={160}
                        className="w-full h-full object-cover"
                      />
                    )}
                  </div>
                  <div className="p-2 sm:p-3">
                    <h3 className="text-xs sm:text-sm md:text-base font-semibold text-gray-900 line-clamp-2 mb-1">{item.itemName}</h3>
                    <p className="text-xs text-gray-600 line-clamp-1 mb-2">{item.description}</p>
                    <div className="flex justify-between items-center">
                      <span className="text-green-600 font-bold text-xs sm:text-sm">
                        ₹{item.subcategories[0]?.price || 0}
                        {item.subcategories.length > 1 && <span className="text-gray-500 text-xs">+</span>}
                      </span>
                      {isInCart ? (
                        <div className="flex items-center gap-1">
                          <div className="flex items-center bg-gray-100 border border-gray-200 rounded-md py-0.5 px-1">
                            <button
                              onClick={(e) => handleDecreaseQuantity(e, item._id, item)}
                              className="flex items-center justify-center w-4 h-4 hover:bg-gray-200 rounded-full transition-colors"
                            >
                              <Minus className="h-2.5 w-2.5 text-gray-600" />
                            </button>
                            <span className="font-semibold text-center min-w-[16px] text-xs text-gray-800">{quantity}</span>
                            <button
                              onClick={(e) => handleIncreaseQuantity(e, item)}
                              className="flex items-center justify-center w-4 h-4 hover:bg-gray-200 rounded-full transition-colors"
                            >
                              <Plus className="h-2.5 w-2.5 text-gray-600" />
                            </button>
                          </div>
                          <button
                            className="bg-bandiwala-orange text-white px-2 py-1 rounded-md hover:bg-bandiwala-red transition-colors text-xs"
                            onClick={(e) => {
                              e.preventDefault(); // Prevent link navigation
                              handleAddToCart(item);
                            }}
                          >
                            Add
                          </button>
                        </div>
                      ) : (
                        <button
                          className="bg-bandiwala-orange text-white px-2 py-1 rounded-md hover:bg-bandiwala-red transition-colors text-xs"
                          onClick={(e) => {
                            e.preventDefault(); // Prevent link navigation
                            handleAddToCart(item);
                          }}
                        >
                          Add
                        </button>
                      )}
                    </div>
                  </div>
                </Link>
              </div>
            );
          })}
        </div>
      </div>

      {/* Reviews Section */}
      <div className="max-w-7xl mx-auto px-2 sm:px-4 lg:px-6 pb-8">
        <ReviewsList
          targetType="Vendor"
          targetId={vendor._id}
          targetName={vendor.name}
          currentUserId={user?._id}
          isAuthenticated={isAuthenticated}
        />
      </div>
    </Layout>
  );
}
