'use client';

import { useEffect } from 'react';

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    console.error(error);
  }, [error]);

  return (
    <div className="flex min-h-screen items-center justify-center p-4">
      <div className="max-w-md w-full bg-white shadow-lg rounded-lg p-6">
        <div className="text-center">
          <svg className="mx-auto h-12 w-12 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
          </svg>
          <h2 className="mt-4 text-xl font-semibold text-gray-900">Vendor Not Found</h2>
          <p className="mt-2 text-gray-600">We couldn't find the vendor you're looking for. It may have been removed or the URL might be incorrect.</p>

          {error.message && (
            <div className="mt-4 p-3 bg-red-50 rounded-md text-left">
              <p className="text-sm text-red-800">{error.message}</p>
            </div>
          )}

          <div className="mt-6 flex flex-col sm:flex-row gap-3 justify-center">
            <button
              className="bg-bandiwala-orange text-white px-4 py-2 rounded-lg hover:bg-bandiwala-red transition-colors"
              onClick={() => reset()}
            >
              Try again
            </button>
            <a
              href="/vendors"
              className="border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Browse all vendors
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}