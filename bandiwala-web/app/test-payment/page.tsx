'use client';

import { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';

export default function TestPaymentPage() {
  const { user, token, isAuthenticated } = useAuth();
  const [testResults, setTestResults] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const addResult = (test: string, success: boolean, data: any) => {
    setTestResults(prev => [...prev, { test, success, data, timestamp: new Date().toISOString() }]);
  };

  const runTests = async () => {
    setIsLoading(true);
    setTestResults([]);

    // Test 1: Check authentication
    addResult('Authentication Check', isAuthenticated, {
      isAuthenticated,
      hasUser: !!user,
      hasToken: !!token,
      userId: user?._id,
      userPhone: user?.phone
    });

    // Test 2: Test auth endpoint
    try {
      const authResponse = await fetch('/api/test-auth');
      const authData = await authResponse.json();
      addResult('Auth Endpoint Test', authResponse.ok, authData);
    } catch (error) {
      addResult('Auth Endpoint Test', false, { error: error instanceof Error ? error.message : 'Unknown error' });
    }

    // Test 3: Test backend connection
    try {
      const backendResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000'}/api/test`);
      const backendData = await backendResponse.json();
      addResult('Backend Connection Test', backendResponse.ok, backendData);
    } catch (error) {
      addResult('Backend Connection Test', false, { error: error instanceof Error ? error.message : 'Unknown error' });
    }

    // Test 4: Test create order endpoint (if authenticated)
    if (isAuthenticated) {
      try {
        const orderResponse = await fetch('/api/create-order', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ amount: 100, currency: 'INR' })
        });
        const orderData = await orderResponse.json();
        addResult('Create Order Test', orderResponse.ok, orderData);
      } catch (error) {
        addResult('Create Order Test', false, { error: error instanceof Error ? error.message : 'Unknown error' });
      }
    } else {
      addResult('Create Order Test', false, { error: 'Not authenticated' });
    }

    setIsLoading(false);
  };

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <h1 className="text-2xl font-bold mb-6">Payment System Test</h1>
      
      <div className="mb-6">
        <button
          onClick={runTests}
          disabled={isLoading}
          className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded disabled:opacity-50"
        >
          {isLoading ? 'Running Tests...' : 'Run Tests'}
        </button>
      </div>

      <div className="space-y-4">
        {testResults.map((result, index) => (
          <div
            key={index}
            className={`p-4 rounded border ${
              result.success ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'
            }`}
          >
            <div className="flex items-center justify-between mb-2">
              <h3 className="font-semibold">{result.test}</h3>
              <span className={`px-2 py-1 rounded text-sm ${
                result.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
              }`}>
                {result.success ? 'PASS' : 'FAIL'}
              </span>
            </div>
            <pre className="text-sm bg-gray-100 p-2 rounded overflow-auto">
              {JSON.stringify(result.data, null, 2)}
            </pre>
            <div className="text-xs text-gray-500 mt-2">
              {result.timestamp}
            </div>
          </div>
        ))}
      </div>

      {testResults.length === 0 && !isLoading && (
        <div className="text-gray-500 text-center py-8">
          Click "Run Tests" to start testing the payment system
        </div>
      )}
    </div>
  );
}
