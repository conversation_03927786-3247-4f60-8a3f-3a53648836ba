@import "tailwindcss";

@theme {
    --container-center: true;
    --container-padding: 2rem;
    --container-screens:{2xl:1400px;};

    --color-border: hsl(var(--border));
    --color-input: hsl(var(--input));
    --color-ring: hsl(var(--ring));
    --color-background: hsl(var(--background));
    --color-foreground: hsl(var(--foreground));

    --primary-DEFAULT: hsl(var(--primary));
    --primary-foreground: hsl(var(--primary-foreground));

    --secondary-DEFAULT: hsl(var(--secondary));
    --secondary-foreground: hsl(var(--secondary-foreground));

    --destructive-DEFAULT: hsl(var(--destructive));
    --destructive-foreground: hsl(var(--destructive-foreground));

    --muted-DEFAULT: hsl(var(--muted));
    --muted-foreground: hsl(var(--muted-foreground));

    --accent-DEFAULT: hsl(var(--accent));
    --accent-foreground: hsl(var(--accent-foreground));

    --popover-foreground: hsl(var(--popover-foreground));
    --popover-DEFAULT: hsl(var(--popover));

    --card-DEFAULT: hsl(var(--card));
    --card-foreground: hsl(var(--card-foreground));

    --sidebar-DEFAULT: hsl(var(--sidebar-background));
    --sidebar-foreground: hsl(var(--sidebar-foreground));
    --sidebar-primary: hsl(var(--sidebar-primary));
    --sidebar-primary-foreground: hsl(var(--sidebar-primary-foreground));
    --sidebar-accent: hsl(var(--sidebar-accent));
    --sidebar-accent-foreground: hsl(var(--sidebar-accent-foreground));
    --sidebar-border: hsl(var(--sidebar-border));
    --sidebar-ring: hsl(var(--sidebar-ring));

    --color-bandiwala-orange: #FFA92F;
    --color-bandiwala-red: #F97316;
    --color-bandiwala-brown: #92400E;
    --color-bandiwala-green: #16A34A;
    --color-bandiwala-yellow: #FBBF24;
    --color-bandiwala-bg: #FFFBF5;
    --color-bandiwala-button: #C9801D;
    --color-bandiwala-buttonHover: #B56A00;
    --color-bandiwala-white: #FFFFFF;
    --color-bandiwala-success: #4CAF50;
    --color-bandiwala-error: #F44336;
    --color-bandiwala-dark: #1F2937;

    --radius-lg: var(--radius);
    --radius-md: calc(var(--radius) - 2px);
    --radius-sm: calc(var(--radius) - 4px);

    --font-display: "Poppins", sans-serif;
    --font-body: "Inter", sans-serif;

    @keyframes{
        accordion-down {
        from: { height: 0; };
        to: { height: var(--radix-accordion-content-height); };
        };
        accordion-up {
        from: { height: var(--radix-accordion-content-height); };
        to: { height: 0; };
        };
        fade-in {
        0%: { opacity: 0; transform: translateY(10px); };
        100%: { opacity: 1; transform: translateY(0); };
        };
        pulse-subtle {
        0%, 100%: { transform: scale(1); };
        50%: { transform: scale(1.03); };
        };
    }

    --animate-accordion-down: accordion-down 0.2s ease-out;
    --animate-accordion-up: accordion-up 0.2s ease-out;
    --animate-fade-in: fade-in 0.5s ease-out;
    --animate-pulse-subtle: pulse-subtle 3s infinite;
    }

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 20 14.3% 4.1%;

    --card: 0 0% 100%;
    --card-foreground: 20 14.3% 4.1%;

    --popover: 0 0% 100%;
    --popover-foreground: 20 14.3% 4.1%;

    --primary: 24.6 95% 53.1%;
    --primary-foreground: 60 9.1% 97.8%;

    --secondary: 60 4.8% 95.9%;
    --secondary-foreground: 24 9.8% 10%;

    --muted: 60 4.8% 95.9%;
    --muted-foreground: 25 5.3% 44.7%;

    --accent: 60 4.8% 95.9%;
    --accent-foreground: 24 9.8% 10%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 20 5.9% 90%;
    --input: 20 5.9% 90%;
    --ring: 24.6 95% 53.1%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
    }
    .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
    }
}

@layer base {

    body {
      @apply bg-bandiwala-bg text-foreground font-body;
      font-family: 'Poppins', sans-serif;
    }

    h1, h2, h3, h4, h5, h6 {
      @apply font-display font-semibold;
    }
}

@layer components {
.section-container {
    @apply max-w-7xl mx-auto px-4 sm:px-6 md:px-8 py-8 sm:py-12 md:py-16;
}

.vendor-card-hover {
    @apply transform transition-all duration-500 hover:-translate-y-2 hover:shadow-2xl;
}

.vendor-image-zoom {
    @apply transition-transform duration-500 group-hover:scale-110;
}

.gradient-overlay {
    @apply absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300;
}

.badge-floating {
    @apply bg-white/95 backdrop-blur-sm shadow-lg rounded-full;
}

.section-title {
    @apply text-2xl sm:text-3xl md:text-4xl font-bold text-gray-900 mb-3 sm:mb-4;
}

.section-subtitle {
    @apply text-gray-600 text-base sm:text-lg max-w-2xl mx-auto;
}

.card-hover {
    @apply transition-all duration-300 hover:shadow-lg hover:-translate-y-1;
}
.bandiwala-button {
    @apply bg-bandiwala-button text-white font-medium py-2 px-4 rounded-lg transition-all hover:bg-bandiwala-buttonHover focus:outline-none focus:ring-2 focus:ring-bandiwala-yellow;
  }

  .bandiwala-button-primary {
    @apply bg-bandiwala-yellow text-bandiwala-dark font-medium py-2 px-4 rounded-lg transition-all hover:bg-amber-400 focus:outline-none focus:ring-2 focus:ring-bandiwala-button;
  }

  .bandiwala-input {
    @apply border border-gray-300 rounded-lg px-4 py-2 w-full focus:outline-none focus:ring-2 focus:ring-bandiwala-yellow focus:border-transparent;
  }

  .bandiwala-card {
    @apply bg-white rounded-lg p-4 transition-shadow;
  }

  /* Line clamp utilities for text truncation */
  .line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }

  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }

  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }
}

#root {
    max-width: 1280px;
    margin: 0 auto;
    padding: 2rem;
    text-align: center;
  }

  .logo {
    height: 6em;
    padding: 1.5em;
    will-change: filter;
    transition: filter 300ms;
  }
  .logo:hover {
    filter: drop-shadow(0 0 2em #646cffaa);
  }
  .logo.react:hover {
    filter: drop-shadow(0 0 2em #61dafbaa);
  }

  @keyframes logo-spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  @media (prefers-reduced-motion: no-preference) {
    a:nth-of-type(2) .logo {
      animation: logo-spin infinite 20s linear;
    }
  }

  .card {
    padding: 2em;
  }

  .read-the-docs {
    color: #888;
  }
/* Hide the Next.js dev overlay popup by targeting its data attribute */
[data-nextjs-router-overlay] {
  display: none !important;
}

/* Additional rules to hide Next.js development overlays */
[data-next-hide],
#__next-build-watcher,
[data-next-modal],
[data-reactroot] > div[style*="position: fixed"] {
  display: none !important;
  opacity: 0 !important;
  pointer-events: none !important;
}

/* Hide all Next.js development overlays and popups */
[data-nextjs-router-overlay],
[data-nextjs-dialog-overlay],
[data-nextjs-toast],
[data-nextjs-refresh-overlay],
[id^="__nextjs"] {
  display: none !important;
  opacity: 0 !important;
  visibility: hidden !important;
  pointer-events: none !important;
  position: absolute !important;
  z-index: -9999 !important;
}


