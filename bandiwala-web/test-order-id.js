/**
 * Simple test to verify order ID extraction works correctly
 */

// Import the order utilities
const { extractOrderId, formatOrderIdForDisplay, generateTempOrderId } = require('./lib/orderUtils.ts');

// Test cases
const testCases = [
  {
    name: 'Backend response with orderNumber',
    response: {
      success: true,
      data: {
        _id: '675f1234567890abcdef1234',
        orderNumber: 'BW-20241220-143022-001',
        user: 'user123',
        items: []
      }
    },
    expected: 'BW-20241220-143022-001'
  },
  {
    name: 'Backend response with only _id',
    response: {
      success: true,
      data: {
        _id: '675f1234567890abcdef1234',
        user: 'user123',
        items: []
      }
    },
    expected: '675f1234567890abcdef1234'
  },
  {
    name: 'Response with orderNumber at root level',
    response: {
      success: true,
      orderNumber: 'BW-20241220-143022-002',
      data: {
        _id: '675f1234567890abcdef1234'
      }
    },
    expected: 'BW-20241220-143022-002'
  },
  {
    name: 'Empty response (should generate temp ID)',
    response: {},
    expected: 'TEMP-' // Should start with TEMP-
  }
];

// Run tests
console.log('Testing Order ID Extraction...\n');

testCases.forEach((testCase, index) => {
  console.log(`Test ${index + 1}: ${testCase.name}`);
  
  try {
    const result = extractOrderId(testCase.response);
    
    if (testCase.expected === 'TEMP-') {
      // For temp IDs, just check if it starts with TEMP-
      if (result.startsWith('TEMP-')) {
        console.log(`✅ PASS: Got temp ID: ${result}`);
      } else {
        console.log(`❌ FAIL: Expected temp ID starting with 'TEMP-', got: ${result}`);
      }
    } else {
      // For specific IDs, check exact match
      if (result === testCase.expected) {
        console.log(`✅ PASS: Got expected ID: ${result}`);
      } else {
        console.log(`❌ FAIL: Expected '${testCase.expected}', got '${result}'`);
      }
    }
  } catch (error) {
    console.log(`❌ ERROR: ${error.message}`);
  }
  
  console.log('');
});

// Test formatOrderIdForDisplay
console.log('Testing Order ID Display Formatting...\n');

const displayTestCases = [
  {
    input: 'BW-20241220-143022-001',
    expected: 'BW-20241220-143022-001'
  },
  {
    input: '675f1234567890abcdef1234',
    expected: '675f1234567890abcdef1234'
  },
  {
    input: 'TEMP-20241220-143022-123',
    expected: 'TEMP-20241220-143022-123'
  },
  {
    input: '',
    expected: 'Unknown'
  }
];

displayTestCases.forEach((testCase, index) => {
  console.log(`Display Test ${index + 1}: Input '${testCase.input}'`);
  
  try {
    const result = formatOrderIdForDisplay(testCase.input);
    
    if (result === testCase.expected) {
      console.log(`✅ PASS: Got expected display: ${result}`);
    } else {
      console.log(`❌ FAIL: Expected '${testCase.expected}', got '${result}'`);
    }
  } catch (error) {
    console.log(`❌ ERROR: ${error.message}`);
  }
  
  console.log('');
});

// Test generateTempOrderId
console.log('Testing Temp Order ID Generation...\n');

try {
  const tempId1 = generateTempOrderId();
  const tempId2 = generateTempOrderId();
  
  console.log(`Generated temp ID 1: ${tempId1}`);
  console.log(`Generated temp ID 2: ${tempId2}`);
  
  if (tempId1.startsWith('TEMP-') && tempId2.startsWith('TEMP-')) {
    console.log('✅ PASS: Both temp IDs start with TEMP-');
  } else {
    console.log('❌ FAIL: Temp IDs do not start with TEMP-');
  }
  
  if (tempId1 !== tempId2) {
    console.log('✅ PASS: Temp IDs are unique');
  } else {
    console.log('❌ FAIL: Temp IDs are not unique');
  }
} catch (error) {
  console.log(`❌ ERROR: ${error.message}`);
}

console.log('\nTest completed!');
