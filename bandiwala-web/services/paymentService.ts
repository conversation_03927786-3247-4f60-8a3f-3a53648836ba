import api from './api';

export interface PaymentOrderData {
  amount: number;
  currency?: string;
}

export interface PaymentVerificationData {
  razorpay_payment_id: string;
  razorpay_order_id: string;
  razorpay_signature: string;
  deliveryAddress: {
    formatted: string;
    coordinates: {
      lat: number;
      lng: number;
    };
    mapUrl?: string;
  };
  promoCode?: string;
}

export interface PaymentOrderResponse {
  success: boolean;
  message: string;
  data: {
    orderId: string;
    amount: number;
    currency: string;
    receipt: string;
    razorpayKeyId: string;
  };
}

export interface PaymentVerificationResponse {
  success: boolean;
  message: string;
  data: {
    paymentId: string;
    orderId: string;
    amount: number;
    status: string;
    method: string;
    verified: boolean;
    order: any; // The created order object
    orderNumber: string;
  };
}

export const paymentService = {
  /**
   * Create a payment order in the backend
   */
  createPaymentOrder: async (orderData: PaymentOrderData): Promise<PaymentOrderResponse> => {
    try {
      console.log('Creating payment order with data:', orderData);
      
      const response = await fetch('/api/create-order', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(orderData),
      });

      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.message || 'Failed to create payment order');
      }

      console.log('Payment order created successfully:', data);
      return data;
    } catch (error) {
      console.error('Error creating payment order:', error);
      throw error;
    }
  },

  /**
   * Verify payment and create order in the backend
   */
  verifyPaymentAndCreateOrder: async (verificationData: PaymentVerificationData): Promise<PaymentVerificationResponse> => {
    try {
      console.log('Verifying payment and creating order with data:', verificationData);
      
      const response = await fetch('/api/verify', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(verificationData),
      });

      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.message || 'Payment verification failed');
      }

      if (!data.success) {
        throw new Error(data.message || 'Payment verification failed');
      }

      console.log('Payment verified and order created successfully:', data);
      return data;
    } catch (error) {
      console.error('Error verifying payment:', error);
      throw error;
    }
  },

  /**
   * Get payment status from backend
   */
  getPaymentStatus: async (paymentId: string) => {
    try {
      console.log('Getting payment status for payment:', paymentId);
      
      // Get auth token
      let token = null;
      if (typeof window !== 'undefined') {
        token = localStorage.getItem('auth_token');
      }

      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      };
      
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000'}/api/payments/status/${paymentId}`, {
        method: 'GET',
        headers,
      });

      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.message || 'Failed to get payment status');
      }

      console.log('Payment status retrieved successfully:', data);
      return data;
    } catch (error) {
      console.error('Error getting payment status:', error);
      throw error;
    }
  },
};

export default paymentService;
