import axios from 'axios';

export const testBackendConnection = async () => {
  try {
    const response = await axios.get(`${process.env.NEXT_PUBLIC_API_URL}/health-check`);
    return {
      success: true,
      message: 'Backend connection successful',
      data: response.data
    };
  } catch (error) {
    console.error('Backend connection test failed:', error);
    return {
      success: false,
      message: 'Backend connection failed',
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
};