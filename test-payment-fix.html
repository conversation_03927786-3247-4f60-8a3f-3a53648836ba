<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Payment Amount Validation Fix</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Test Payment Amount Validation Fix</h1>
        <p>This test verifies that the payment amount validation now correctly accounts for promo codes.</p>
        
        <div class="form-group">
            <label for="token">Auth Token:</label>
            <input type="text" id="token" placeholder="Enter your auth token">
        </div>
        
        <div class="form-group">
            <label for="amount">Payment Amount (₹):</label>
            <input type="number" id="amount" value="100" step="0.01">
        </div>
        
        <div class="form-group">
            <label for="promoCode">Promo Code (optional):</label>
            <input type="text" id="promoCode" placeholder="e.g., FREEDEL">
        </div>
        
        <button onclick="testPaymentOrder()">Test Create Payment Order</button>
        <button onclick="clearResults()">Clear Results</button>
        
        <div id="result" class="result" style="display: none;"></div>
    </div>

    <script>
        function getCookie(name) {
            const value = `; ${document.cookie}`;
            const parts = value.split(`; ${name}=`);
            if (parts.length === 2) return parts.pop().split(';').shift();
        }
        
        function clearResults() {
            const resultDiv = document.getElementById('result');
            resultDiv.style.display = 'none';
            resultDiv.textContent = '';
        }
        
        async function testPaymentOrder() {
            const token = document.getElementById('token').value.trim();
            const amount = parseFloat(document.getElementById('amount').value);
            const promoCode = document.getElementById('promoCode').value.trim();
            const resultDiv = document.getElementById('result');
            
            if (!token) {
                resultDiv.className = 'result error';
                resultDiv.style.display = 'block';
                resultDiv.textContent = 'Please enter an auth token';
                return;
            }
            
            if (!amount || amount <= 0) {
                resultDiv.className = 'result error';
                resultDiv.style.display = 'block';
                resultDiv.textContent = 'Please enter a valid amount';
                return;
            }
            
            try {
                resultDiv.className = 'result info';
                resultDiv.style.display = 'block';
                resultDiv.textContent = 'Testing payment order creation...';
                
                const requestBody = { amount };
                if (promoCode) {
                    requestBody.promoCode = promoCode;
                }
                
                console.log('Request body:', requestBody);
                
                const response = await fetch('http://localhost:3111/api/create-order', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify(requestBody)
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = '✅ Payment order created successfully!\n\nAPI Response:\n' + JSON.stringify(data, null, 2);
                    
                    if (promoCode) {
                        resultDiv.textContent += '\n\n✅ Promo code was included in the request and validation passed!';
                    }
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = '❌ Payment order creation failed:\n\nAPI Error:\n' + JSON.stringify(data, null, 2);
                    
                    if (data.message && data.message.includes('Amount mismatch')) {
                        resultDiv.textContent += '\n\n❌ The amount mismatch error still exists. The fix may need more work.';
                    } else {
                        resultDiv.textContent += '\n\n✅ No amount mismatch error detected. The fix appears to be working!';
                    }
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.style.display = 'block';
                resultDiv.textContent = '❌ Network Error:\n' + error.message;
            }
        }
        
        // Auto-fill token from localStorage or cookies if available
        window.onload = function() {
            const savedToken = localStorage.getItem('authToken') || getCookie('token');
            if (savedToken) {
                document.getElementById('token').value = savedToken;
            }
        };
    </script>
</body>
</html>
